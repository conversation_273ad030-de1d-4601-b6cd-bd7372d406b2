"""Add is_deployed in Team

Revision ID: 6cc9c8805fd0
Revises: 789207e65d22
Create Date: 2025-06-15 18:25:55.147363

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "6cc9c8805fd0"
down_revision = "789207e65d22"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "teams",
        sa.<PERSON>umn(
            "is_deployed", sa.<PERSON>(), server_default=sa.text("false"), nullable=False
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("teams", "is_deployed")
    # ### end Alembic commands ###
