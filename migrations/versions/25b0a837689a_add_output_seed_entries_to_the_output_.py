"""Add output seed entries to the output table

Revision ID: 25b0a837689a
Revises: 7b8d4b45ef15
Create Date: 2025-06-22 02:01:13.823598

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB
import uuid
from datetime import datetime


# revision identifiers, used by Alembic.
revision = "25b0a837689a"
down_revision = "7b8d4b45ef15"
branch_labels = None
depends_on = None


# Default output components data
DEFAULT_OUTPUT_COMPONENTS = [
    {
        "provider": "autogen_core.io.JSONOutput",
        "component_type": "output",
        "version": 1,
        "component_version": 1,
        "description": "JSON output component for writing JSON data",
        "label": "JSONOutput",
        "config": {
            "indent": 2,
            "ensure_ascii": False,
            "sort_keys": False,
            "output_path": None,
        },
    },
    {
        "provider": "autogen_core.io.TextOutput",
        "component_type": "output",
        "version": 1,
        "component_version": 1,
        "description": "Text output component for writing text data",
        "label": "TextOutput",
        "config": {"encoding": "utf-8", "line_ending": "auto"},
    },
    {
        "provider": "autogen_core.io.MarkdownOutput",
        "component_type": "output",
        "version": 1,
        "component_version": 1,
        "description": "Markdown output component for writing Markdown data",
        "label": "MarkdownOutput",
        "config": {
            "encoding": "utf-8",
            "output_path": None,
            "include_toc": False,
            "heading_level": 1,
        },
    },
]


def upgrade() -> None:
    """
    Add default output components to the database.
    This migration will only add components that don't already exist.
    """
    # Get database connection
    connection = op.get_bind()

    # Check if outputs table exists
    inspector = sa.inspect(connection)
    if "outputs" not in inspector.get_table_names():
        print("Outputs table doesn't exist yet. Skipping seeding.")
        return

    # Create a temporary table reference for querying
    outputs_table = sa.table(
        "outputs",
        sa.column("id", sa.UUID),
        sa.column("component", JSONB),
        sa.column("created_at", sa.DateTime),
        sa.column("updated_at", sa.DateTime),
        sa.column("is_deleted", sa.Boolean),
        sa.column("organization_id", sa.UUID),
    )

    # Get current timestamp
    now = datetime.utcnow()

    for component_data in DEFAULT_OUTPUT_COMPONENTS:
        # Check if component already exists
        existing = connection.execute(
            sa.select(outputs_table.c.id).where(
                sa.and_(
                    outputs_table.c.component["provider"].astext
                    == component_data["provider"],
                    outputs_table.c.component["component_type"].astext
                    == component_data["component_type"],
                    outputs_table.c.component["label"].astext
                    == component_data["label"],
                    outputs_table.c.is_deleted.is_(False),
                )
            )
        ).first()

        if existing:
            print(
                f"Component already exists, skipping: {component_data['label']} ({component_data['provider']})"
            )
            continue

        # Insert new component
        connection.execute(
            outputs_table.insert().values(
                id=uuid.uuid4(),
                component=component_data,
                created_at=now,
                updated_at=None,
                is_deleted=False,
                organization_id=None,
            )
        )
        print(
            f"Created output component: {component_data['label']} ({component_data['provider']})"
        )


def downgrade() -> None:
    """
    Remove the seeded output components.
    This will only remove components that match our default data exactly.
    """
    # Get database connection
    connection = op.get_bind()

    # Check if outputs table exists
    inspector = sa.inspect(connection)
    if "outputs" not in inspector.get_table_names():
        print("Outputs table doesn't exist. Nothing to remove.")
        return

    # Create a temporary table reference for querying
    outputs_table = sa.table(
        "outputs",
        sa.column("id", sa.UUID),
        sa.column("component", JSONB),
        sa.column("is_deleted", sa.Boolean),
    )

    for component_data in DEFAULT_OUTPUT_COMPONENTS:
        # Find and delete matching components
        result = connection.execute(
            outputs_table.delete().where(
                sa.and_(
                    outputs_table.c.component["provider"].astext
                    == component_data["provider"],
                    outputs_table.c.component["component_type"].astext
                    == component_data["component_type"],
                    outputs_table.c.component["label"].astext
                    == component_data["label"],
                    outputs_table.c.is_deleted.is_(False),
                )
            )
        )

        if result.rowcount > 0:
            print(
                f"Removed output component: {component_data['label']} ({component_data['provider']})"
            )
        else:
            print(
                f"Component not found for removal: {component_data['label']} ({component_data['provider']})"
            )
