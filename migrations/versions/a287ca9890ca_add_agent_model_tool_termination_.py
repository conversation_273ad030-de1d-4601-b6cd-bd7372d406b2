"""Add agent, model, tool, termination_condition, team, user, organization, input, output tables

Revision ID: a287ca9890ca
Revises: 
Create Date: 2025-05-31 03:25:44.858155

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy import exc as sa_exc
from sqlalchemy.dialects.postgresql import ENUM as PGEnum

# revision identifiers, used by Alembic.
revision = "a287ca9890ca"
down_revision = None
branch_labels = None
depends_on = None

# --- define your PostgreSQL ENUM types once, disable inline CREATE ---
organizationstatus = PGEnum(
    "ACTIVE",
    "INACTIVE",
    "PENDING",
    "ARCHIVED",
    "DELETED",
    name="organizationstatus",
    create_type=False,
)
usertype = PGEnum("ADMIN", "NORMAL", name="usertype", create_type=False)
userstatus = PGEnum(
    "ACTIVE",
    "INACTIVE",
    "PENDING",
    "ARCHIVED",
    "DELETED",
    name="userstatus",
    create_type=False,
)


def upgrade():
    bind = op.get_bind()
    # Create ENUM types if they don’t exist yet
    try:
        organizationstatus.create(bind, checkfirst=True)
    except sa_exc.ProgrammingError:
        pass
    try:
        usertype.create(bind, checkfirst=True)
    except sa_exc.ProgrammingError:
        pass
    try:
        userstatus.create(bind, checkfirst=True)
    except sa_exc.ProgrammingError:
        pass

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "agents",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("component", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_agents_id"), "agents", ["id"], unique=False)
    op.create_index(
        op.f("ix_agents_is_deleted"), "agents", ["is_deleted"], unique=False
    )

    op.create_table(
        "inputs",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("component", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_inputs_id"), "inputs", ["id"], unique=False)
    op.create_index(
        op.f("ix_inputs_is_deleted"), "inputs", ["is_deleted"], unique=False
    )

    op.create_table(
        "models",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("component", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_models_id"), "models", ["id"], unique=False)
    op.create_index(
        op.f("ix_models_is_deleted"), "models", ["is_deleted"], unique=False
    )

    op.create_table(
        "organizations",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("status", organizationstatus, nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_organizations_id"), "organizations", ["id"], unique=False)
    op.create_index(
        op.f("ix_organizations_is_deleted"),
        "organizations",
        ["is_deleted"],
        unique=False,
    )
    op.create_index(
        op.f("ix_organizations_name"), "organizations", ["name"], unique=False
    )
    op.create_index(
        op.f("ix_organizations_status"), "organizations", ["status"], unique=False
    )

    op.create_table(
        "outputs",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("component", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_outputs_id"), "outputs", ["id"], unique=False)
    op.create_index(
        op.f("ix_outputs_is_deleted"), "outputs", ["is_deleted"], unique=False
    )

    op.create_table(
        "teams",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("component", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_teams_id"), "teams", ["id"], unique=False)
    op.create_index(op.f("ix_teams_is_deleted"), "teams", ["is_deleted"], unique=False)

    op.create_table(
        "terminationconditions",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("component", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_terminationconditions_id"),
        "terminationconditions",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_terminationconditions_is_deleted"),
        "terminationconditions",
        ["is_deleted"],
        unique=False,
    )

    op.create_table(
        "tools",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("component", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_tools_id"), "tools", ["id"], unique=False)
    op.create_index(op.f("ix_tools_is_deleted"), "tools", ["is_deleted"], unique=False)

    op.create_table(
        "users",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("bio", sa.Text(), nullable=True),
        sa.Column("email", sa.String(length=255), nullable=False),
        sa.Column("password", sa.String(length=255), nullable=False),
        sa.Column("role", usertype, nullable=False),
        sa.Column("status", userstatus, nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("organization_id", sa.UUID(), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(["organization_id"], ["organizations.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_users_email"), "users", ["email"], unique=True)
    op.create_index(op.f("ix_users_id"), "users", ["id"], unique=False)
    op.create_index(op.f("ix_users_is_deleted"), "users", ["is_deleted"], unique=False)
    op.create_index(op.f("ix_users_name"), "users", ["name"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_users_name"), table_name="users")
    op.drop_index(op.f("ix_users_is_deleted"), table_name="users")
    op.drop_index(op.f("ix_users_id"), table_name="users")
    op.drop_index(op.f("ix_users_email"), table_name="users")
    op.drop_table("users")

    op.drop_index(op.f("ix_tools_is_deleted"), table_name="tools")
    op.drop_index(op.f("ix_tools_id"), table_name="tools")
    op.drop_table("tools")

    op.drop_index(
        op.f("ix_terminationconditions_is_deleted"), table_name="terminationconditions"
    )
    op.drop_index(
        op.f("ix_terminationconditions_id"), table_name="terminationconditions"
    )
    op.drop_table("terminationconditions")

    op.drop_index(op.f("ix_teams_is_deleted"), table_name="teams")
    op.drop_index(op.f("ix_teams_id"), table_name="teams")
    op.drop_table("teams")

    op.drop_index(op.f("ix_outputs_is_deleted"), table_name="outputs")
    op.drop_index(op.f("ix_outputs_id"), table_name="outputs")
    op.drop_table("outputs")

    op.drop_index(op.f("ix_organizations_status"), table_name="organizations")
    op.drop_index(op.f("ix_organizations_name"), table_name="organizations")
    op.drop_index(op.f("ix_organizations_is_deleted"), table_name="organizations")
    op.drop_index(op.f("ix_organizations_id"), table_name="organizations")
    op.drop_table("organizations")

    op.drop_index(op.f("ix_models_is_deleted"), table_name="models")
    op.drop_index(op.f("ix_models_id"), table_name="models")
    op.drop_table("models")

    op.drop_index(op.f("ix_inputs_is_deleted"), table_name="inputs")
    op.drop_index(op.f("ix_inputs_id"), table_name="inputs")
    op.drop_table("inputs")

    op.drop_index(op.f("ix_agents_is_deleted"), table_name="agents")
    op.drop_index(op.f("ix_agents_id"), table_name="agents")
    op.drop_table("agents")
    # ### end Alembic commands ###

    # Drop ENUM types, ignoring errors if already dropped
    bind = op.get_bind()
    try:
        userstatus.drop(bind, checkfirst=True)
    except sa_exc.ProgrammingError:
        pass
    try:
        usertype.drop(bind, checkfirst=True)
    except sa_exc.ProgrammingError:
        pass
    try:
        organizationstatus.drop(bind, checkfirst=True)
    except sa_exc.ProgrammingError:
        pass
