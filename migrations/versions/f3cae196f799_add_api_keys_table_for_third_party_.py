"""Add API keys table for third-party access

Revision ID: f3cae196f799
Revises: 25b0a837689a
Create Date: 2025-06-25 18:52:44.187489

"""
from alembic import op
import sqlalchemy as sa

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "f3cae196f799"
down_revision = "25b0a837689a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "apikeys",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column(
            "key_prefix",
            sa.String(length=10),
            nullable=False,
            comment="First few chars for identification",
        ),
        sa.Column(
            "key_hash",
            sa.String(length=255),
            nullable=False,
            comment="Hashed version of the full key",
        ),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("organization_id", sa.UUID(), nullable=False),
        sa.Column(
            "scopes",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            comment="List of allowed scopes",
        ),
        sa.Column(
            "rate_limit_per_minute",
            sa.Integer(),
            nullable=True,
            comment="API calls per minute limit",
        ),
        sa.Column(
            "rate_limit_per_hour",
            sa.Integer(),
            nullable=True,
            comment="API calls per hour limit",
        ),
        sa.Column(
            "rate_limit_per_day",
            sa.Integer(),
            nullable=True,
            comment="API calls per day limit",
        ),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column(
            "expires_at",
            sa.DateTime(),
            nullable=True,
            comment="Optional expiration date",
        ),
        sa.Column("last_used_at", sa.DateTime(), nullable=True),
        sa.Column("total_requests", sa.Integer(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("created_by", sa.UUID(), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("key_hash"),
    )
    op.create_index(op.f("ix_apikeys_id"), "apikeys", ["id"], unique=False)
    op.create_index(
        op.f("ix_apikeys_is_deleted"), "apikeys", ["is_deleted"], unique=False
    )
    op.create_index(
        op.f("ix_apikeys_key_prefix"), "apikeys", ["key_prefix"], unique=False
    )
    op.create_index(op.f("ix_apikeys_name"), "apikeys", ["name"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_apikeys_name"), table_name="apikeys")
    op.drop_index(op.f("ix_apikeys_key_prefix"), table_name="apikeys")
    op.drop_index(op.f("ix_apikeys_is_deleted"), table_name="apikeys")
    op.drop_index(op.f("ix_apikeys_id"), table_name="apikeys")
    op.drop_table("apikeys")
    # ### end Alembic commands ###
