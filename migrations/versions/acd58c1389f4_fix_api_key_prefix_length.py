"""fix_api_key_prefix_length

Revision ID: acd58c1389f4
Revises: 4d00a289165d
Create Date: 2025-06-26 01:27:18.381546

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "acd58c1389f4"
down_revision = "4d00a289165d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "apikeys",
        "key_prefix",
        existing_type=sa.VARCHAR(length=10),
        type_=sa.String(length=20),
        existing_comment="First few chars for identification",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "apikeys",
        "key_prefix",
        existing_type=sa.String(length=20),
        type_=sa.VARCHAR(length=10),
        existing_comment="First few chars for identification",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
