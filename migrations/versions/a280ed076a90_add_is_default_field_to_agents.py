"""add_is_default_field_to_agents

Revision ID: a280ed076a90
Revises: 28867f955654
Create Date: 2025-07-11 20:16:07.830087

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "a280ed076a90"
down_revision = "28867f955654"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add the column as nullable first
    op.add_column("agents", sa.Column("is_default", sa.<PERSON>(), nullable=True))

    # Set default value for existing records (all existing agents are not default)
    op.execute("UPDATE agents SET is_default = false WHERE is_default IS NULL")

    # Now make the column non-nullable
    op.alter_column("agents", "is_default", nullable=False)

    # Create the index
    op.create_index(
        op.f("ix_agents_is_default"), "agents", ["is_default"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_agents_is_default"), table_name="agents")
    op.drop_column("agents", "is_default")
    # ### end Alembic commands ###
