"""Add relationship of organization with agent, model, tool, team, input, output

Revision ID: c2540dd0756f
Revises: a287ca9890ca
Create Date: 2025-06-02 16:54:58.968556
"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID  # For PostgreSQL-specific UUID support

# revision identifiers, used by Alembic.
revision = "c2540dd0756f"
down_revision = "a287ca9890ca"
branch_labels = None
depends_on = None


def upgrade():
    # Add organization_id column and foreign key to each table

    op.add_column("agents", sa.Column("organization_id", UUID(), nullable=True))
    op.create_foreign_key(
        "fk_agents_organization_id",
        "agents",
        "organizations",
        ["organization_id"],
        ["id"],
    )

    op.add_column("inputs", sa.Column("organization_id", UUID(), nullable=True))
    op.create_foreign_key(
        "fk_inputs_organization_id",
        "inputs",
        "organizations",
        ["organization_id"],
        ["id"],
    )

    op.add_column("models", sa.Column("organization_id", UUID(), nullable=True))
    op.create_foreign_key(
        "fk_models_organization_id",
        "models",
        "organizations",
        ["organization_id"],
        ["id"],
    )

    op.add_column("outputs", sa.Column("organization_id", UUID(), nullable=True))
    op.create_foreign_key(
        "fk_outputs_organization_id",
        "outputs",
        "organizations",
        ["organization_id"],
        ["id"],
    )

    op.add_column("teams", sa.Column("organization_id", UUID(), nullable=True))
    op.create_foreign_key(
        "fk_teams_organization_id",
        "teams",
        "organizations",
        ["organization_id"],
        ["id"],
    )

    op.add_column("tools", sa.Column("organization_id", UUID(), nullable=True))
    op.create_foreign_key(
        "fk_tools_organization_id",
        "tools",
        "organizations",
        ["organization_id"],
        ["id"],
    )


def downgrade():
    # Drop foreign keys and columns in reverse order

    op.drop_constraint("fk_tools_organization_id", "tools", type_="foreignkey")
    op.drop_column("tools", "organization_id")

    op.drop_constraint("fk_teams_organization_id", "teams", type_="foreignkey")
    op.drop_column("teams", "organization_id")

    op.drop_constraint("fk_outputs_organization_id", "outputs", type_="foreignkey")
    op.drop_column("outputs", "organization_id")

    op.drop_constraint("fk_models_organization_id", "models", type_="foreignkey")
    op.drop_column("models", "organization_id")

    op.drop_constraint("fk_inputs_organization_id", "inputs", type_="foreignkey")
    op.drop_column("inputs", "organization_id")

    op.drop_constraint("fk_agents_organization_id", "agents", type_="foreignkey")
    op.drop_column("agents", "organization_id")
