"""Add rate_limit and scope, usages fields

Revision ID: e1bae993b30e
Revises: acd58c1389f4
Create Date: 2025-06-26 19:26:52.440719

"""
from alembic import op
import sqlalchemy as sa

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "e1bae993b30e"
down_revision = "acd58c1389f4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column(
            "scopes",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default='["*"]',
            comment="List of allowed scopes for this user",
        ),
    )
    op.add_column(
        "users",
        sa.Column(
            "rate_limit_per_minute",
            sa.Integer(),
            nullable=False,
            server_default="100",
            comment="API calls per minute limit for this user",
        ),
    )
    op.add_column(
        "users",
        sa.Column(
            "rate_limit_per_hour",
            sa.Integer(),
            nullable=False,
            server_default="1000",
            comment="API calls per hour limit for this user",
        ),
    )
    op.add_column(
        "users",
        sa.Column(
            "rate_limit_per_day",
            sa.Integer(),
            nullable=True,
            server_default="10000",
            comment="API calls per day limit for this user",
        ),
    )
    op.add_column(
        "users", sa.Column("last_used_at", sa.DateTime(timezone=True), nullable=True)
    )
    op.add_column(
        "users",
        sa.Column("total_requests", sa.Integer(), nullable=False, server_default="0"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "total_requests")
    op.drop_column("users", "last_used_at")
    op.drop_column("users", "rate_limit_per_day")
    op.drop_column("users", "rate_limit_per_hour")
    op.drop_column("users", "rate_limit_per_minute")
    op.drop_column("users", "scopes")
    # ### end Alembic commands ###
