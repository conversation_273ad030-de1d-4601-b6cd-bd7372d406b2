"""Update to run model

Revision ID: 4bc275faef67
Revises: d801e8e95686
Create Date: 2025-06-18 05:10:04.926673

"""
from alembic import op
import sqlalchemy as sa

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "4bc275faef67"
down_revision = "d801e8e95686"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "runs",
        "task",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "runs",
        "task",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        nullable=False,
    )
    # ### end Alembic commands ###
