"""Remove messages field from Run Table

Revision ID: d801e8e95686
Revises: 23496b163632
Create Date: 2025-06-18 03:22:13.328042

"""
from alembic import op
import sqlalchemy as sa

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "d801e8e95686"
down_revision = "23496b163632"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("runs", "messages")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "runs",
        sa.Column(
            "messages",
            postgresql.ARRAY(postgresql.JSONB(astext_type=sa.Text())),
            autoincrement=False,
            nullable=True,
        ),
    )
    # ### end Alembic commands ###
