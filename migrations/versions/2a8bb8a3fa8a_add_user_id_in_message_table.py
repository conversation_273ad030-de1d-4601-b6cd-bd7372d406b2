"""Add user_id in message table

Revision ID: 2a8bb8a3fa8a
Revises: 4bc275faef67
Create Date: 2025-06-19 13:05:55.866505

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "2a8bb8a3fa8a"
down_revision = "4bc275faef67"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("messages", sa.Column("user_id", sa.UUID(), nullable=True))
    op.create_foreign_key(
        "fk_messages_user_id",
        "messages",
        "users",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("fk_messages_user_id", "messages", type_="foreignkey")
    op.drop_column("messages", "user_id")
    # ### end Alembic commands ###
