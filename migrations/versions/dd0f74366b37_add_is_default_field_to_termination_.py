"""add_is_default_field_to_termination_conditions

Revision ID: dd0f74366b37
Revises: 20d7173d5ed1
Create Date: 2025-07-14 13:00:58.269581

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "dd0f74366b37"
down_revision = "20d7173d5ed1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add the column as nullable first
    op.add_column(
        "terminationconditions", sa.Column("is_default", sa.<PERSON>(), nullable=True)
    )

    # Set default value for existing records (all existing termination conditions are not default)
    op.execute(
        "UPDATE terminationconditions SET is_default = false WHERE is_default IS NULL"
    )

    # Now make the column non-nullable
    op.alter_column("terminationconditions", "is_default", nullable=False)

    # Create the index
    op.create_index(
        op.f("ix_terminationconditions_is_default"),
        "terminationconditions",
        ["is_default"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_terminationconditions_is_default"), table_name="terminationconditions"
    )
    op.drop_column("terminationconditions", "is_default")
    # ### end Alembic commands ###
