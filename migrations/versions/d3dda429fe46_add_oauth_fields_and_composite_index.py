"""Add oauth fields and composite index

Revision ID: d3dda429fe46
Revises: 2fb88df67296
Create Date: 2025-06-28 02:05:17.149942

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "d3dda429fe46"
down_revision = "2fb88df67296"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users", sa.Column("oauth_provider", sa.String(length=255), nullable=True)
    )
    op.add_column("users", sa.Column("oauth_id", sa.String(length=255), nullable=True))
    op.alter_column(
        "users", "password", existing_type=sa.VARCHAR(length=255), nullable=True
    )
    op.alter_column(
        "users",
        "rate_limit_per_minute",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_comment="API calls per minute limit for this user",
        existing_server_default=sa.text("100"),
    )
    op.alter_column(
        "users",
        "rate_limit_per_hour",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_comment="API calls per hour limit for this user",
        existing_server_default=sa.text("1000"),
    )
    op.alter_column(
        "users",
        "total_requests",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_server_default=sa.text("0"),
    )
    op.create_index(
        "idx_users_oauth_provider_id",
        "users",
        ["oauth_provider", "oauth_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_users_oauth_provider_id", table_name="users")
    op.alter_column(
        "users",
        "total_requests",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_server_default=sa.text("0"),
    )
    op.alter_column(
        "users",
        "rate_limit_per_hour",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_comment="API calls per hour limit for this user",
        existing_server_default=sa.text("1000"),
    )
    op.alter_column(
        "users",
        "rate_limit_per_minute",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_comment="API calls per minute limit for this user",
        existing_server_default=sa.text("100"),
    )
    op.alter_column(
        "users", "password", existing_type=sa.VARCHAR(length=255), nullable=False
    )
    op.drop_column("users", "oauth_id")
    op.drop_column("users", "oauth_provider")
    # ### end Alembic commands ###
