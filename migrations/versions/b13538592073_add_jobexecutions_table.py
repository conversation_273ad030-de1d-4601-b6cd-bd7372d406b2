"""Add JobExecutions Table

Revision ID: b13538592073
Revises: d3dda429fe46
Create Date: 2025-06-30 18:44:18.520860

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "b13538592073"
down_revision = "d3dda429fe46"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "jobexecutions",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("run_id", sa.UUID(), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=True),
        sa.Column(
            "completed_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_deleted", sa.<PERSON>(), nullable=False),
        sa.ForeignKeyConstraint(
            ["run_id"],
            ["runs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_jobexecutions_id"), "jobexecutions", ["id"], unique=False)
    op.create_index(
        op.f("ix_jobexecutions_is_deleted"),
        "jobexecutions",
        ["is_deleted"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_jobexecutions_is_deleted"), table_name="jobexecutions")
    op.drop_index(op.f("ix_jobexecutions_id"), table_name="jobexecutions")
    op.drop_table("jobexecutions")
    # ### end Alembic commands ###
