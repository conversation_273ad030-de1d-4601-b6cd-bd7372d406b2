"""Add configs in folder table

Revision ID: eb18744733da
Revises: 95541a5db545
Create Date: 2025-07-18 17:05:41.720909

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "eb18744733da"
down_revision = "95541a5db545"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("folders", sa.Column("chunking_config", sa.JSON(), nullable=True))
    op.add_column("folders", sa.Column("embedding_config", sa.JSON(), nullable=True))
    op.add_column("folders", sa.Column("vector_db_config", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("folders", "vector_db_config")
    op.drop_column("folders", "embedding_config")
    op.drop_column("folders", "chunking_config")
    # ### end Alembic commands ###
