"""Fix API Key user reference in created_by

Revision ID: 4d00a289165d
Revises: f3cae196f799
Create Date: 2025-06-25 20:23:57.500768

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = "4d00a289165d"
down_revision = "f3cae196f799"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("apikeys_created_by_fkey"), "apikeys", type_="foreignkey")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        op.f("apikeys_created_by_fkey"), "apikeys", "users", ["created_by"], ["id"]
    )
    # ### end Alembic commands ###
