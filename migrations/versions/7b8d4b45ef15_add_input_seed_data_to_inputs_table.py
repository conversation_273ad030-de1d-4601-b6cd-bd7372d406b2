"""Add Input seed data to Inputs Table

Revision ID: 7b8d4b45ef15
Revises: 2a8bb8a3fa8a
Create Date: 2025-06-22 01:23:06.226511

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB
import uuid
from datetime import datetime


# revision identifiers, used by Alembic.
revision = "7b8d4b45ef15"
down_revision = "2a8bb8a3fa8a"
branch_labels = None
depends_on = None

# Default input components data
DEFAULT_INPUT_COMPONENTS = [
    {
        "provider": "autogen_core.io.FileInput",
        "component_type": "input",
        "version": 1,
        "component_version": 1,
        "description": "File input component for reading various file types",
        "label": "FileInput",
        "config": {"encoding": "utf-8", "file_type": "txt"},
    },
    {
        "provider": "autogen_core.io.URLInput",
        "component_type": "input",
        "version": 1,
        "component_version": 1,
        "description": "URL input component for fetching data from APIs",
        "label": "URLInput",
        "config": {
            "url": "https://api.example.com/data",
            "headers": {"Authorization": "Bearer token"},
            "timeout": 30,
            "verify_ssl": True,
        },
    },
    {
        "provider": "autogen_core.io.TextInput",
        "component_type": "input",
        "version": 1,
        "component_version": 1,
        "description": "Text input component for direct text input",
        "label": "TextInput",
        "config": {"content": "Hello, world!", "encoding": "utf-8"},
    },
    {
        "provider": "autogen_core.io.ImageInput",
        "component_type": "input",
        "version": 1,
        "component_version": 1,
        "description": "Image input component for handling image data",
        "label": "ImageInput",
        "config": {"image_type": "png", "encoding": "utf-8"},
    },
]


def upgrade() -> None:
    """
    Add default input components to the database.
    This migration will only add components that don't already exist.
    """
    # Get database connection
    connection = op.get_bind()

    # Check if inputs table exists
    inspector = sa.inspect(connection)
    if "inputs" not in inspector.get_table_names():
        print("Inputs table doesn't exist yet. Skipping seeding.")
        return

    # Create a temporary table reference for querying
    inputs_table = sa.table(
        "inputs",
        sa.column("id", sa.UUID),
        sa.column("component", JSONB),
        sa.column("created_at", sa.DateTime),
        sa.column("updated_at", sa.DateTime),
        sa.column("is_deleted", sa.Boolean),
        sa.column("organization_id", sa.UUID),
    )

    # Get current timestamp
    now = datetime.utcnow()

    for component_data in DEFAULT_INPUT_COMPONENTS:
        # Check if component already exists
        existing = connection.execute(
            sa.select(inputs_table.c.id).where(
                sa.and_(
                    inputs_table.c.component["provider"].astext
                    == component_data["provider"],
                    inputs_table.c.component["component_type"].astext
                    == component_data["component_type"],
                    inputs_table.c.component["label"].astext == component_data["label"],
                    inputs_table.c.is_deleted.is_(False),
                )
            )
        ).first()

        if existing:
            print(
                f"Component already exists, skipping: {component_data['label']} ({component_data['provider']})"
            )
            continue

        # Insert new component
        connection.execute(
            inputs_table.insert().values(
                id=uuid.uuid4(),
                component=component_data,
                created_at=now,
                updated_at=None,
                is_deleted=False,
                organization_id=None,
            )
        )
        print(
            f"Created input component: {component_data['label']} ({component_data['provider']})"
        )


def downgrade() -> None:
    """
    Remove the seeded input components.
    This will only remove components that match our default data exactly.
    """
    # Get database connection
    connection = op.get_bind()

    # Check if inputs table exists
    inspector = sa.inspect(connection)
    if "inputs" not in inspector.get_table_names():
        print("Inputs table doesn't exist. Nothing to remove.")
        return

    # Create a temporary table reference for querying
    inputs_table = sa.table(
        "inputs",
        sa.column("id", sa.UUID),
        sa.column("component", JSONB),
        sa.column("is_deleted", sa.Boolean),
    )

    for component_data in DEFAULT_INPUT_COMPONENTS:
        # Find and delete matching components
        result = connection.execute(
            inputs_table.delete().where(
                sa.and_(
                    inputs_table.c.component["provider"].astext
                    == component_data["provider"],
                    inputs_table.c.component["component_type"].astext
                    == component_data["component_type"],
                    inputs_table.c.component["label"].astext == component_data["label"],
                    inputs_table.c.is_deleted.is_(False),
                )
            )
        )

        if result.rowcount > 0:
            print(
                f"Removed input component: {component_data['label']} ({component_data['provider']})"
            )
        else:
            print(
                f"Component not found for removal: {component_data['label']} ({component_data['provider']})"
            )
