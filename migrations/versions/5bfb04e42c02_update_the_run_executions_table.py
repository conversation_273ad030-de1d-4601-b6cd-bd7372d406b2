"""Update the run_executions table

Revision ID: 5bfb04e42c02
Revises: b13538592073
Create Date: 2025-07-01 12:59:53.469062

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "5bfb04e42c02"
down_revision = "b13538592073"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "jobexecutions",
        sa.Column("lambda_request_id", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "jobexecutions", sa.Column("s3_input_key", sa.String(length=500), nullable=True)
    )
    op.add_column(
        "jobexecutions",
        sa.Column("s3_output_key", sa.String(length=500), nullable=True),
    )
    op.add_column(
        "jobexecutions",
        sa.Column("sqs_message_id", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "jobexecutions", sa.Column("progress_percentage", sa.Float(), nullable=True)
    )
    op.add_column(
        "jobexecutions", sa.Column("current_step", sa.String(length=255), nullable=True)
    )
    op.add_column("jobexecutions", sa.Column("result_data", sa.Text(), nullable=True))
    op.add_column("jobexecutions", sa.Column("error_message", sa.Text(), nullable=True))
    op.add_column(
        "jobexecutions",
        sa.Column(
            "queued_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
    )
    op.add_column(
        "jobexecutions",
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("jobexecutions", "started_at")
    op.drop_column("jobexecutions", "queued_at")
    op.drop_column("jobexecutions", "error_message")
    op.drop_column("jobexecutions", "result_data")
    op.drop_column("jobexecutions", "current_step")
    op.drop_column("jobexecutions", "progress_percentage")
    op.drop_column("jobexecutions", "sqs_message_id")
    op.drop_column("jobexecutions", "s3_output_key")
    op.drop_column("jobexecutions", "s3_input_key")
    op.drop_column("jobexecutions", "lambda_request_id")
    # ### end Alembic commands ###
