"""add_default_termination_conditions_for_users

Revision ID: 3adde1b0cd6f
Revises: acd58c1389f4
Create Date: 2025-06-27 12:27:58.734988

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB
import uuid
from datetime import datetime


# revision identifiers, used by Alembic.
revision = "3adde1b0cd6f"
down_revision = "acd58c1389f4"
branch_labels = None
depends_on = None


# Default termination condition components that should be available for all users
DEFAULT_TERMINATION_CONDITIONS = [
    {
        "provider": "autogen_agentchat.conditions.MaxMessageTermination",
        "component_type": "termination",
        "version": 1,
        "component_version": 1,
        "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
        "label": "MaxMessageTermination",
        "config": {"max_messages": 10, "include_agent_event": False},
    },
    {
        "provider": "autogen_agentchat.conditions.TextMentionTermination",
        "component_type": "termination",
        "version": 1,
        "component_version": 1,
        "description": "Terminate the conversation when a specific text is mentioned.",
        "label": "TextMentionTermination",
        "config": {"text": "TERMINATE", "case_sensitive": False},
    },
    {
        "provider": "autogen_agentchat.conditions.TokenUsageTermination",
        "component_type": "termination",
        "version": 1,
        "component_version": 1,
        "description": "Terminate the conversation when token usage exceeds a threshold.",
        "label": "TokenUsageTermination",
        "config": {
            "max_total_token": 1000,
            "max_prompt_token": 500,
            "max_completion_token": 500,
        },
    },
    {
        "provider": "autogen_agentchat.conditions.TimeoutTermination",
        "component_type": "termination",
        "version": 1,
        "component_version": 1,
        "description": "Terminate the conversation after a timeout period.",
        "label": "TimeoutTermination",
        "config": {"timeout": 300},
    },
]


def upgrade() -> None:
    """
    Add default termination conditions for all users.
    This migration will create default termination conditions that are accessible to all users.
    """
    # Get database connection
    connection = op.get_bind()

    # Check if terminationconditions table exists
    inspector = sa.inspect(connection)
    if "terminationconditions" not in inspector.get_table_names():
        print("TerminationConditions table doesn't exist yet. Skipping seeding.")
        return

    # Check if users table exists
    if "users" not in inspector.get_table_names():
        print("Users table doesn't exist yet. Skipping seeding.")
        return

    # Create temporary table references for querying
    terminationconditions_table = sa.table(
        "terminationconditions",
        sa.column("id", sa.UUID),
        sa.column("component", JSONB),
        sa.column("created_at", sa.DateTime),
        sa.column("updated_at", sa.DateTime),
        sa.column("is_deleted", sa.Boolean),
        sa.column("organization_id", sa.UUID),
    )

    users_table = sa.table(
        "users",
        sa.column("id", sa.UUID),
        sa.column("organization_id", sa.UUID),
        sa.column("is_deleted", sa.Boolean),
    )

    # Get current timestamp
    now = datetime.utcnow()

    # Get all active users and their organizations
    users = connection.execute(
        sa.select(users_table.c.id, users_table.c.organization_id).where(
            users_table.c.is_deleted.is_(False)
        )
    ).fetchall()

    # Get unique organization IDs (including None for users without organizations)
    organizations = set()
    for user in users:
        organizations.add(user.organization_id)

    print(f"Found {len(users)} active users across {len(organizations)} organizations")

    # Create default termination conditions for each organization
    for org_id in organizations:
        org_display = (
            f"organization {org_id}" if org_id else "users without organization"
        )
        print(f"Creating default termination conditions for {org_display}")

        for component_data in DEFAULT_TERMINATION_CONDITIONS:
            # Check if this termination condition already exists for this organization
            existing = connection.execute(
                sa.select(terminationconditions_table.c.id).where(
                    sa.and_(
                        terminationconditions_table.c.component["provider"].astext
                        == component_data["provider"],
                        terminationconditions_table.c.component["component_type"].astext
                        == component_data["component_type"],
                        terminationconditions_table.c.component["label"].astext
                        == component_data["label"],
                        terminationconditions_table.c.organization_id == org_id,
                        terminationconditions_table.c.is_deleted.is_(False),
                    )
                )
            ).first()

            if existing:
                print(
                    f"  Component already exists for {org_display}, skipping: {component_data['label']}"
                )
                continue

            # Insert new termination condition for this organization
            new_id = uuid.uuid4()
            connection.execute(
                terminationconditions_table.insert().values(
                    id=new_id,
                    component=component_data,
                    created_at=now,
                    updated_at=None,
                    is_deleted=False,
                    organization_id=org_id,
                )
            )
            print(
                f"  Created termination condition for {org_display}: {component_data['label']}"
            )

    print("Default termination conditions migration completed successfully!")


def downgrade() -> None:
    """
    Remove the default termination conditions that were added.
    This will only remove termination conditions that match our default data exactly.
    """
    # Get database connection
    connection = op.get_bind()

    # Check if terminationconditions table exists
    inspector = sa.inspect(connection)
    if "terminationconditions" not in inspector.get_table_names():
        print("TerminationConditions table doesn't exist. Nothing to remove.")
        return

    # Create a temporary table reference for querying
    terminationconditions_table = sa.table(
        "terminationconditions",
        sa.column("id", sa.UUID),
        sa.column("component", JSONB),
        sa.column("is_deleted", sa.Boolean),
        sa.column("organization_id", sa.UUID),
    )

    total_removed = 0
    for component_data in DEFAULT_TERMINATION_CONDITIONS:
        # Find and delete matching termination conditions
        result = connection.execute(
            terminationconditions_table.delete().where(
                sa.and_(
                    terminationconditions_table.c.component["provider"].astext
                    == component_data["provider"],
                    terminationconditions_table.c.component["component_type"].astext
                    == component_data["component_type"],
                    terminationconditions_table.c.component["label"].astext
                    == component_data["label"],
                    terminationconditions_table.c.is_deleted.is_(False),
                )
            )
        )

        if result.rowcount > 0:
            total_removed += result.rowcount
            print(
                f"Removed {result.rowcount} instances of termination condition: {component_data['label']}"
            )
        else:
            print(f"No instances found for removal: {component_data['label']}")

    print(f"Removed {total_removed} default termination conditions in total")
