"""Remove job_execution_table and add ratelimiting fields in organization

Revision ID: 713384359bcc
Revises: 5bfb04e42c02
Create Date: 2025-07-05 21:31:35.320658

"""
from alembic import op
import sqlalchemy as sa

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "713384359bcc"
down_revision = "5bfb04e42c02"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_jobexecutions_id"), table_name="jobexecutions")
    op.drop_index(op.f("ix_jobexecutions_is_deleted"), table_name="jobexecutions")
    op.drop_table("jobexecutions")
    op.add_column(
        "organizations",
        sa.Column(
            "rate_limit_per_minute",
            sa.Integer(),
            server_default=sa.text("100"),
            nullable=True,
            comment="API calls per minute limit for this user",
        ),
    )
    op.add_column(
        "organizations",
        sa.Column(
            "rate_limit_per_hour",
            sa.Integer(),
            server_default=sa.text("5000"),
            nullable=True,
            comment="API calls per hour limit for this user",
        ),
    )
    op.add_column(
        "organizations",
        sa.Column(
            "rate_limit_per_day",
            sa.Integer(),
            server_default=sa.text("50000"),
            nullable=True,
            comment="API calls per day limit for this user",
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("organizations", "rate_limit_per_day")
    op.drop_column("organizations", "rate_limit_per_hour")
    op.drop_column("organizations", "rate_limit_per_minute")
    op.create_table(
        "jobexecutions",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("run_id", sa.UUID(), autoincrement=False, nullable=True),
        sa.Column("status", sa.VARCHAR(length=50), autoincrement=False, nullable=True),
        sa.Column(
            "completed_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("is_deleted", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column(
            "lambda_request_id",
            sa.VARCHAR(length=255),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "s3_input_key", sa.VARCHAR(length=500), autoincrement=False, nullable=True
        ),
        sa.Column(
            "s3_output_key", sa.VARCHAR(length=500), autoincrement=False, nullable=True
        ),
        sa.Column(
            "sqs_message_id", sa.VARCHAR(length=255), autoincrement=False, nullable=True
        ),
        sa.Column(
            "progress_percentage",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "current_step", sa.VARCHAR(length=255), autoincrement=False, nullable=True
        ),
        sa.Column("result_data", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("error_message", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "queued_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "started_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["run_id"], ["runs.id"], name=op.f("jobexecutions_run_id_fkey")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("jobexecutions_pkey")),
    )
    op.create_index(
        op.f("ix_jobexecutions_is_deleted"),
        "jobexecutions",
        ["is_deleted"],
        unique=False,
    )
    op.create_index(op.f("ix_jobexecutions_id"), "jobexecutions", ["id"], unique=False)
    # ### end Alembic commands ###
