"""add_is_default_field_to_tools

Revision ID: 28867f955654
Revises: 2b74b743d0dd
Create Date: 2025-07-11 00:02:05.074564

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "28867f955654"
down_revision = "2b74b743d0dd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add the column as nullable first
    op.add_column("tools", sa.Column("is_default", sa.<PERSON>(), nullable=True))

    # Set default value for existing records (all existing tools are not default)
    op.execute("UPDATE tools SET is_default = false WHERE is_default IS NULL")

    # Now make the column non-nullable
    op.alter_column("tools", "is_default", nullable=False)

    # Create the index
    op.create_index(op.f("ix_tools_is_default"), "tools", ["is_default"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_tools_is_default"), table_name="tools")
    op.drop_column("tools", "is_default")
    # ### end Alembic commands ###
