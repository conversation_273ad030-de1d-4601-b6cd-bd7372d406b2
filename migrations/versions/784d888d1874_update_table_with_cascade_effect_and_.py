"""Update table with cascade effect and update table organization and termination condition

Revision ID: 784d888d1874
Revises: 41e21e33222d
Create Date: 2025-06-03 23:41:29.007760

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "784d888d1874"
down_revision = "41e21e33222d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("fk_agents_organization_id"), "agents", type_="foreignkey")
    op.create_foreign_key(
        "fk_agents_organization_id",
        "agents",
        "organizations",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(op.f("fk_inputs_organization_id"), "inputs", type_="foreignkey")
    op.create_foreign_key(
        "fk_inputs_organization_id",
        "inputs",
        "organizations",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(op.f("fk_models_organization_id"), "models", type_="foreignkey")
    op.create_foreign_key(
        "fk_models_organization_id",
        "models",
        "organizations",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.add_column(
        "organizations", sa.Column("email", sa.String(length=255), nullable=False)
    )
    op.drop_index(op.f("ix_organizations_name"), table_name="organizations")
    op.create_index(
        op.f("ix_organizations_email"), "organizations", ["email"], unique=True
    )
    op.drop_constraint(
        op.f("fk_outputs_organization_id"), "outputs", type_="foreignkey"
    )
    op.create_foreign_key(
        "fk_outputs_organization_id",
        "outputs",
        "organizations",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(op.f("fk_teams_organization_id"), "teams", type_="foreignkey")
    op.create_foreign_key(
        "fk_teams_organization_id",
        "teams",
        "organizations",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.add_column(
        "terminationconditions", sa.Column("organization_id", sa.UUID(), nullable=True)
    )
    op.create_foreign_key(
        "fk_terminationconditions_organization_id",
        "terminationconditions",
        "organizations",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(op.f("fk_tools_organization_id"), "tools", type_="foreignkey")
    op.create_foreign_key(
        "fk_tools_organization_id",
        "tools",
        "organizations",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_index(op.f("ix_users_name"), table_name="users")
    op.drop_constraint(op.f("users_organization_id_fkey"), "users", type_="foreignkey")
    op.create_foreign_key(
        "users_organization_id_fkey",
        "users",
        "organizations",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "users", type_="foreignkey")
    op.create_foreign_key(
        op.f("users_organization_id_fkey"),
        "users",
        "organizations",
        ["organization_id"],
        ["id"],
    )
    op.create_index(op.f("ix_users_name"), "users", ["name"], unique=False)
    op.drop_constraint(None, "tools", type_="foreignkey")
    op.create_foreign_key(
        op.f("fk_tools_organization_id"),
        "tools",
        "organizations",
        ["organization_id"],
        ["id"],
    )
    op.drop_constraint(None, "terminationconditions", type_="foreignkey")
    op.drop_column("terminationconditions", "organization_id")
    op.drop_constraint(None, "teams", type_="foreignkey")
    op.create_foreign_key(
        op.f("fk_teams_organization_id"),
        "teams",
        "organizations",
        ["organization_id"],
        ["id"],
    )
    op.drop_constraint(None, "outputs", type_="foreignkey")
    op.create_foreign_key(
        op.f("fk_outputs_organization_id"),
        "outputs",
        "organizations",
        ["organization_id"],
        ["id"],
    )
    op.drop_index(op.f("ix_organizations_email"), table_name="organizations")
    op.create_index(
        op.f("ix_organizations_name"), "organizations", ["name"], unique=False
    )
    op.drop_column("organizations", "email")
    op.drop_constraint(None, "models", type_="foreignkey")
    op.create_foreign_key(
        op.f("fk_models_organization_id"),
        "models",
        "organizations",
        ["organization_id"],
        ["id"],
    )
    op.drop_constraint(None, "inputs", type_="foreignkey")
    op.create_foreign_key(
        op.f("fk_inputs_organization_id"),
        "inputs",
        "organizations",
        ["organization_id"],
        ["id"],
    )
    op.drop_constraint(None, "agents", type_="foreignkey")
    op.create_foreign_key(
        op.f("fk_agents_organization_id"),
        "agents",
        "organizations",
        ["organization_id"],
        ["id"],
    )
    # ### end Alembic commands ###
