"""add_is_default_field_to_models

Revision ID: 20d7173d5ed1
Revises: a280ed076a90
Create Date: 2025-07-14 11:58:22.335523

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "20d7173d5ed1"
down_revision = "a280ed076a90"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add the column as nullable first
    op.add_column("models", sa.Column("is_default", sa.<PERSON>(), nullable=True))

    # Set default value for existing records (all existing models are not default)
    op.execute("UPDATE models SET is_default = false WHERE is_default IS NULL")

    # Now make the column non-nullable
    op.alter_column("models", "is_default", nullable=False)

    # Create the index
    op.create_index(
        op.f("ix_models_is_default"), "models", ["is_default"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_models_is_default"), table_name="models")
    op.drop_column("models", "is_default")
    # ### end Alembic commands ###
