"""Add team_templates table

Revision ID: 1118557e90b
Revises: dd0f74366b37
Create Date: 2025-01-16 12:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "1118557e90b"
down_revision = "dd0f74366b37"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "team_templates",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("category", sa.String(length=100), nullable=True),
        sa.Column(
            "template_config", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.Column("use_case", sa.String(length=255), nullable=True),
        sa.Column("tags", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("organization_id", sa.UUID(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "is_deleted", sa.Boolean(), nullable=False, server_default=sa.text("false")
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"], ["organizations.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_team_templates_category"), "team_templates", ["category"], unique=False
    )
    op.create_index(
        op.f("ix_team_templates_id"), "team_templates", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_team_templates_is_deleted"),
        "team_templates",
        ["is_deleted"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_team_templates_is_deleted"), table_name="team_templates")
    op.drop_index(op.f("ix_team_templates_id"), table_name="team_templates")
    op.drop_index(op.f("ix_team_templates_category"), table_name="team_templates")
    op.drop_table("team_templates")
    # ### end Alembic commands ###
