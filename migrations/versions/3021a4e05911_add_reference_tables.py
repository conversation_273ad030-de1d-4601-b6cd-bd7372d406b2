"""Add reference tables

Revision ID: 3021a4e05911
Revises: 784d888d1874
Create Date: 2025-06-15 00:58:53.810298

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "3021a4e05911"
down_revision = "784d888d1874"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "agent_tools",
        sa.Column("agent_id", sa.UUID(), nullable=False),
        sa.Column("tool_id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["agent_id"], ["agents.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["tool_id"], ["tools.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("agent_id", "tool_id"),
    )
    op.create_table(
        "team_agents",
        sa.Column("team_id", sa.UUID(), nullable=False),
        sa.Column("agent_id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["agent_id"], ["agents.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["team_id"], ["teams.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("team_id", "agent_id"),
    )
    op.create_table(
        "team_inputs",
        sa.Column("team_id", sa.UUID(), nullable=False),
        sa.Column("input_id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["input_id"], ["inputs.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["team_id"], ["teams.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("team_id", "input_id"),
    )
    op.create_table(
        "team_outputs",
        sa.Column("team_id", sa.UUID(), nullable=False),
        sa.Column("output_id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["output_id"], ["outputs.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["team_id"], ["teams.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("team_id", "output_id"),
    )
    op.create_table(
        "team_termination_conditions",
        sa.Column("team_id", sa.UUID(), nullable=False),
        sa.Column("termination_condition_id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["team_id"], ["teams.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["termination_condition_id"],
            ["terminationconditions.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("team_id", "termination_condition_id"),
    )
    op.add_column("teams", sa.Column("model_id", sa.UUID(), nullable=True))
    op.create_foreign_key(
        "fk_teams_model_id",
        "teams",
        "models",
        ["model_id"],
        ["id"],
        ondelete="SET NULL",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("fk_teams_model_id", "teams", type_="foreignkey")
    op.drop_column("teams", "model_id")
    op.drop_table("team_termination_conditions")
    op.drop_table("team_outputs")
    op.drop_table("team_inputs")
    op.drop_table("team_agents")
    op.drop_table("agent_tools")
    # ### end Alembic commands ###
