"""Add model_id in agent table

Revision ID: 789207e65d22
Revises: 3021a4e05911
Create Date: 2025-06-15 01:03:03.070507

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "789207e65d22"
down_revision = "3021a4e05911"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agents", sa.Column("model_id", sa.UUID(), nullable=True))
    op.create_foreign_key(
        None, "agents", "models", ["model_id"], ["id"], ondelete="CASCADE"
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "agents", type_="foreignkey")
    op.drop_column("agents", "model_id")
    # ### end Alembic commands ###
