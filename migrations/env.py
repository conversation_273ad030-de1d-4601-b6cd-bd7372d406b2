"""
Alembic environment configuration
"""
import os
import sys
from logging.config import fileConfig

from alembic import context
from sqlalchemy import engine_from_config, pool

# Add the parent directory to sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# Import all models to ensure they're known to SQLAlchemy
# This dynamically imports all models from the package
import importlib
import pkgutil

from aiplanet_platform import models

# Import the SQLAlchemy models and database URL
from aiplanet_platform.core.config import get_settings
from aiplanet_platform.core.database import Base

# Get the path to the models package
models_path = os.path.dirname(models.__file__)

# Import all modules in the models package
for _, name, _ in pkgutil.iter_modules([models_path]):
    importlib.import_module(f"aiplanet_platform.models.{name}")

settings = get_settings()

# This is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Force the DATABASE_URL from environment variables if available
database_url = os.environ.get("DATABASE_URL")
if database_url:
    print(f"Using DATABASE_URL from environment: {database_url}")
    config.set_main_option("sqlalchemy.url", database_url)
else:
    # Override the SQLAlchemy URL with the one from the settings
    config.set_main_option("sqlalchemy.url", str(settings.DATABASE_URL))
    print(f"Using DATABASE_URL from settings: {settings.DATABASE_URL}")

# Interpret the config file for Python logging.
# This line sets up loggers basically.
fileConfig(config.config_file_name)

# Add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline():
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
