"""
FastAPI application entry point
"""
import asyncio
import logging
import time
from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

from aiplanet_platform.core.config import get_settings
from aiplanet_platform.core.rate_limiter import rate_limiter

# Add middleware
from aiplanet_platform.middleware.rate_limit_middleware import (
    SecurityHeadersMiddleware,
    RequestTimingMiddleware,
    EnhancedRateLimitMiddleware,
)

# Import and include routers
from aiplanet_platform.routers.v1 import router as v1_router

# Temporal

# Initialize logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    cleanup_task = asyncio.create_task(periodic_cleanup())
    yield
    # Shutdown
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        pass


async def periodic_cleanup():
    """Background task to clean up expired rate limit windows"""
    while True:
        try:
            # Wait 1 hour between cleanups
            await asyncio.sleep(3600)  # 3600 seconds = 1 hour

            # Perform cleanup
            cleaned_count = rate_limiter.cleanup_expired_windows()
            logger.info(
                f"Rate limiter cleanup: removed {cleaned_count} expired windows"
            )

        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"Error during rate limiter cleanup: {e}")


# Initialize database if needed
# init_db()

# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=f"{settings.PROJECT_NAME} API",
    version="0.1.0",
    openapi_url=f"{settings.API_V1_PREFIX}/openapi.json",
    docs_url=f"{settings.API_V1_PREFIX}/docs",
    redoc_url=f"{settings.API_V1_PREFIX}/redoc",
    lifespan=lifespan,
)

# Add middleware (BEFORE CORS)
app.add_middleware(EnhancedRateLimitMiddleware)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RequestTimingMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin).rstrip("/") for origin in settings.BACKEND_CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Add request processing time middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Exception handlers
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Handle HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors"""
    errors = []
    for error in exc.errors():
        errors.append(
            {
                "loc": error["loc"],
                "msg": error["msg"],
                "type": error["type"],
            }
        )
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": "Validation error", "errors": errors},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "Internal server error"},
    )


app.include_router(v1_router, prefix=settings.API_V1_PREFIX)


# Root endpoint
@app.get("/", tags=["Health"])
async def root():
    """
    Root endpoint for health checks.
    """
    return {
        "message": f"Welcome to {settings.PROJECT_NAME} API",
        "docs": f"{settings.API_V1_PREFIX}/docs",
        "status": "healthy",
        "version": "0.1.0",
    }


# Health check
@app.get("/health", tags=["Health"])
async def health_check():
    """
    Health check endpoint.
    """
    return {
        "status": "healthy",
        "api": "ok",
        "database": "ok",  # You could add actual db checking here
    }


@app.get("/temporal/health")
async def temporal_health_check():
    """Temporal-specific health check"""
    try:
        from aiplanet_platform.services.temporal_worker_service import (
            get_worker_service,
        )

        worker_service = await get_worker_service()
        is_healthy = await worker_service.health_check()

        return {
            "temporal_worker": "healthy" if is_healthy else "unhealthy",
            "worker_running": is_healthy,
        }
    except Exception as e:
        return {"temporal_worker": "error", "error": str(e), "worker_running": False}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
