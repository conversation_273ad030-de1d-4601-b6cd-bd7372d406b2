"""
Dedicated Temporal Worker Server - Runs AI processing separately from FastAPI
FIXED VERSION with better error handling and connection management
"""
import asyncio
import logging
import os
import signal
import sys
import time
from datetime import timedelta
from typing import Optional
from temporalio.worker import Worker
from temporalio.worker.workflow_sandbox import (
    SandboxedWorkflowRunner,
    SandboxRestrictions,
)
from temporalio.client import Client

from aiplanet_platform.core.temporal_config import TemporalClient, TemporalConfig
from aiplanet_platform.jobs.temporal_streaming_workflow import (
    TeamStreamingWorkflow,
    setup_team_environment,
    execute_team_stream,
    update_run_status,
)

# Setup logging first
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DedicatedTemporalWorker:
    """Dedicated Temporal worker for processing AI tasks"""

    def __init__(self, worker_id: Optional[str] = None):
        self.worker_id = worker_id or f"worker-{os.getpid()}"
        self.worker: Optional[Worker] = None
        self.client: Optional[Client] = None
        self.is_running = False
        self.connection_retries = 0
        self.max_retries = 10

    async def _wait_for_temporal_server(self) -> bool:
        """Wait for Temporal server to be available with retries"""
        logger.info("Waiting for Temporal server to be available...")

        max_wait_time = 300  # 5 minutes
        start_time = time.time()
        retry_interval = 5  # seconds

        while (time.time() - start_time) < max_wait_time:
            try:
                # Try to create a client and connect
                config = TemporalClient.get_config()

                client = await Client.connect(
                    f"{config.host}",
                    namespace=config.namespace,
                    tls=config.tls_config if hasattr(config, "tls_config") else False,
                )

                # Test the connection by listing workflows (should not fail if server is up)
                try:
                    handle = client.get_workflow_handle(
                        "test-connection-check", run_id="test-run-id"
                    )
                    # This will fail but tells us the server is responding
                    await handle.describe()
                except Exception:
                    # Expected to fail, but server is responding
                    pass

                # await client.close()
                logger.info("✅ Temporal server is available!")
                return True

            except Exception as e:
                elapsed = int(time.time() - start_time)
                logger.warning(
                    f"Temporal server not ready (attempt after {elapsed}s): {e}"
                )
                logger.info(f"Retrying in {retry_interval} seconds...")
                await asyncio.sleep(retry_interval)

        logger.error(
            f"Temporal server did not become available within {max_wait_time} seconds"
        )
        return False

    async def start(self):
        """Start the dedicated worker with retry logic"""
        logger.info(f"Starting dedicated Temporal worker: {self.worker_id}")

        # Wait for Temporal server to be available
        if not await self._wait_for_temporal_server():
            raise Exception("Temporal server is not available")

        while self.connection_retries < self.max_retries:
            try:
                # Initialize Temporal client
                self.client = await TemporalClient.get_client()
                config = TemporalClient.get_config()

                logger.info(f"Connected to Temporal server: {config.host}")
                logger.info(f"Using namespace: {config.namespace}")
                logger.info(f"Listening on task queue: {config.task_queue}")

                # Create worker with all activities
                self.worker = Worker(
                    self.client,
                    task_queue=config.task_queue,
                    workflows=[TeamStreamingWorkflow],
                    activities=[
                        setup_team_environment,
                        execute_team_stream,
                        update_run_status,
                    ],
                    workflow_runner=SandboxedWorkflowRunner(
                        restrictions=SandboxRestrictions.default.with_passthrough_modules(
                            "autogen_agentchat.messages"
                        )
                    ),
                    # Worker configuration for processing workloads
                    max_concurrent_activities=5,  # Adjust based on your server capacity
                    max_concurrent_workflow_tasks=10,
                    identity=self.worker_id,  # Helps with monitoring
                    # ✅ FIXED: Use timedelta for graceful shutdown timeout
                    graceful_shutdown_timeout=timedelta(seconds=30),
                )

                logger.info(f"Worker {self.worker_id} configured successfully")
                logger.info("🚀 Starting to process activities...")

                self.is_running = True
                self.connection_retries = 0  # Reset on successful connection

                # This blocks and runs the worker
                await self.worker.run()
                break  # Exit the retry loop if worker runs successfully

            except Exception as e:
                self.connection_retries += 1
                logger.error(
                    f"Failed to start worker {self.worker_id} (attempt {self.connection_retries}/{self.max_retries}): {e}"
                )

                if self.connection_retries < self.max_retries:
                    wait_time = min(
                        60, self.connection_retries * 5
                    )  # Exponential backoff, max 60s
                    logger.info(f"Retrying in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(
                        f"Max retries ({self.max_retries}) reached. Worker {self.worker_id} failed to start."
                    )
                    raise

        self.is_running = False
        logger.info(f"Worker {self.worker_id} stopped")

    async def stop(self):
        """Gracefully stop the worker"""
        if self.worker and self.is_running:
            logger.info(f"Stopping worker {self.worker_id}...")
            try:
                # ✅ FIXED: asyncio.wait_for expects timeout in seconds (float/int)
                await asyncio.wait_for(self.worker.shutdown(), timeout=30.0)
            except asyncio.TimeoutError:
                logger.warning(f"Worker {self.worker_id} shutdown timed out")
            self.is_running = False

        if self.client:
            try:
                await self.client.close()
            except Exception as e:
                logger.warning(f"Error closing client: {e}")


# Global worker instance
worker_instance: Optional[DedicatedTemporalWorker] = None


def check_environment():
    """Check if required environment variables are set"""
    config = TemporalConfig()

    print("Environment Check:")
    print(f"  Temporal Host: {config.host}")
    print("  Temporal Port: ")
    print(f"  Namespace: {config.namespace}")
    print(f"  Task Queue: {config.task_queue}")
    print(f"  TLS Enabled: {getattr(config, 'tls_enabled', False)}")

    # Check if we can resolve the hostname

    # try:
    #     socket.gethostbyname(config.host)
    #     print(f"✅ Can resolve hostname: {config.host}")
    # except socket.gaierror:
    #     print(f"❌ Cannot resolve hostname: {config.host}")
    #     return False

    return True


async def main():
    """Main entry point for the worker server"""
    global worker_instance

    print("=" * 60)
    print("🤖 AI Planet Platform - Temporal Worker Server (Fixed)")
    print("=" * 60)

    # Check environment
    if not check_environment():
        print("❌ Environment check failed")
        sys.exit(1)

    # Get worker configuration from environment
    worker_id = os.getenv("WORKER_ID", f"worker-{os.getpid()}")

    # Display configuration
    config = TemporalConfig()
    print(f"Worker ID: {worker_id}")
    print(f"Temporal Host: {config.host}")
    print(f"Namespace: {config.namespace}")
    print(f"Task Queue: {config.task_queue}")
    print("-" * 60)

    try:
        # Create and start worker
        worker_instance = DedicatedTemporalWorker(worker_id)

        # Setup graceful shutdown
        shutdown_event = asyncio.Event()

        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            shutdown_event.set()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Start worker in a task
        worker_task = asyncio.create_task(worker_instance.start())

        # Wait for either the worker to complete or shutdown signal
        await asyncio.wait(
            [worker_task, asyncio.create_task(shutdown_event.wait())],
            return_when=asyncio.FIRST_COMPLETED,
        )

        # If we get here due to shutdown signal, stop the worker
        if not worker_task.done():
            logger.info("Shutdown signal received, stopping worker...")
            await worker_instance.stop()
            # Cancel the worker task if it's still running
            worker_task.cancel()
            try:
                await worker_task
            except asyncio.CancelledError:
                pass

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Worker failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
    finally:
        if worker_instance:
            await worker_instance.stop()
        logger.info("Worker server shutdown complete")


if __name__ == "__main__":
    # Ensure we're in the right environment
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Worker server stopped by user")
    except Exception as e:
        print(f"❌ Worker server failed: {e}")
        sys.exit(1)
