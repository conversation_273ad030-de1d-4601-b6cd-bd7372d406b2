# Docker Environment Configuration
# Copy this to .env for Docker deployment

# Project Settings
PROJECT_NAME=aiplanet_platform
DEBUG=false
API_V1_PREFIX=/api/v1

# Security
SECRET_KEY=fd44c0aa45613a3095fa7f0a975071feae62485dae1c1e935ff3173a3bf35407
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Origins (JSON array as string)
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://localhost:3001","http://127.0.0.1:3000","http://127.0.0.1:8000"]

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=aiplanet_platform
DATABASE_URL=**************************************/aiplanet_platform

# Database Pool Settings
SQL_ECHO=false
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800

# Redis Configuration
REDIS_URL=redis://redis:6379/0
CACHE_TTL=300

# Logging
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# Docker Port Configuration
APP_PORT=8000
DB_PORT=5432
REDIS_PORT=6379
PGADMIN_PORT=5050

# PgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin

# Docker Target Environment
DOCKER_TARGET=production