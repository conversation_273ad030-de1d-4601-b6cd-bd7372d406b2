# test_step3b_fixed.py
"""
Fixed test for Step 3B: Rate Limiting Middleware
"""

import sys
import os
from uuid import uuid4
import asyncio

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_middleware_import():
    """Test that middleware classes can be imported"""
    print("📦 Testing Middleware Import...")

    try:
        from aiplanet_platform.middleware.rate_limit_middleware import (
            RateLimitHeadersMiddleware,
            RequestTimingMiddleware,
            APIKeyContextMiddleware,
            SecurityHeadersMiddleware,
            get_rate_limit_headers,
            add_rate_limit_headers,
        )

        print("✅ All middleware classes imported successfully")

        # Test that middleware classes are proper classes
        assert hasattr(
            RateLimitHeadersMiddleware, "dispatch"
        ), "RateLimitHeadersMiddleware should have dispatch method"
        assert hasattr(
            RequestTimingMiddleware, "dispatch"
        ), "RequestTimingMiddleware should have dispatch method"
        assert hasattr(
            APIKeyContextMiddleware, "dispatch"
        ), "APIKeyContextMiddleware should have dispatch method"
        assert hasattr(
            SecurityHeadersMiddleware, "dispatch"
        ), "SecurityHeadersMiddleware should have dispatch method"
        print("✅ All middleware classes have required methods")

        # Test utility functions
        assert callable(
            get_rate_limit_headers
        ), "get_rate_limit_headers should be callable"
        assert callable(
            add_rate_limit_headers
        ), "add_rate_limit_headers should be callable"
        print("✅ Utility functions are callable")

        return True

    except ImportError as e:
        print(f"❌ Failed to import middleware: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing middleware import: {e}")
        return False


def create_mock_request():
    """Create a mock FastAPI request object"""

    class MockState:
        def __init__(self):
            self.api_key = None
            self.auth_type = None

    class MockHeaders:
        def __init__(self, headers_dict=None):
            self._headers = headers_dict or {}

        def get(self, key, default=None):
            return self._headers.get(key.lower(), default)

    class MockRequest:
        def __init__(self, headers=None):
            self.state = MockState()
            self.headers = MockHeaders(headers or {})

    return MockRequest()


def create_mock_response():
    """Create a fresh mock FastAPI response object"""

    class MockHeaders:
        def __init__(self):
            self._headers = {}

        def __setitem__(self, key, value):
            self._headers[key] = value

        def __getitem__(self, key):
            return self._headers[key]

        def get(self, key, default=None):
            return self._headers.get(key, default)

        def items(self):
            return self._headers.items()

    class MockResponse:
        def __init__(self):
            self.headers = MockHeaders()
            self.status_code = 200

    return MockResponse()


def create_mock_api_key():
    """Create a mock API key for testing"""
    try:
        from aiplanet_platform.models.api_key import APIKey

        class MockAPIKey(APIKey):
            def __init__(self):
                self.id = uuid4()
                self.key_prefix = "ak_test1234"
                self.rate_limit_per_minute = 100
                self.rate_limit_per_hour = 1000
                self.rate_limit_per_day = 10000
                self.total_requests = 42

        return MockAPIKey()
    except Exception:
        # Fallback mock
        class MockAPIKey:
            def __init__(self):
                self.id = uuid4()
                self.key_prefix = "ak_test1234"
                self.rate_limit_per_minute = 100
                self.rate_limit_per_hour = 1000
                self.rate_limit_per_day = 10000
                self.total_requests = 42

        return MockAPIKey()


async def test_rate_limit_headers_middleware():
    """Test rate limit headers middleware"""
    print("\n⏱️  Testing Rate Limit Headers Middleware...")

    try:
        from aiplanet_platform.middleware.rate_limit_middleware import (
            RateLimitHeadersMiddleware,
        )

        # Create middleware instance
        middleware = RateLimitHeadersMiddleware(app=None)

        # Test WITH API key
        request_with_key = create_mock_request()
        response_with_key = create_mock_response()

        # Set up API key in request state
        api_key = create_mock_api_key()
        request_with_key.state.api_key = api_key

        # Mock call_next function that returns a fresh response
        async def mock_call_next_with_key(req):
            return response_with_key

        # Test middleware with API key
        result_with_key = await middleware.dispatch(
            request_with_key, mock_call_next_with_key
        )

        # Check that rate limit headers were added
        assert (
            "X-RateLimit-Limit-Minute" in result_with_key.headers._headers
        ), "Should have minute limit header"
        assert (
            "X-RateLimit-Remaining-Minute" in result_with_key.headers._headers
        ), "Should have minute remaining header"
        assert (
            "X-API-Key-ID" in result_with_key.headers._headers
        ), "Should have API key ID header"
        assert (
            result_with_key.headers.get("X-API-Key-ID") == "ak_test1234"
        ), "Should have correct API key prefix"

        print("✅ Rate limit headers added correctly with API key")

        # Test WITHOUT API key (fresh request and response)
        request_no_key = create_mock_request()
        response_no_key = create_mock_response()

        # Make sure no API key is set
        assert request_no_key.state.api_key is None, "Request should have no API key"

        async def mock_call_next_no_key(req):
            return response_no_key

        result_no_key = await middleware.dispatch(request_no_key, mock_call_next_no_key)
        assert (
            "X-RateLimit-Limit-Minute" not in result_no_key.headers._headers
        ), "Should not have rate limit headers without API key"
        assert (
            "X-API-Key-ID" not in result_no_key.headers._headers
        ), "Should not have API key ID without API key"

        print("✅ No headers added when no API key present")

        return True

    except Exception as e:
        print(f"❌ Error testing rate limit headers middleware: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_security_headers_middleware():
    """Test security headers middleware"""
    print("\n🔒 Testing Security Headers Middleware...")

    try:
        from aiplanet_platform.middleware.rate_limit_middleware import (
            SecurityHeadersMiddleware,
        )

        # Create middleware instance
        middleware = SecurityHeadersMiddleware(app=None)

        # Create fresh mock request and response
        request = create_mock_request()
        response = create_mock_response()

        # Mock call_next function
        async def mock_call_next(req):
            return response

        # Test middleware
        result = await middleware.dispatch(request, mock_call_next)

        # Check that security headers were added
        expected_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options",
            "X-XSS-Protection",
            "Referrer-Policy",
            "X-API-Version",
            "X-Powered-By",
        ]

        for header in expected_headers:
            assert header in result.headers._headers, f"Should have {header} header"

        assert (
            result.headers.get("X-Content-Type-Options") == "nosniff"
        ), "Should have correct content type options"
        assert (
            result.headers.get("X-Frame-Options") == "DENY"
        ), "Should have correct frame options"

        print("✅ Security headers added correctly")
        return True

    except Exception as e:
        print(f"❌ Error testing security headers middleware: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_timing_middleware():
    """Test request timing middleware"""
    print("\n⏰ Testing Request Timing Middleware...")

    try:
        from aiplanet_platform.middleware.rate_limit_middleware import (
            RequestTimingMiddleware,
        )

        # Create middleware instance
        middleware = RequestTimingMiddleware(app=None)

        # Create fresh mock request and response
        request = create_mock_request()
        response = create_mock_response()

        # Mock call_next function with delay
        async def mock_call_next_with_delay(req):
            import asyncio

            await asyncio.sleep(0.01)  # 10ms delay
            return response

        # Test middleware
        result = await middleware.dispatch(request, mock_call_next_with_delay)

        # Check that timing header was added
        assert (
            "X-Process-Time" in result.headers._headers
        ), "Should have process time header"

        process_time = float(result.headers.get("X-Process-Time"))
        assert process_time > 0, "Process time should be greater than 0"
        assert (
            process_time < 1.0
        ), "Process time should be reasonable (less than 1 second)"

        print(f"✅ Timing header added correctly (process time: {process_time:.4f}s)")
        return True

    except Exception as e:
        print(f"❌ Error testing timing middleware: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_api_key_context_middleware():
    """Test API key context middleware"""
    print("\n🔑 Testing API Key Context Middleware...")

    try:
        from aiplanet_platform.middleware.rate_limit_middleware import (
            APIKeyContextMiddleware,
        )

        # Create middleware instance
        middleware = APIKeyContextMiddleware(app=None)

        async def mock_call_next(req):
            return create_mock_response()

        # Test with Authorization header (API key)
        request_auth = create_mock_request()
        request_auth.headers = request_auth.headers.__class__(
            {"authorization": "Bearer ak_test123456_abcdef"}
        )

        await middleware.dispatch(request_auth, mock_call_next)
        assert (
            request_auth.state.auth_type == "api_key"
        ), "Should detect API key auth type"
        assert hasattr(
            request_auth.state, "api_key_value"
        ), "Should have api_key_value attribute"
        assert (
            request_auth.state.api_key_value == "ak_test123456_abcdef"
        ), "Should store API key value"
        print("✅ API key detected in Authorization header")

        # Test with X-API-Key header
        request_x_key = create_mock_request()
        request_x_key.headers = request_x_key.headers.__class__(
            {"x-api-key": "ak_another_key"}
        )

        await middleware.dispatch(request_x_key, mock_call_next)
        assert (
            request_x_key.state.auth_type == "api_key"
        ), "Should detect API key auth type from X-API-Key"
        assert hasattr(
            request_x_key.state, "api_key_value"
        ), "Should have api_key_value attribute"
        assert (
            request_x_key.state.api_key_value == "ak_another_key"
        ), "Should store API key value from X-API-Key"
        print("✅ API key detected in X-API-Key header")

        # Test with JWT token (doesn't start with ak_)
        request_jwt = create_mock_request()
        request_jwt.headers = request_jwt.headers.__class__(
            {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}
        )

        await middleware.dispatch(request_jwt, mock_call_next)
        assert request_jwt.state.auth_type == "jwt", "Should detect JWT auth type"
        assert (
            not hasattr(request_jwt.state, "api_key_value")
            or request_jwt.state.api_key_value is None
        ), "Should not have API key value for JWT"
        print("✅ JWT token detected correctly")

        # Test with no authorization
        request_no_auth = create_mock_request()

        await middleware.dispatch(request_no_auth, mock_call_next)
        assert (
            request_no_auth.state.auth_type is None
        ), "Should have no auth type without headers"
        assert (
            not hasattr(request_no_auth.state, "api_key_value")
            or request_no_auth.state.api_key_value is None
        ), "Should not have API key value without auth"
        print("✅ No authentication detected correctly")

        return True

    except Exception as e:
        print(f"❌ Error testing API key context middleware: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_utility_functions():
    """Test utility functions"""
    print("\n🛠️  Testing Utility Functions...")

    try:
        from aiplanet_platform.middleware.rate_limit_middleware import (
            get_rate_limit_headers,
            add_rate_limit_headers,
        )

        # Create mock API key
        api_key = create_mock_api_key()

        # Test get_rate_limit_headers
        headers = get_rate_limit_headers(api_key)
        assert isinstance(headers, dict), "Should return a dictionary"
        assert "X-RateLimit-Limit-Minute" in headers, "Should have minute limit"
        assert "X-API-Key-ID" in headers, "Should have API key ID"
        assert (
            headers["X-API-Key-ID"] == "ak_test1234"
        ), "Should have correct API key prefix"
        print("✅ get_rate_limit_headers works correctly")

        # Test add_rate_limit_headers
        response = create_mock_response()
        add_rate_limit_headers(response, api_key)

        assert (
            "X-RateLimit-Limit-Minute" in response.headers._headers
        ), "Should add rate limit headers to response"
        assert (
            "X-API-Key-ID" in response.headers._headers
        ), "Should add API key ID to response"
        print("✅ add_rate_limit_headers works correctly")

        return True

    except Exception as e:
        print(f"❌ Error testing utility functions: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    """Run Step 3B tests"""
    print("🚀 Running Step 3B Tests: Rate Limiting Middleware (Fixed)\n")

    tests_passed = 0
    total_tests = 6

    # Run tests
    if test_middleware_import():
        tests_passed += 1

    if await test_rate_limit_headers_middleware():
        tests_passed += 1

    if await test_security_headers_middleware():
        tests_passed += 1

    if await test_timing_middleware():
        tests_passed += 1

    if await test_api_key_context_middleware():
        tests_passed += 1

    if test_utility_functions():
        tests_passed += 1

    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 All Step 3B tests passed!")
        print("\n✅ Step 3B is complete!")
        print("📝 What's working:")
        print("   ✅ Rate limit headers middleware")
        print("   ✅ Security headers middleware")
        print("   ✅ Request timing middleware")
        print("   ✅ API key context middleware")
        print("   ✅ Utility functions for manual header management")
        print("\n🔧 Middleware features:")
        print("   ✅ Automatic rate limit headers for API key requests")
        print("   ✅ Security headers for all responses")
        print("   ✅ Request timing information")
        print("   ✅ API key detection and context setting")
        print("   ✅ Manual header management utilities")
        print("\n🎯 Step 3 Summary:")
        print("   ✅ Enhanced Security Functions (Step 3A) - Complete")
        print("   ✅ Rate Limiting Middleware (Step 3B) - Complete")
        print("\n🚀 Ready for Step 4: API Key Management Router")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues before proceeding.")
        return False


if __name__ == "__main__":
    asyncio.run(main())
