"""
Tests for the presigned URL endpoint
"""
import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)


class TestPresignedURLEndpoint:
    """Test cases for the presigned URL endpoint"""

    @pytest.fixture
    def mock_aws_settings(self):
        """Mock AWS settings"""
        with patch("aiplanet_platform.core.config.get_settings") as mock_get_settings:
            mock_settings = mock_get_settings.return_value
            mock_settings.STORAGE_PROVIDER = "aws"
            mock_settings.AWS_ACCESS_KEY_ID = "test_access_key"
            mock_settings.AWS_SECRET_ACCESS_KEY = "test_secret_key"
            mock_settings.AWS_REGION = "us-east-1"
            mock_settings.AWS_S3_BUCKET = "test-bucket"
            yield mock_settings

    @patch("aiplanet_platform.routers.v1.private.session.PresignedURLService")
    def test_generate_presigned_url_success(
        self, mock_service_class, mock_aws_settings
    ):
        """Test successful presigned URL generation"""
        # Mock the service instance
        mock_service = AsyncMock()
        mock_service.generate_presigned_url.return_value = {
            "presigned_url": "https://test-bucket.s3.amazonaws.com/test.pdf?signature=test",
            "provider": "aws",
            "bucket_name": "test-bucket",
            "object_name": "test.pdf",
            "expires_in_minutes": 15,
        }
        mock_service.close = AsyncMock()
        mock_service_class.return_value = mock_service

        # Make the request
        response = client.post(
            "/api/v1/private/sessions/chat/presigned",
            json={
                "object_name": "test.pdf",
                "expiration_minutes": 15,
                "content_type": "application/pdf",
            },
        )

        assert response.status_code == 200
        data = response.json()
        assert data["provider"] == "aws"
        assert data["bucket_name"] == "test-bucket"
        assert data["object_name"] == "test.pdf"
        assert data["expires_in_minutes"] == 15
        assert "presigned_url" in data

    def test_invalid_request_data(self):
        """Test invalid request data"""
        # Test missing object_name
        response = client.post(
            "/api/v1/private/sessions/chat/presigned",
            json={"expiration_minutes": 15, "content_type": "application/pdf"},
        )
        assert response.status_code == 422

        # Test invalid expiration_minutes (too high)
        response = client.post(
            "/api/v1/private/sessions/chat/presigned",
            json={
                "object_name": "test.pdf",
                "expiration_minutes": 2000,  # Max is 1440 (24 hours)
                "content_type": "application/pdf",
            },
        )
        assert response.status_code == 422
