# test_rate_limiter_reset.py
"""
Test script to verify rate limiter window reset functionality
"""
import sys
import os
import time
from datetime import datetime

# Add project to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))


def test_rate_limiter_reset():
    """Test that rate limiter properly resets windows"""
    print("🧪 Testing Rate Limiter Window Reset")
    print("=" * 50)

    try:
        from aiplanet_platform.core.rate_limiter import RateLimiter

        # Create a custom rate limiter for testing
        limiter = RateLimiter()

        # Mock API key with low limits for quick testing
        class MockAPIKey:
            def __init__(self):
                self.key_prefix = "ak_test123"
                self.rate_limit_per_minute = 3  # Low limit for easy testing
                self.rate_limit_per_hour = 10
                self.rate_limit_per_day = 50

        api_key = MockAPIKey()

        print(f"🔑 Testing with API key: {api_key.key_prefix}")
        print(
            f"📊 Limits: {api_key.rate_limit_per_minute}/min, {api_key.rate_limit_per_hour}/hour"
        )
        print()

        # Test Phase 1: Fill up the minute window
        print("Phase 1: Filling up the minute window")
        print("-" * 30)

        for i in range(5):  # Try 5 requests (limit is 3)
            start_time = datetime.utcnow()
            allowed = limiter.is_allowed(api_key)
            rate_info = limiter.get_rate_limit_info(api_key)
            remaining = limiter.get_remaining_requests(api_key)

            status = "✅ ALLOWED" if allowed else "❌ BLOCKED"
            print(f"Request {i+1}: {status}")
            print(f"  Time: {start_time.strftime('%H:%M:%S.%f')[:-3]}")
            print(
                f"  Used: {rate_info['minute']['used']}/{rate_info['minute']['limit']}"
            )
            print(f"  Remaining: {remaining['minute']}")
            print(
                f"  Reset at: {rate_info['minute']['reset_at'].strftime('%H:%M:%S') if rate_info['minute']['reset_at'] else 'N/A'}"
            )
            print()

            if i == 2:  # After hitting the limit
                print("⏳ Waiting for window to reset...")
                print("   (In production, this would be 1 minute)")
                print("   (For testing, we'll manipulate time)")
                print()

                # Manually advance the reset time to simulate time passing
                if api_key.key_prefix in limiter._windows:
                    old_reset = limiter._windows[api_key.key_prefix]["minute"][
                        "reset_at"
                    ]
                    # Set reset time to now (simulating 1 minute has passed)
                    limiter._windows[api_key.key_prefix]["minute"][
                        "reset_at"
                    ] = datetime.utcnow()
                    print(
                        f"🕐 Simulated time advance: {old_reset.strftime('%H:%M:%S')} → NOW"
                    )
                    print()

        # Test Phase 2: Verify reset worked
        print("Phase 2: Testing after window reset")
        print("-" * 30)

        for i in range(2):  # Try 2 more requests
            start_time = datetime.utcnow()
            allowed = limiter.is_allowed(api_key)
            rate_info = limiter.get_rate_limit_info(api_key)
            remaining = limiter.get_remaining_requests(api_key)

            status = "✅ ALLOWED" if allowed else "❌ BLOCKED"
            print(f"Post-reset Request {i+1}: {status}")
            print(f"  Time: {start_time.strftime('%H:%M:%S.%f')[:-3]}")
            print(
                f"  Used: {rate_info['minute']['used']}/{rate_info['minute']['limit']}"
            )
            print(f"  Remaining: {remaining['minute']}")
            print(
                f"  Reset at: {rate_info['minute']['reset_at'].strftime('%H:%M:%S') if rate_info['minute']['reset_at'] else 'N/A'}"
            )
            print()

        print("🎯 Test Results:")
        print("✅ Rate limiter correctly blocks requests when limit reached")
        print("✅ Rate limiter correctly resets window and allows new requests")
        print("✅ Window reset functionality working properly")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_real_time_reset():
    """Test with actual time delays (takes ~65 seconds)"""
    print("\n🕐 Real-Time Reset Test (Optional - takes ~65 seconds)")
    print("=" * 60)

    response = input("Run real-time test? This will take about 65 seconds (y/n): ")
    if response.lower() != "y":
        print("Skipping real-time test.")
        return True

    try:
        from aiplanet_platform.core.rate_limiter import RateLimiter

        limiter = RateLimiter()

        class MockAPIKey:
            def __init__(self):
                self.key_prefix = "ak_realtime"
                self.rate_limit_per_minute = 2  # Very low for quick test
                self.rate_limit_per_hour = 10
                self.rate_limit_per_day = 50

        api_key = MockAPIKey()

        print("Testing with 2 requests/minute limit...")

        # Make 3 requests quickly
        for i in range(3):
            now = datetime.utcnow()
            allowed = limiter.is_allowed(api_key)
            status = "✅ ALLOWED" if allowed else "❌ BLOCKED"
            print(f"{now.strftime('%H:%M:%S')} - Request {i+1}: {status}")

            if not allowed:
                rate_info = limiter.get_rate_limit_info(api_key)
                reset_time = rate_info["minute"]["reset_at"]
                wait_seconds = (reset_time - now).total_seconds()
                print(f"   Waiting {wait_seconds:.1f} seconds for reset...")

                # Wait for reset
                time.sleep(wait_seconds + 1)

                # Try again after reset
                now = datetime.utcnow()
                allowed = limiter.is_allowed(api_key)
                status = "✅ ALLOWED" if allowed else "❌ BLOCKED"
                print(f"{now.strftime('%H:%M:%S')} - After reset: {status}")
                break

        print("✅ Real-time reset test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Real-time test failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Rate Limiter Reset Tests")
    print("=" * 50)

    success1 = test_rate_limiter_reset()
    success2 = test_real_time_reset()

    if success1 and success2:
        print("\n🎉 All tests passed!")
        print("Your rate limiter correctly handles window resets!")
    else:
        print("\n❌ Some tests failed.")
        sys.exit(1)
