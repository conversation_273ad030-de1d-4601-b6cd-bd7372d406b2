# ruff: noqa: F401
"""
Fixed test for Step 3A: Enhanced Security Functions
"""

import sys
import os
from uuid import uuid4

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_enhanced_security_import():
    """Test that Enhanced Security functions can be imported"""
    print("📦 Testing Enhanced Security Import...")

    try:
        from aiplanet_platform.core.enhanced_security import (
            APIKeyAuth,
            create_api_key_auth,  # type: ignore
            get_current_user_or_api_key,  # type: ignore
            require_scopes_for_auth,  # type: ignore
            create_mixed_auth_dependency,  # type: ignore
            get_auth_context,  # type: ignore
            require_user_auth,  # type: ignore
            require_api_key_auth,  # type: ignore
            is_user_auth,  # type: ignore
            is_api_key_auth,  # type: ignore
            get_organization_id,  # type: ignore
            get_user_id,  # type: ignore
        )

        print("✅ Enhanced security functions imported successfully")

        # Test that APIKeyAuth class can be instantiated
        auth_class = APIKeyAuth()
        assert hasattr(auth_class, "__call__"), "APIKeyAuth should be callable"
        assert hasattr(
            auth_class, "required_scopes"
        ), "APIKeyAuth should have required_scopes"
        print("✅ APIKeyAuth class works correctly")

        return True

    except ImportError as e:
        print(f"❌ Failed to import enhanced security functions: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing enhanced security import: {e}")
        return False


def test_api_key_auth_class():
    """Test APIKeyAuth class functionality"""
    print("\n🔐 Testing APIKeyAuth Class...")

    try:
        from aiplanet_platform.core.enhanced_security import APIKeyAuth

        # Test with no scopes
        auth1 = APIKeyAuth()
        assert auth1.required_scopes == [], "Should have empty scopes by default"
        print("✅ APIKeyAuth with no scopes works")

        # Test with scopes
        auth2 = APIKeyAuth(required_scopes=["read:users", "write:agents"])
        assert auth2.required_scopes == [
            "read:users",
            "write:agents",
        ], "Should have specified scopes"
        print("✅ APIKeyAuth with scopes works")

        # Test create_api_key_auth helper
        from aiplanet_platform.core.enhanced_security import create_api_key_auth

        auth3 = create_api_key_auth("read:users", "write:agents")
        assert isinstance(auth3, APIKeyAuth), "Should return APIKeyAuth instance"
        assert auth3.required_scopes == [
            "read:users",
            "write:agents",
        ], "Should have correct scopes"
        print("✅ create_api_key_auth helper works")

        return True

    except Exception as e:
        print(f"❌ Error testing APIKeyAuth class: {e}")
        import traceback

        traceback.print_exc()
        return False


def create_realistic_user_mock():
    """Create a realistic mock user that inherits from the base User class"""
    try:
        from aiplanet_platform.models.user import User

        # Create a mock that inherits from the real User class
        class MockUser(User):
            def __init__(self):
                # Don't call super().__init__ to avoid SQLAlchemy issues
                self.id = uuid4()
                self.email = "<EMAIL>"
                self.organization_id = uuid4()

        return MockUser()
    except Exception:
        # Fallback: create a simple mock with the right class name
        class MockUser:
            def __init__(self):
                self.id = uuid4()
                self.email = "<EMAIL>"
                self.organization_id = uuid4()
                self.__class__.__name__ = "User"

        return MockUser()


def create_realistic_api_key_mock():
    """Create a realistic mock API key that inherits from the base APIKey class"""
    try:
        from aiplanet_platform.models.api_key import APIKey

        # Create a mock that inherits from the real APIKey class
        class MockAPIKey(APIKey):
            def __init__(self):
                # Don't call super().__init__ to avoid SQLAlchemy issues
                self.id = uuid4()
                self.name = "Test API Key"
                self.user_id = uuid4()
                self.organization_id = uuid4()
                self.scopes = ["read:users", "write:agents"]

            def has_scope(self, scope: str) -> bool:
                return scope in self.scopes

        return MockAPIKey()
    except Exception:
        # Fallback: create a simple mock with the right class name
        class MockAPIKey:
            def __init__(self):
                self.id = uuid4()
                self.name = "Test API Key"
                self.user_id = uuid4()
                self.organization_id = uuid4()
                self.scopes = ["read:users", "write:agents"]
                self.__class__.__name__ = "APIKey"

            def has_scope(self, scope: str) -> bool:
                return scope in self.scopes

        return MockAPIKey()


def test_helper_functions():
    """Test helper functions with realistic mocks"""
    print("\n🔧 Testing Helper Functions...")

    try:
        from aiplanet_platform.core.enhanced_security import (
            get_auth_context,
            is_user_auth,
            is_api_key_auth,
            get_organization_id,
            get_user_id,
        )

        user = create_realistic_user_mock()
        api_key = create_realistic_api_key_mock()

        # Test get_auth_context with user
        user_context = get_auth_context(user)
        assert (
            user_context["auth_type"] == "jwt"
        ), f"Should identify JWT auth, got: {user_context['auth_type']}"
        assert user_context["email"] == "<EMAIL>", "Should have user email"
        print("✅ get_auth_context works with User")

        # Test get_auth_context with API key
        api_key_context = get_auth_context(api_key)
        assert (
            api_key_context["auth_type"] == "api_key"
        ), f"Should identify API key auth, got: {api_key_context['auth_type']}"
        assert (
            api_key_context["api_key_name"] == "Test API Key"
        ), "Should have API key name"
        assert api_key_context["scopes"] == [
            "read:users",
            "write:agents",
        ], "Should have API key scopes"
        print("✅ get_auth_context works with APIKey")

        # Test is_user_auth and is_api_key_auth
        assert is_user_auth(user), "Should identify user correctly"
        assert not is_user_auth(api_key), "Should not identify API key as user"
        assert is_api_key_auth(api_key), "Should identify API key correctly"
        assert not is_api_key_auth(user), "Should not identify user as API key"
        print("✅ is_user_auth and is_api_key_auth work correctly")

        # Test get_organization_id
        user_org_id = get_organization_id(user)
        api_key_org_id = get_organization_id(api_key)
        assert user_org_id == str(user.organization_id), "Should get user org ID"
        assert api_key_org_id == str(
            api_key.organization_id
        ), "Should get API key org ID"
        print("✅ get_organization_id works correctly")

        # Test get_user_id
        user_id = get_user_id(user)
        api_key_user_id = get_user_id(api_key)
        assert user_id == str(user.id), "Should get user ID"
        assert api_key_user_id == str(api_key.user_id), "Should get API key's user ID"
        print("✅ get_user_id works correctly")

        return True

    except Exception as e:
        print(f"❌ Error testing helper functions: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_mixed_auth_dependency():
    """Test mixed authentication dependency creation"""
    print("\n🔀 Testing Mixed Auth Dependency...")

    try:
        from aiplanet_platform.core.enhanced_security import (
            create_mixed_auth_dependency,
        )

        # Test creating mixed auth dependency
        mixed_auth = create_mixed_auth_dependency("read:users", "write:agents")
        assert callable(mixed_auth), "Should return a callable dependency"
        print("✅ create_mixed_auth_dependency works")

        # Test with no scopes
        mixed_auth_no_scopes = create_mixed_auth_dependency()
        assert callable(mixed_auth_no_scopes), "Should work with no scopes"
        print("✅ Mixed auth dependency with no scopes works")

        return True

    except Exception as e:
        print(f"❌ Error testing mixed auth dependency: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_scope_checking_logic():
    """Test scope checking logic without FastAPI dependencies"""
    print("\n✅ Testing Scope Checking Logic...")

    try:
        # Test the scope checking logic directly
        user = create_realistic_user_mock()
        api_key = create_realistic_api_key_mock()

        # Test API key scope checking (direct method)
        assert api_key.has_scope("read:users"), "API key should have read:users scope"
        assert api_key.has_scope(
            "write:agents"
        ), "API key should have write:agents scope"
        assert not api_key.has_scope("admin"), "API key should not have admin scope"
        print("✅ API key scope checking methods work")

        # Test that we can identify auth types correctly
        from aiplanet_platform.core.enhanced_security import (
            is_user_auth,
            is_api_key_auth,
        )

        # For users, they should have all permissions (as per the logic)
        if is_user_auth(user):
            print("✅ User identified correctly (users have all permissions)")

        # For API keys, check scopes
        if is_api_key_auth(api_key):
            required_scopes = ["read:users", "write:agents"]
            has_all_scopes = all(api_key.has_scope(scope) for scope in required_scopes)
            assert has_all_scopes, "API key should have all required scopes"
            print("✅ API key scope validation logic works")

        return True

    except Exception as e:
        print(f"❌ Error testing scope checking logic: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run Step 3A tests"""
    print("🚀 Running Step 3A Tests: Enhanced Security Functions (Fixed)\n")

    tests_passed = 0
    total_tests = 5

    # Run tests
    if test_enhanced_security_import():
        tests_passed += 1

    if test_api_key_auth_class():
        tests_passed += 1

    if test_helper_functions():
        tests_passed += 1

    if test_mixed_auth_dependency():
        tests_passed += 1

    if test_scope_checking_logic():
        tests_passed += 1

    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 All Step 3A tests passed!")
        print("\n✅ Step 3A is complete!")
        print("📝 What's working:")
        print("   ✅ Enhanced security functions import correctly")
        print("   ✅ APIKeyAuth class works with scopes")
        print("   ✅ Helper functions work with realistic mocks")
        print("   ✅ Mixed authentication dependency creation works")
        print("   ✅ Scope checking logic works correctly")
        print("\n🔧 What you can now do:")
        print("   ✅ Authenticate users with JWT (existing)")
        print("   ✅ Authenticate third-party apps with API keys (new)")
        print("   ✅ Use mixed authentication (both JWT and API keys)")
        print("   ✅ Enforce scope-based permissions")
        print("   ✅ Get authentication context information")
        print("\n🚀 Ready for Step 3B: Rate Limiting Middleware")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues before proceeding.")
        return False


if __name__ == "__main__":
    main()
