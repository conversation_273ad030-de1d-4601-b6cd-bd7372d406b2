#!/usr/bin/env python3
"""
Comprehensive test script for team template functionality.
Tests creating, reading, updating, deleting templates and creating teams from templates.
"""

import uuid
from typing import Dict, Any
from sqlalchemy.orm import Session
from aiplanet_platform.core.database import get_db
from aiplanet_platform.services.team_template_service import TeamTemplateService

# Sample organization ID (in real use, this would come from authenticated user)
SAMPLE_ORG_ID = "28320941-af5a-4e67-b2bb-c93e0498e455"


def create_sample_templates() -> Dict[str, Dict[str, Any]]:
    """Create sample template data for testing"""

    # Template 1: Customer Service Team
    customer_service_template = {
        "name": "Customer Service Team V2",
        "description": "A template for handling customer inquiries and support",
        "category": "customer_service",
        "use_case": "Handle customer inquiries, complaints, and support requests",
        "tags": ["customer_service", "support", "chat"],
        "template_config": {
            "team": {
                "label": "Customer Service Team",
                "component_type": "team",
                "component_version": 1,
                "version": 1,
                "provider": "autogen_agentchat.teams.RoundRobinGroupChat",
                "description": "A round-robin customer service team",
                "config": {
                    "max_turns": 10,
                    "emit_team_events": False,
                    "participants": [
                        {
                            "label": "support_agent",
                            "component_type": "agent",
                            "component_version": 1,
                            "version": 1,
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "description": "Customer support specialist",
                            "config": {
                                "name": "support_agent",
                                "system_message": "You are a helpful customer service agent. Be polite, professional, and solution-oriented.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": False,
                                "tool_call_summary_format": "{result}",
                                "model_client": {
                                    "label": "azu",
                                    "config": {
                                        "model": "gpt-4o-mini",
                                        "api_key": "apikeyapikeyapikeyapikeyapikeyapikeyapikeyapikeyapikey",
                                        "max_tokens": 1000,
                                        "api_version": "2025-01-01-preview",
                                        "temperature": 0.7,
                                        "azure_endpoint": "https://newaiplatform.openai.azure.com/",
                                        "azure_deployment": "aiplatform",
                                    },
                                    "version": 1,
                                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                                    "description": "Chat completion client for Azure OpenAI hosted models.",
                                    "component_type": "model",
                                    "component_version": 1,
                                },
                                "model_context": {
                                    "label": "UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "component_version": 1,
                                    "version": 1,
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "description": "Unbounded chat context",
                                    "config": {},
                                },
                                "tools": [
                                    {
                                        "label": "knowledge_search",
                                        "component_type": "tool",
                                        "component_version": 1,
                                        "version": 1,
                                        "provider": "autogen_core.tools.FunctionTool",
                                        "description": "Search knowledge base for answers",
                                        "config": {
                                            "name": "knowledge_search",
                                            "description": "Search the knowledge base for relevant information",
                                            "source_code": "async def knowledge_search(query: str) -> str:\n    # Simulate knowledge base search\n    return f'Found relevant information for: {query}'",
                                            "global_imports": [],
                                            "has_cancellation_support": False,
                                        },
                                    }
                                ],
                            },
                        }
                    ],
                    "termination_condition": {
                        "label": "MaxMessageTermination",
                        "component_type": "termination",
                        "component_version": 1,
                        "version": 1,
                        "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                        "description": "Stop after maximum messages",
                        "config": {"max_messages": 20, "include_agent_event": False},
                    },
                },
            },
            "inputs": [
                {
                    "label": "TextInput",
                    "config": {
                        "content": "Hello, world!",
                        "encoding": "utf-8",
                        "required": True,
                    },
                    "version": 1,
                    "provider": "autogen_core.io.TextInput",
                    "description": "Text input component for direct text input",
                    "component_type": "input",
                    "component_version": 1,
                }
            ],
            "output": "eb10bf30-f3e7-4efd-b7d7-9caf0f37e113",
        },
    }

    # Template 2: Code Review Team
    code_review_template = {
        "name": "Code Review Team V2",
        "description": "A team specialized in reviewing code and providing feedback",
        "category": "development",
        "use_case": "Review pull requests and provide constructive feedback",
        "tags": ["code_review", "development", "quality"],
        "template_config": {
            "team": {
                "label": "Code Review Team",
                "component_type": "team",
                "component_version": 1,
                "version": 1,
                "provider": "autogen_agentchat.teams.SelectorGroupChat",
                "description": "A selector-based code review team",
                "config": {
                    "max_turns": 15,
                    "emit_team_events": False,
                    "max_selector_attempts": 3,
                    "allow_repeated_speaker": False,
                    "model_client_streaming": False,
                    "selector_prompt": "You are a code reviewer. You will be given a code snippet and you will need to review it and provide a review. You will also be given a list of tools that you can use to help you review the code. You will need to use the tools to review the code and provide a review. You will also be given a list of tools that you can use to help you review the code. You will need to use the tools to review the code and provide a review.",
                    "model_client": {
                        "label": "azu",
                        "config": {
                            "model": "gpt-4o-mini",
                            "api_key": "apikeyapikeyapikeyapikeyapikeyapikeyapikeyapikeyapikey",
                            "max_tokens": 1000,
                            "api_version": "2025-01-01-preview",
                            "temperature": 0.7,
                            "azure_endpoint": "https://newaiplatform.openai.azure.com/",
                            "azure_deployment": "aiplatform",
                        },
                        "version": 1,
                        "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                        "description": "Chat completion client for Azure OpenAI hosted models.",
                        "component_type": "model",
                        "component_version": 1,
                    },
                    "participants": [
                        {
                            "label": "senior_reviewer",
                            "component_type": "agent",
                            "component_version": 1,
                            "version": 1,
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "description": "Senior code reviewer",
                            "config": {
                                "name": "senior_reviewer",
                                "system_message": "You are a senior software engineer. Review code for best practices, security, and maintainability.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": True,
                                "tool_call_summary_format": "{result}",
                                "model_client": {
                                    "label": "azu",
                                    "config": {
                                        "model": "gpt-4o-mini",
                                        "api_key": "apikeyapikeyapikeyapikeyapikeyapikeyapikeyapikeyapikey",
                                        "max_tokens": 1000,
                                        "api_version": "2025-01-01-preview",
                                        "temperature": 0.7,
                                        "azure_endpoint": "https://newaiplatform.openai.azure.com/",
                                        "azure_deployment": "aiplatform",
                                    },
                                    "version": 1,
                                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                                    "description": "Chat completion client for Azure OpenAI hosted models.",
                                    "component_type": "model",
                                    "component_version": 1,
                                },
                                "model_context": {
                                    "label": "UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "component_version": 1,
                                    "version": 1,
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "description": "Unbounded chat context",
                                    "config": {},
                                },
                                "tools": [
                                    {
                                        "label": "code_analyzer",
                                        "component_type": "tool",
                                        "component_version": 1,
                                        "version": 1,
                                        "provider": "autogen_core.tools.FunctionTool",
                                        "description": "Analyze code for issues",
                                        "config": {
                                            "name": "code_analyzer",
                                            "description": "Analyze code for potential issues and improvements",
                                            "source_code": "async def code_analyzer(code: str) -> str:\n    # Simulate code analysis\n    return f'Code analysis complete. Found potential improvements in: {code[:50]}...'",
                                            "global_imports": [],
                                            "has_cancellation_support": False,
                                        },
                                    }
                                ],
                            },
                        },
                        {
                            "label": "security_reviewer",
                            "component_type": "agent",
                            "component_version": 1,
                            "version": 1,
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "description": "Security-focused reviewer",
                            "config": {
                                "name": "security_reviewer",
                                "system_message": "You are a security specialist. Focus on identifying security vulnerabilities and suggesting fixes.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": True,
                                "tool_call_summary_format": "{result}",
                                "model_client": {
                                    "label": "azu",
                                    "config": {
                                        "model": "gpt-4o-mini",
                                        "api_key": "apikeyapikeyapikeyapikeyapikeyapikeyapikeyapikeyapikey",
                                        "max_tokens": 1000,
                                        "api_version": "2025-01-01-preview",
                                        "temperature": 0.7,
                                        "azure_endpoint": "https://newaiplatform.openai.azure.com/",
                                        "azure_deployment": "aiplatform",
                                    },
                                    "version": 1,
                                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                                    "description": "Chat completion client for Azure OpenAI hosted models.",
                                    "component_type": "model",
                                    "component_version": 1,
                                },
                                "model_context": {
                                    "label": "UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "component_version": 1,
                                    "version": 1,
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "description": "Unbounded chat context",
                                    "config": {},
                                },
                                "tools": [
                                    {
                                        "label": "security_scanner",
                                        "component_type": "tool",
                                        "component_version": 1,
                                        "version": 1,
                                        "provider": "autogen_core.tools.FunctionTool",
                                        "description": "Scan for security vulnerabilities",
                                        "config": {
                                            "name": "security_scanner",
                                            "description": "Scan code for security vulnerabilities",
                                            "source_code": "async def security_scanner(code: str) -> str:\n    # Simulate security scan\n    return f'Security scan complete. No critical vulnerabilities found in: {code[:50]}...'",
                                            "global_imports": [],
                                            "has_cancellation_support": False,
                                        },
                                    }
                                ],
                            },
                        },
                    ],
                    "termination_condition": {
                        "label": "MaxMessageTermination",
                        "component_type": "termination",
                        "component_version": 1,
                        "version": 1,
                        "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                        "description": "Stop after maximum messages",
                        "config": {"max_messages": 25, "include_agent_event": False},
                    },
                },
            },
            "inputs": [
                {
                    "label": "TextInput",
                    "config": {
                        "content": "Hello, world!",
                        "encoding": "utf-8",
                        "required": True,
                    },
                    "version": 1,
                    "provider": "autogen_core.io.TextInput",
                    "description": "Text input component for direct text input",
                    "component_type": "input",
                    "component_version": 1,
                }
            ],
            "output": "eb10bf30-f3e7-4efd-b7d7-9caf0f37e113",
        },
    }

    # Template 3: Research Team
    research_template = {
        "name": "Research Team V2",
        "description": "A team for conducting research and analysis",
        "category": "research",
        "use_case": "Conduct comprehensive research on topics and provide insights",
        "tags": ["research", "analysis", "insights"],
        "template_config": {
            "team": {
                "label": "Research Team",
                "component_type": "team",
                "component_version": 1,
                "version": 1,
                "provider": "autogen_agentchat.teams.RoundRobinGroupChat",
                "description": "A research-focused team",
                "config": {
                    "max_turns": 20,
                    "emit_team_events": False,
                    "participants": [
                        {
                            "label": "research_analyst",
                            "component_type": "agent",
                            "component_version": 1,
                            "version": 1,
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "description": "Research analyst",
                            "config": {
                                "name": "research_analyst",
                                "system_message": "You are a research analyst. Provide thorough, well-sourced analysis on topics.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": True,
                                "tool_call_summary_format": "{result}",
                                "model_client": {
                                    "label": "azu",
                                    "config": {
                                        "model": "gpt-4o-mini",
                                        "api_key": "apikeyapikeyapikeyapikeyapikeyapikeyapikeyapikeyapikey",
                                        "max_tokens": 1000,
                                        "api_version": "2025-01-01-preview",
                                        "temperature": 0.7,
                                        "azure_endpoint": "https://newaiplatform.openai.azure.com/",
                                        "azure_deployment": "aiplatform",
                                    },
                                    "version": 1,
                                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                                    "description": "Chat completion client for Azure OpenAI hosted models.",
                                    "component_type": "model",
                                    "component_version": 1,
                                },
                                "model_context": {
                                    "label": "UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "component_version": 1,
                                    "version": 1,
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "description": "Unbounded chat context",
                                    "config": {},
                                },
                                "tools": [
                                    {
                                        "label": "web_search",
                                        "component_type": "tool",
                                        "component_version": 1,
                                        "version": 1,
                                        "provider": "autogen_core.tools.FunctionTool",
                                        "description": "Search the web for information",
                                        "config": {
                                            "name": "web_search",
                                            "description": "Search the web for current information",
                                            "source_code": "async def web_search(query: str) -> str:\n    # Simulate web search\n    return f'Found relevant web results for: {query}'",
                                            "global_imports": [],
                                            "has_cancellation_support": False,
                                        },
                                    }
                                ],
                            },
                        }
                    ],
                    "termination_condition": {
                        "label": "MaxMessageTermination",
                        "component_type": "termination",
                        "component_version": 1,
                        "version": 1,
                        "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                        "description": "Stop after maximum messages",
                        "config": {"max_messages": 30, "include_agent_event": False},
                    },
                },
            },
            "inputs": [
                {
                    "label": "TextInput",
                    "config": {
                        "content": "Hello, world!",
                        "encoding": "utf-8",
                        "required": True,
                    },
                    "version": 1,
                    "provider": "autogen_core.io.TextInput",
                    "description": "Text input component for direct text input",
                    "component_type": "input",
                    "component_version": 1,
                }
            ],
            "output": "eb10bf30-f3e7-4efd-b7d7-9caf0f37e113",
        },
    }

    # Template 4: Multi-Provider Analysis Team
    multi_provider_template = {
        "name": "Multi-Provider Analysis Team V2",
        "description": "A team with mixed AI providers for comprehensive analysis",
        "category": "analysis",
        "use_case": "Leverage different AI models for diverse analytical perspectives",
        "tags": ["analysis", "multi-provider", "diverse"],
        "template_config": {
            "team": {
                "label": "Multi-Provider Analysis Team",
                "component_type": "team",
                "component_version": 1,
                "version": 1,
                "provider": "autogen_agentchat.teams.SelectorGroupChat",
                "description": "A selector-based team with multiple AI providers",
                "config": {
                    "max_turns": 12,
                    "emit_team_events": False,
                    "max_selector_attempts": 3,
                    "allow_repeated_speaker": False,
                    "model_client_streaming": False,
                    "selector_prompt": "You are a team coordinator. Select the most appropriate agent based on the task requirements and previous conversation context.",
                    "model_client": {
                        "label": "team_azure_gpt4",
                        "config": {
                            "model": "gpt-4",
                            "api_key": "apikeyapikeyapikeyapikeyapikeyapikeyapikeyapikeyapikey",
                            "max_tokens": 1500,
                            "api_version": "2025-01-01-preview",
                            "temperature": 0.3,
                            "azure_endpoint": "https://newaiplatform.openai.azure.com/",
                            "azure_deployment": "aiplatform",
                        },
                        "version": 1,
                        "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                        "description": "Azure GPT-4 for team coordination",
                        "component_type": "model",
                        "component_version": 1,
                    },
                    "participants": [
                        {
                            "label": "azure_analyst",
                            "component_type": "agent",
                            "component_version": 1,
                            "version": 1,
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "description": "Azure-powered data analyst",
                            "config": {
                                "name": "azure_analyst",
                                "system_message": "You are a data analyst specializing in quantitative analysis and statistical insights.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": True,
                                "tool_call_summary_format": "{result}",
                                "model_client": {
                                    "label": "azure_gpt35_turbo",
                                    "config": {
                                        "model": "gpt-35-turbo",
                                        "api_key": "apikeyapikeyapikeyapikeyapikeyapikeyapikeyapikeyapikey",
                                        "max_tokens": 1200,
                                        "api_version": "2025-01-01-preview",
                                        "temperature": 0.5,
                                        "azure_endpoint": "https://newaiplatform.openai.azure.com/",
                                        "azure_deployment": "aiplatform",
                                    },
                                    "version": 1,
                                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                                    "description": "Azure GPT-3.5 Turbo for data analysis",
                                    "component_type": "model",
                                    "component_version": 1,
                                },
                                "model_context": {
                                    "label": "UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "component_version": 1,
                                    "version": 1,
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "description": "Unbounded chat context",
                                    "config": {},
                                },
                            },
                        },
                        {
                            "label": "openai_strategist",
                            "component_type": "agent",
                            "component_version": 1,
                            "version": 1,
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "description": "OpenAI-powered strategic advisor",
                            "config": {
                                "name": "openai_strategist",
                                "system_message": "You are a strategic advisor focused on business insights and recommendations.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": True,
                                "tool_call_summary_format": "{result}",
                                "model_client": {
                                    "label": "openai_gpt4_turbo",
                                    "config": {
                                        "model": "gpt-4-turbo",
                                        "api_key": "sk-placeholder-openai-key-123",
                                        "max_tokens": 2000,
                                        "temperature": 0.7,
                                    },
                                    "version": 1,
                                    "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                                    "description": "OpenAI GPT-4 Turbo for strategic analysis",
                                    "component_type": "model",
                                    "component_version": 1,
                                },
                                "model_context": {
                                    "label": "UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "component_version": 1,
                                    "version": 1,
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "description": "Unbounded chat context",
                                    "config": {},
                                },
                            },
                        },
                        {
                            "label": "openai_creative",
                            "component_type": "agent",
                            "component_version": 1,
                            "version": 1,
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "description": "OpenAI-powered creative specialist",
                            "config": {
                                "name": "openai_creative",
                                "system_message": "You are a creative specialist focused on innovative solutions and out-of-the-box thinking.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": False,
                                "tool_call_summary_format": "{result}",
                                "model_client": {
                                    "label": "openai_gpt4o_mini",
                                    "config": {
                                        "model": "gpt-4o-mini",
                                        "api_key": "sk-placeholder-openai-key-456",
                                        "max_tokens": 1000,
                                        "temperature": 0.9,
                                    },
                                    "version": 1,
                                    "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                                    "description": "OpenAI GPT-4o Mini for creative insights",
                                    "component_type": "model",
                                    "component_version": 1,
                                },
                                "model_context": {
                                    "label": "UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "component_version": 1,
                                    "version": 1,
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "description": "Unbounded chat context",
                                    "config": {},
                                },
                            },
                        },
                    ],
                    "termination_condition": {
                        "label": "MaxMessageTermination",
                        "component_type": "termination",
                        "component_version": 1,
                        "version": 1,
                        "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                        "description": "Stop after maximum messages",
                        "config": {"max_messages": 15, "include_agent_event": False},
                    },
                },
            },
            "inputs": [
                {
                    "label": "TextInput",
                    "config": {
                        "content": "Hello, world!",
                        "encoding": "utf-8",
                        "required": True,
                    },
                    "version": 1,
                    "provider": "autogen_core.io.TextInput",
                    "description": "Text input component for direct text input",
                    "component_type": "input",
                    "component_version": 1,
                }
            ],
            "output": "eb10bf30-f3e7-4efd-b7d7-9caf0f37e113",
        },
    }

    return {
        "customer_service": customer_service_template,
        "code_review": code_review_template,
        "research": research_template,
        "multi_provider": multi_provider_template,
    }


def test_template_operations(db: Session):
    """Test CRUD operations and team creation from templates"""
    print("\n=== Testing Template Operations ===")

    service = TeamTemplateService(db)
    templates = create_sample_templates()
    created_templates = []

    # Test CREATE
    print("\n1. Testing template creation...")
    for template_key, template_data in templates.items():
        # Set to None for global templates, or set to specific org UUID for org-specific templates
        template_data["organization_id"] = None  # Global templates visible to all users

        try:
            created_template = service.create_template(template_data)
            created_templates.append(created_template)
            print(
                f"✓ Created template: {created_template.name} (ID: {created_template.id})"
            )
            # Verify nested structure
            assert "team" in created_template.template_config
            assert "inputs" in created_template.template_config
            assert "output" in created_template.template_config
            print(f"  ✓ Verified nested structure for {created_template.name}")
        except Exception as e:
            print(f"✗ Error creating template {template_key}: {e}")
            raise

    # Test READ
    print("\n2. Testing template retrieval...")
    try:
        all_templates = service.fetch_templates_by_filters({})
        print(f"✓ Retrieved {len(all_templates)} templates")

        if created_templates:
            first_template_id = created_templates[0].id
            retrieved_template = service.fetch_template_by_id(first_template_id)
            print(f"✓ Retrieved template by ID: {retrieved_template.name}")
            assert retrieved_template.id == first_template_id
    except Exception as e:
        print(f"✗ Error retrieving templates: {e}")
        raise

    # Test Team Creation from Template
    print("\n3. Testing team creation from template...")
    if created_templates:
        for template_to_use in created_templates:
            try:
                team_name = f"Team from {template_to_use.name}"
                team = service.create_team_from_template(
                    template_id=template_to_use.id,
                    organization_id=uuid.UUID(SAMPLE_ORG_ID),
                    name_override=team_name,
                )
                print(
                    f"✓ Created team '{team.component.get('label')}' from template '{template_to_use.name}'"
                )

                # Verify inputs and outputs were created for the team
                if template_to_use.template_config.get("inputs"):
                    assert len(team.team_inputs) == len(
                        template_to_use.template_config["inputs"]
                    )
                    print(
                        f"  ✓ Verified {len(team.team_inputs)} inputs created for the team."
                    )

                if template_to_use.template_config.get("outputs"):
                    assert len(team.team_outputs) == len(
                        template_to_use.template_config["outputs"]
                    )
                    print(
                        f"  ✓ Verified {len(team.team_outputs)} outputs created for the team."
                    )

            except Exception as e:
                print(
                    f"✗ Error creating team from template '{template_to_use.name}': {e}"
                )
                import traceback

                traceback.print_exc()
                raise


def test_api_key_analysis(db: Session):
    """Test API key analysis functionality"""
    print("\n=== Testing API Key Analysis ===")

    service = TeamTemplateService(db)
    templates = create_sample_templates()

    # Create a template with known API key structure
    print("\n1. Creating template for API key analysis...")
    template_data = templates["code_review"].copy()
    template_data["organization_id"] = None

    try:
        created_template = service.create_template(template_data)
        print(
            f"✓ Created template: {created_template.name} (ID: {created_template.id})"
        )

        # Test API requirements analysis
        print("\n2. Testing API requirements analysis...")
        api_requirements = service.analyze_template_api_requirements(
            created_template.id
        )

        print(f"✓ API analysis completed for template: {created_template.name}")
        print(f"  Required providers: {api_requirements['required_providers']}")
        print(
            f"  Agent requirements count: {len(api_requirements['agent_requirements'])}"
        )
        print(
            f"  Team requirements: {'Yes' if api_requirements['team_requirements'] else 'No'}"
        )

        # Verify expected results
        assert api_requirements[
            "required_providers"
        ], "Should detect required providers"
        assert (
            "azure_openai" in api_requirements["required_providers"]
        ), "Should detect Azure OpenAI"
        assert api_requirements["agent_requirements"], "Should find agent requirements"

        # Check first agent requirement details
        first_agent = api_requirements["agent_requirements"][0]
        print(f"  First agent provider: {first_agent['provider_name']}")
        print(f"  First agent required fields: {first_agent['required_fields']}")
        print(f"  First agent model: {first_agent['model']}")

        assert (
            first_agent["provider_type"] == "azure_openai"
        ), "Should identify as Azure OpenAI"
        assert "api_key" in first_agent["required_fields"], "Should require API key"
        assert (
            "azure_endpoint" in first_agent["required_fields"]
        ), "Should require Azure endpoint"
        assert (
            "azure_deployment" in first_agent["required_fields"]
        ), "Should require Azure deployment"

        print("  ✓ All API requirement analysis assertions passed")

    except Exception as e:
        print(f"✗ Error in API key analysis test: {e}")
        import traceback

        traceback.print_exc()
        raise


def test_api_key_customizations(db: Session):
    """Test API key customizations generation"""
    print("\n=== Testing API Key Customizations ===")

    service = TeamTemplateService(db)
    templates = create_sample_templates()

    # Test with code review template (has team-level model client)
    print("\n1. Testing with SelectorGroupChat template (team + agent models)...")
    template_data = templates[
        "code_review"
    ].copy()  # This has both team and agent model clients
    template_data["organization_id"] = None

    try:
        created_template = service.create_template(template_data)
        print(f"✓ Created template: {created_template.name}")

        # Get API requirements
        api_requirements = service.analyze_template_api_requirements(
            created_template.id
        )
        print(
            f"✓ Found {len(api_requirements['required_providers'])} required providers"
        )
        print(
            f"  Team requirements: {'Yes' if api_requirements['team_requirements'] else 'No'}"
        )
        print(f"  Agent requirements: {len(api_requirements['agent_requirements'])}")

        # Test customizations generation
        print("\n2. Testing customizations generation...")
        test_api_keys = {
            "azure_openai": {
                "api_key": "test-api-key-123",
                "model": "gpt-4o",
                "azure_endpoint": "https://test.openai.azure.com/",
                "azure_deployment": "test-deployment",
                "api_version": "2024-02-01",
            }
        }

        customizations = service.generate_api_key_customizations(
            created_template.id, test_api_keys
        )
        print("✓ Generated customizations successfully")

        # Verify customizations structure
        assert "config" in customizations, "Should have config section"

        # Check team-level model client customization
        if api_requirements["team_requirements"]:
            assert (
                "model_client" in customizations["config"]
            ), "Should have team model_client customization"
            team_model_config = customizations["config"]["model_client"]["config"]
            assert (
                team_model_config["api_key"] == "test-api-key-123"
            ), "Should substitute team API key"
            print("  ✓ Team-level model client customization verified")

        # Check agent-level model client customizations
        if api_requirements["agent_requirements"]:
            assert (
                "participants" in customizations["config"]
            ), "Should have participants customizations"
            participants = customizations["config"]["participants"]
            assert len(participants) >= len(
                api_requirements["agent_requirements"]
            ), "Should have customizations for all agents with models"

            # Check first agent customization
            first_participant = participants[0]
            assert "config" in first_participant, "Should have agent config section"
            assert (
                "model_client" in first_participant["config"]
            ), "Should have model_client section"
            agent_model_config = first_participant["config"]["model_client"]["config"]
            assert (
                agent_model_config["api_key"] == "test-api-key-123"
            ), "Should substitute agent API key"
            print("  ✓ Agent-level model client customizations verified")

        print("  ✓ All customization generation assertions passed")

    except Exception as e:
        print(f"✗ Error in API key customizations test: {e}")
        import traceback

        traceback.print_exc()
        raise


def test_team_creation_with_api_keys(db: Session):
    """Test creating team from template with API key customizations"""
    print("\n=== Testing Team Creation with API Keys ===")

    service = TeamTemplateService(db)
    templates = create_sample_templates()

    print("\n1. Creating template for team creation test...")
    template_data = templates["customer_service"].copy()
    template_data["organization_id"] = None

    try:
        created_template = service.create_template(template_data)
        print(f"✓ Created template: {created_template.name}")

        # Generate customizations with new API keys
        print("\n2. Generating API key customizations...")
        test_api_keys = {
            "azure_openai": {
                "api_key": "custom-user-api-key-456",
                "model": "gpt-4o",
                "azure_endpoint": "https://user.openai.azure.com/",
                "azure_deployment": "user-deployment",
                "api_version": "2024-02-01",
            }
        }

        customizations = service.generate_api_key_customizations(
            created_template.id, test_api_keys
        )
        print(f"Customizations: {customizations}")
        print("✓ Generated customizations with user API keys")

        # Create team from template with customizations
        print("\n3. Creating team from template with custom API keys...")
        team = service.create_team_from_template(
            template_id=created_template.id,
            organization_id=uuid.UUID(SAMPLE_ORG_ID),
            name_override="Test Team with Custom API Keys",
            customizations=customizations,
        )

        print(f"✓ Created team: {team.component.get('label')}")

        # Verify the team was created with substituted API keys
        team_config = team.component
        participants = team_config.get("config", {}).get("participants", [])

        if participants:
            first_participant = participants[0]
            model_client = first_participant.get("config", {}).get("model_client", {})
            model_config = model_client.get("config", {})

            # Verify the API key was substituted
            assert (
                model_config.get("api_key") == "custom-user-api-key-456"
            ), "Should use custom user API key"
            assert (
                model_config.get("azure_endpoint") == "https://user.openai.azure.com/"
            ), "Should use custom endpoint"
            assert (
                model_config.get("azure_deployment") == "user-deployment"
            ), "Should use custom deployment"

            print("  ✓ Verified team was created with custom API keys")
            print(f"    API Key: {model_config.get('api_key')[:20]}...")
            print(f"    Endpoint: {model_config.get('azure_endpoint')}")
            print(f"    Deployment: {model_config.get('azure_deployment')}")

        print("  ✓ All team creation with API keys assertions passed")

    except Exception as e:
        print(f"✗ Error in team creation with API keys test: {e}")
        import traceback

        traceback.print_exc()
        raise


def test_delete_template(db: Session):
    """Test deleting a template"""
    print("\n=== Testing Template Deletion ===")

    service = TeamTemplateService(db)
    # Delete specific templates by UUID
    template_uuids_to_delete = [
        "95cf389c-92b9-4d41-a4ef-bbb5ee076158",
        "0cb11a36-fb2f-4c55-a69c-71e19d158a51",
        "8214c4cf-a0ba-424c-893d-22b22e7158fd",
    ]

    deleted_count = 0
    for uuid_str in template_uuids_to_delete:
        try:
            template_id = uuid.UUID(uuid_str)
            success = service.delete_template_by_id(template_id)
            if success:
                deleted_count += 1
                print(f"✓ Deleted template with ID: {uuid_str}")
            else:
                print(f"⚠ Template not found or already deleted: {uuid_str}")
        except ValueError as e:
            print(f"✗ Invalid UUID format: {uuid_str} - {e}")
        except Exception as e:
            print(f"✗ Error deleting template {uuid_str}: {e}")

    print(
        f"\n📊 Successfully deleted {deleted_count} out of {len(template_uuids_to_delete)} templates"
    )


def main():
    """Main test function"""
    print("🚀 Starting Team Template Testing")
    print("=" * 50)

    db = next(get_db())

    try:
        # Run all tests
        test_template_operations(db)
        # test_api_key_analysis(db)
        # test_api_key_customizations(db)
        # test_team_creation_with_api_keys(db)

        print("\n" + "=" * 50)
        print("🎉 All tests completed successfully!")

        # Final summary
        service = TeamTemplateService(db)
        total_templates = len(service.fetch_templates_by_filters({}))
        print(f"📊 Total templates in database: {total_templates}")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()

    finally:
        db.close()


if __name__ == "__main__":
    main()
    # db = next(get_db())
    # test_delete_template(db)
