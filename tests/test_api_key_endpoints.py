"""
Test for API Key Management Router
"""
# ruff: noqa: F401
import sys
import os
from datetime import datetime
from uuid import uuid4

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_router_import():
    """Test that API Key router can be imported"""
    print("📦 Testing API Key Router Import...")

    try:
        from aiplanet_platform.routers.v1.private.api_key import router

        print("✅ API Key router imported successfully")

        # Check that router has the expected attributes
        assert hasattr(router, "prefix"), "Router should have prefix"
        assert hasattr(router, "tags"), "Router should have tags"
        assert router.prefix == "/api-keys", "Router should have correct prefix"
        assert "API Keys" in router.tags, "Router should have correct tags"
        print("✅ Router configuration is correct")

        # Check that router has routes
        routes = router.routes
        assert len(routes) > 0, "Router should have routes"
        print(f"✅ Router has {len(routes)} routes")

        return True

    except ImportError as e:
        print(f"❌ Failed to import API Key router: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing router import: {e}")
        return False


def test_router_endpoints():
    """Test that router has expected endpoints"""
    print("\n🛤️  Testing Router Endpoints...")

    try:
        from aiplanet_platform.routers.v1.private.api_key import router

        # Get all route paths and methods
        routes_info = []
        for route in router.routes:
            if hasattr(route, "path") and hasattr(route, "methods"):
                for method in route.methods:
                    routes_info.append((method, route.path))

        # Expected endpoints
        expected_endpoints = [
            ("POST", "/api-keys/"),  # Create API key
            ("GET", "/api-keys/"),  # List API keys
            ("GET", "/api-keys/{key_id}"),  # Get API key
            ("PUT", "/api-keys/{key_id}"),  # Update API key
            ("DELETE", "/api-keys/{key_id}"),  # Revoke API key
            ("GET", "/api-keys/{key_id}/usage"),  # Get usage stats
            ("GET", "/api-keys/scopes/available"),  # Get available scopes
            ("POST", "/api-keys/{key_id}/regenerate"),  # Regenerate API key
        ]

        print("📋 Expected endpoints:")
        for method, path in expected_endpoints:
            print(f"   {method} {path}")

        print("\n📋 Actual endpoints:")
        for method, path in routes_info:
            print(f"   {method} {path}")

        # Check that we have the main endpoints (some might be slightly different)
        has_create = any(
            "POST" in method and "/api-keys/" in path for method, path in routes_info
        )
        has_list = any(
            "GET" in method and path == "/api-keys/" for method, path in routes_info
        )
        has_get = any(
            "GET" in method
            and "{key_id}" in path
            and "usage" not in path
            and "scopes" not in path
            for method, path in routes_info
        )
        has_update = any(
            "PUT" in method and "{key_id}" in path for method, path in routes_info
        )
        has_delete = any(
            "DELETE" in method and "{key_id}" in path for method, path in routes_info
        )

        assert has_create, "Should have create endpoint"
        assert has_list, "Should have list endpoint"
        assert has_get, "Should have get endpoint"
        assert has_update, "Should have update endpoint"
        assert has_delete, "Should have delete endpoint"

        print("✅ All main endpoints are present")
        return True

    except Exception as e:
        print(f"❌ Error testing router endpoints: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_api_key_schemas():
    """Test that API key schemas work correctly"""
    print("\n📋 Testing API Key Schemas...")

    try:
        from aiplanet_platform.schemas.api_key import (
            APIKeyCreate,
            APIKeyUpdate,
            APIKeyScopeInfo,
        )

        # Test APIKeyCreate schema
        create_data = {
            "name": "Test API Key",
            "description": "A test key",
            "scopes": ["read:users", "write:agents"],
            "rate_limit_per_minute": 100,
        }
        create_schema = APIKeyCreate(**create_data)
        assert create_schema.name == "Test API Key"
        assert "read:users" in create_schema.scopes
        print("✅ APIKeyCreate schema works")

        # Test APIKeyUpdate schema
        update_data = {"name": "Updated API Key", "is_active": False}
        update_schema = APIKeyUpdate(**update_data)
        assert update_schema.name == "Updated API Key"
        assert not update_schema.is_active
        print("✅ APIKeyUpdate schema works")

        # Test APIKeyScopeInfo schema
        scope_data = {
            "scope": "read:users",
            "description": "Read user information",
            "category": "Users",
        }
        scope_schema = APIKeyScopeInfo(**scope_data)
        assert scope_schema.scope == "read:users"
        print("✅ APIKeyScopeInfo schema works")

        return True

    except Exception as e:
        print(f"❌ Error testing schemas: {e}")
        import traceback

        traceback.print_exc()
        return False


def create_mock_user():
    """Create a mock user for testing"""
    try:
        from aiplanet_platform.models.user import User

        class MockUser(User):
            def __init__(self):
                self.id = uuid4()
                self.email = "<EMAIL>"
                self.organization_id = uuid4()

        return MockUser()
    except Exception:
        # Fallback mock
        class MockUser:
            def __init__(self):
                self.id = uuid4()
                self.email = "<EMAIL>"
                self.organization_id = uuid4()

        return MockUser()


def create_mock_api_key():
    """Create a mock API key for testing"""
    try:
        from aiplanet_platform.models.api_key import APIKey

        class MockAPIKey(APIKey):
            def __init__(self):
                self.id = uuid4()
                self.name = "Test API Key"
                self.description = "A test key"
                self.key_prefix = "ak_test1234"
                self.user_id = uuid4()
                self.organization_id = uuid4()
                self.scopes = ["read:users", "write:agents"]
                self.rate_limit_per_minute = 100
                self.rate_limit_per_hour = 1000
                self.rate_limit_per_day = 10000
                self.is_active = True
                self.expires_at = None
                self.last_used_at = None
                self.total_requests = 42
                self.created_at = datetime.utcnow()
                self.updated_at = datetime.utcnow()

        return MockAPIKey()
    except Exception:
        # Fallback mock
        class MockAPIKey:
            def __init__(self):
                self.id = uuid4()
                self.name = "Test API Key"
                self.description = "A test key"
                self.key_prefix = "ak_test1234"
                self.user_id = uuid4()
                self.organization_id = uuid4()
                self.scopes = ["read:users", "write:agents"]
                self.rate_limit_per_minute = 100
                self.rate_limit_per_hour = 1000
                self.rate_limit_per_day = 10000
                self.is_active = True
                self.expires_at = None
                self.last_used_at = None
                self.total_requests = 42
                self.created_at = datetime.utcnow()
                self.updated_at = datetime.utcnow()

        return MockAPIKey()


def test_service_integration():
    """Test that router integrates with API key service"""
    print("\n🔗 Testing Service Integration...")

    try:
        from aiplanet_platform.services.api_key_service import APIKeyService

        # Test that service can be instantiated
        # Note: We can't test with real DB, but we can test the service class
        assert hasattr(
            APIKeyService, "create_api_key"
        ), "Service should have create_api_key method"
        assert hasattr(
            APIKeyService, "get_user_api_keys"
        ), "Service should have get_user_api_keys method"
        assert hasattr(
            APIKeyService, "revoke_api_key"
        ), "Service should have revoke_api_key method"
        assert hasattr(
            APIKeyService, "update_api_key"
        ), "Service should have update_api_key method"
        assert hasattr(
            APIKeyService, "get_api_key_usage_stats"
        ), "Service should have get_api_key_usage_stats method"
        assert hasattr(
            APIKeyService, "get_available_scopes"
        ), "Service should have get_available_scopes method"

        print("✅ Service has all required methods")

        # Test available scopes
        scopes = APIKeyService.get_available_scopes()
        assert len(scopes) > 0, "Should have available scopes"
        assert all(
            "scope" in s and "description" in s and "category" in s for s in scopes
        ), "Scopes should have required fields"

        print(f"✅ Service returns {len(scopes)} available scopes")
        return True

    except Exception as e:
        print(f"❌ Error testing service integration: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_endpoint_documentation():
    """Test that endpoints have proper documentation"""
    print("\n📚 Testing Endpoint Documentation...")

    try:
        from aiplanet_platform.routers.v1.private.api_key import (
            create_api_key,
            list_api_keys,
            get_api_key,
            update_api_key,
            revoke_api_key,
            get_api_key_usage,
            get_available_scopes,
            regenerate_api_key,
        )

        # Check that functions have docstrings
        functions_to_check = [
            create_api_key,
            list_api_keys,
            get_api_key,
            update_api_key,
            revoke_api_key,
            get_api_key_usage,
            get_available_scopes,
            regenerate_api_key,
        ]

        documented_count = 0
        for func in functions_to_check:
            if func.__doc__ and len(func.__doc__.strip()) > 10:
                documented_count += 1

        assert (
            documented_count >= len(functions_to_check) * 0.8
        ), "Most endpoints should be documented"
        print(
            f"✅ {documented_count}/{len(functions_to_check)} endpoints are documented"
        )

        # Check that create_api_key has security warning
        create_doc = create_api_key.__doc__ or ""
        assert (
            "only be shown once" in create_doc.lower()
        ), "Create endpoint should warn about key visibility"
        print("✅ Create endpoint has security warning")

        return True

    except Exception as e:
        print(f"❌ Error testing endpoint documentation: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_error_handling():
    """Test error handling patterns in the router"""
    print("\n⚠️  Testing Error Handling Patterns...")

    try:
        # Import HTTPException to ensure it's being used

        # Check that router file imports HTTPException
        router_file_content = ""
        try:
            router_file_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "aiplanet_platform",
                "routers",
                "v1",
                "private",
                "api_key.py",
            )
            if os.path.exists(router_file_path):
                with open(router_file_path, "r") as f:
                    router_file_content = f.read()
        except Exception:
            pass

        # Check for proper error handling patterns
        has_http_exception = "HTTPException" in router_file_content
        has_status_codes = "status.HTTP_" in router_file_content
        has_error_details = "detail=" in router_file_content

        assert has_http_exception, "Router should use HTTPException for errors"
        assert has_status_codes, "Router should use proper HTTP status codes"
        assert has_error_details, "Router should provide error details"

        print("✅ Router uses proper error handling patterns")
        return True

    except Exception as e:
        print(f"❌ Error testing error handling: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run Step 4 tests"""
    print("🚀 Running Step 4 Tests: API Key Management Router\n")

    tests_passed = 0
    total_tests = 6

    # Run tests
    if test_router_import():
        tests_passed += 1

    if test_router_endpoints():
        tests_passed += 1

    if test_api_key_schemas():
        tests_passed += 1

    if test_service_integration():
        tests_passed += 1

    if test_endpoint_documentation():
        tests_passed += 1

    if test_error_handling():
        tests_passed += 1

    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 All Step 4 tests passed!")
        print("\n✅ Step 4 is complete!")
        print("📝 What's working:")
        print("   ✅ API Key management router imports correctly")
        print("   ✅ Router has all expected endpoints")
        print("   ✅ API key schemas work correctly")
        print("   ✅ Service integration is proper")
        print("   ✅ Endpoints are well documented")
        print("   ✅ Error handling follows best practices")
        print("\n🔧 Available endpoints:")
        print("   ✅ POST /api-keys/ - Create new API key")
        print("   ✅ GET /api-keys/ - List user's API keys")
        print("   ✅ GET /api-keys/{id} - Get specific API key")
        print("   ✅ PUT /api-keys/{id} - Update API key")
        print("   ✅ DELETE /api-keys/{id} - Revoke API key")
        print("   ✅ GET /api-keys/{id}/usage - Get usage stats")
        print("   ✅ GET /api-keys/scopes/available - Get available scopes")
        print("   ✅ POST /api-keys/{id}/regenerate - Regenerate API key")
        print("\n🎯 Next Steps:")
        print("   📝 Add the router to your private router collection")
        print("   📝 Test the endpoints with your FastAPI app")
        print("   📝 Ready for Step 5: Third-Party API Router")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues before proceeding.")
        return False


if __name__ == "__main__":
    main()
