# test_step2b.py
"""
Test for Step 2B: Rate Limiter
"""

import sys
import os
from uuid import uuid4

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_rate_limiter_import():
    """Test that Rate Limiter can be imported"""
    print("📦 Testing Rate Limiter Import...")

    try:
        from aiplanet_platform.core.rate_limiter import RateLimiter

        print("✅ RateLimiter imported successfully")

        # Test that it has the expected methods
        expected_methods = [
            "is_allowed",
            "get_rate_limit_info",
            "reset_key_limits",
            "get_stats",
            "cleanup_expired_windows",
            "get_remaining_requests",
            "is_rate_limited",
        ]

        for method in expected_methods:
            assert hasattr(
                RateLimiter, method
            ), f"RateLimiter should have {method} method"

        print("✅ RateLimiter has all expected methods")
        print("✅ Global rate_limiter instance available")
        return True

    except ImportError as e:
        print(f"❌ Failed to import RateLimiter: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing RateLimiter: {e}")
        return False


def create_mock_api_key(rate_per_minute=5, rate_per_hour=20, rate_per_day=100):
    """Create a mock API key for testing"""

    class MockAPIKey:
        def __init__(self):
            self.id = uuid4()
            self.rate_limit_per_minute = rate_per_minute
            self.rate_limit_per_hour = rate_per_hour
            self.rate_limit_per_day = rate_per_day

    return MockAPIKey()


def test_rate_limiter_basic_functionality():
    """Test basic rate limiter functionality"""
    print("\n⏱️  Testing Rate Limiter Basic Functionality...")

    try:
        from aiplanet_platform.core.rate_limiter import RateLimiter

        # Create a fresh rate limiter for testing
        limiter = RateLimiter()

        # Create a mock API key with very low limits for testing
        api_key = create_mock_api_key(
            rate_per_minute=3, rate_per_hour=10, rate_per_day=50
        )

        # Test that requests are initially allowed
        assert limiter.is_allowed(api_key), "First request should be allowed"
        assert limiter.is_allowed(api_key), "Second request should be allowed"
        assert limiter.is_allowed(api_key), "Third request should be allowed"
        print("✅ Initial requests allowed")

        # Test that 4th request is blocked (rate limit reached)
        assert not limiter.is_allowed(api_key), "Fourth request should be blocked"
        print("✅ Rate limit enforcement works")

        # Test rate limit info
        info = limiter.get_rate_limit_info(api_key)
        assert (
            info["minute"]["used"] == 3
        ), f"Should have used 3 requests, got {info['minute']['used']}"
        assert info["minute"]["limit"] == 3, "Limit should be 3"
        print("✅ Rate limit info works correctly")

        return True

    except Exception as e:
        print(f"❌ Error testing basic functionality: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_rate_limiter_reset():
    """Test rate limiter reset functionality"""
    print("\n🔄 Testing Rate Limiter Reset...")

    try:
        from aiplanet_platform.core.rate_limiter import RateLimiter

        limiter = RateLimiter()
        api_key = create_mock_api_key(rate_per_minute=2)

        # Use up the rate limit
        assert limiter.is_allowed(api_key), "First request should be allowed"
        assert limiter.is_allowed(api_key), "Second request should be allowed"
        assert not limiter.is_allowed(api_key), "Third request should be blocked"

        # Reset and test again
        limiter.reset_key_limits(api_key)
        assert limiter.is_allowed(api_key), "Request should be allowed after reset"
        print("✅ Rate limit reset works")

        return True

    except Exception as e:
        print(f"❌ Error testing reset functionality: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_rate_limiter_info_methods():
    """Test rate limiter information methods"""
    print("\n📊 Testing Rate Limiter Info Methods...")

    try:
        from aiplanet_platform.core.rate_limiter import RateLimiter

        limiter = RateLimiter()
        api_key = create_mock_api_key(
            rate_per_minute=5, rate_per_hour=20, rate_per_day=100
        )

        # Make some requests
        limiter.is_allowed(api_key)
        limiter.is_allowed(api_key)

        # Test get_remaining_requests
        remaining = limiter.get_remaining_requests(api_key)
        assert (
            remaining["minute"] == 3
        ), f"Should have 3 remaining, got {remaining['minute']}"
        assert (
            remaining["hour"] == 18
        ), f"Should have 18 remaining, got {remaining['hour']}"
        print("✅ get_remaining_requests works")

        # Test is_rate_limited
        is_limited, reason = limiter.is_rate_limited(api_key)
        assert not is_limited, "Should not be rate limited yet"
        assert (
            "within rate limits" in reason.lower()
        ), f"Reason should indicate within limits, got: {reason}"
        print("✅ is_rate_limited works")

        # Test get_stats
        stats = limiter.get_stats()
        assert stats["total_tracked_keys"] >= 1, "Should have at least 1 tracked key"
        assert stats["active_keys"] >= 1, "Should have at least 1 active key"
        assert stats["total_requests"] >= 4, "Should have at least 4 total requests"
        print("✅ get_stats works")

        return True

    except Exception as e:
        print(f"❌ Error testing info methods: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_rate_limiter_multiple_keys():
    """Test rate limiter with multiple API keys"""
    print("\n🔑 Testing Multiple API Keys...")

    try:
        from aiplanet_platform.core.rate_limiter import RateLimiter

        limiter = RateLimiter()

        # Create multiple API keys with different limits
        key1 = create_mock_api_key(rate_per_minute=2)
        key2 = create_mock_api_key(rate_per_minute=3)

        # Test that each key is tracked independently
        assert limiter.is_allowed(key1), "Key1 first request should be allowed"
        assert limiter.is_allowed(key1), "Key1 second request should be allowed"
        assert not limiter.is_allowed(key1), "Key1 third request should be blocked"

        # Key2 should still work
        assert limiter.is_allowed(key2), "Key2 first request should be allowed"
        assert limiter.is_allowed(key2), "Key2 second request should be allowed"
        assert limiter.is_allowed(key2), "Key2 third request should be allowed"
        assert not limiter.is_allowed(key2), "Key2 fourth request should be blocked"

        print("✅ Multiple API keys tracked independently")

        # Test stats with multiple keys
        stats = limiter.get_stats()
        assert (
            stats["total_tracked_keys"] == 2
        ), f"Should have 2 tracked keys, got {stats['total_tracked_keys']}"
        print("✅ Stats work with multiple keys")

        return True

    except Exception as e:
        print(f"❌ Error testing multiple keys: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run Step 2B tests"""
    print("🚀 Running Step 2B Tests: Rate Limiter\n")

    tests_passed = 0
    total_tests = 5

    # Run tests
    if test_rate_limiter_import():
        tests_passed += 1

    if test_rate_limiter_basic_functionality():
        tests_passed += 1

    if test_rate_limiter_reset():
        tests_passed += 1

    if test_rate_limiter_info_methods():
        tests_passed += 1

    if test_rate_limiter_multiple_keys():
        tests_passed += 1

    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 All Step 2B tests passed!")
        print("\n✅ Step 2B is complete!")
        print("📝 What's working:")
        print("   ✅ RateLimiter imports and has correct methods")
        print("   ✅ Basic rate limiting works correctly")
        print("   ✅ Rate limit reset functionality works")
        print("   ✅ Information methods work correctly")
        print("   ✅ Multiple API keys tracked independently")
        print("\n🎯 Step 2 Summary:")
        print("   ✅ APIKeyService (Step 2A) - Complete")
        print("   ✅ RateLimiter (Step 2B) - Complete")
        print("\n🚀 Ready for Step 3: Enhanced Security")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues before proceeding.")
        return False


if __name__ == "__main__":
    main()
