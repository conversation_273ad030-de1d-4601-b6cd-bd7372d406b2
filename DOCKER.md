# Docker Setup for AIPlanet Platform

This guide will help you run the AIPlanet Platform using Docker on any machine, including Apple M3 MacBooks.

## Prerequisites

- Docker Desktop (latest version)
- Docker Compose (included with Docker Desktop)
- At least 4GB RAM available for containers

## Quick Start

### 1. Setup Environment

```bash
# Make the run script executable
chmod +x docker-run.sh

# Setup environment and directories
./docker-run.sh setup
```

### 2. Start the Application

```bash
# Start all services
./docker-run.sh start
```

This will:
- Build the application Docker image
- Start PostgreSQL database
- Start Redis cache
- Run database migrations
- Start the FastAPI application

### 3. Access the Application

- **API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/v1/docs
- **Health Check**: http://localhost:8000/health

## Available Commands

```bash
./docker-run.sh start         # Start all services
./docker-run.sh stop          # Stop all services
./docker-run.sh restart       # Restart all services
./docker-run.sh status        # Show service status
./docker-run.sh logs [service] # View logs
./docker-run.sh migrate       # Run database migrations
./docker-run.sh tools         # Start with pgAdmin
./docker-run.sh cleanup       # Remove everything
./docker-run.sh help          # Show help
```

## Manual Docker Commands

If you prefer to use Docker Compose directly:

### Start Services
```bash
# Copy environment file
cp .env.docker .env

# Start services
docker-compose up -d --build

# Run migrations
docker-compose --profile migration up migration
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f app
```

### Stop Services
```bash
docker-compose down
```

## Management Tools

### Start with PgAdmin
```bash
./docker-run.sh tools
```

Access PgAdmin at http://localhost:5050
- Email: <EMAIL>
- Password: admin

### Database Connection in PgAdmin
- Host: db
- Port: 5432
- Database: aiplanet_platform
- Username: postgres
- Password: postgres

## Environment Configuration

The `.env` file contains all configuration options:

### Database Settings
```env
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=aiplanet_platform
```

### API Settings
```env
PROJECT_NAME=aiplanet_platform
DEBUG=false
API_V1_PREFIX=/api/v1
SECRET_KEY=your-secret-key-here
```

### Port Configuration
```env
APP_PORT=8000      # FastAPI application
DB_PORT=5432       # PostgreSQL
REDIS_PORT=6379    # Redis
PGADMIN_PORT=5050  # PgAdmin
```

## Volumes and Data Persistence

The following volumes are created for data persistence:
- `postgres_data`: Database data
- `redis_data`: Redis cache data
- `pgadmin_data`: PgAdmin configuration
- `./logs`: Application logs (mounted from host)
- `./data`: Application data (mounted from host)

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   FastAPI App   │◄──►│   PostgreSQL    │    │     Redis       │
│   (Port 8000)   │    │   (Port 5432)   │    │   (Port 6379)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│                 │
│     PgAdmin     │
│   (Port 5050)   │
│   [Optional]    │
└─────────────────┘
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Change ports in .env file
   APP_PORT=8001
   DB_PORT=5433
   ```

2. **Database connection failed**
   ```bash
   # Check database logs
   ./docker-run.sh logs db
   
   # Restart services
   ./docker-run.sh restart
   ```

3. **Migration errors**
   ```bash
   # Run migrations manually
   ./docker-run.sh migrate
   ```

4. **Permission issues (macOS)**
   ```bash
   # Reset Docker Desktop
   # Or try with sudo (not recommended)
   ```

### View Container Status
```bash
docker-compose ps
```

### Inspect Container
```bash
docker-compose exec app bash
```

### Database Shell
```bash
docker-compose exec db psql -U postgres -d aiplanet_platform
```

## Development vs Production

### Development Mode
- Set `DEBUG=true` in `.env`
- Use `docker-run.sh start` for auto-reload
- Mount source code as volume (optional)

### Production Mode
- Set `DEBUG=false` in `.env`
- Use proper secrets in environment variables
- Consider using external database
- Set up proper logging and monitoring

## Performance Tuning

### For Apple M3 Macs
The Dockerfile is optimized for ARM64 architecture. If you experience issues:

1. Use native ARM64 images where possible
2. Increase Docker Desktop memory allocation
3. Consider using `--platform linux/amd64` for specific images if needed

### Database Performance
```env
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
```

## Security Notes

1. Change default passwords in production
2. Use environment-specific `.env` files
3. Don't commit `.env` files to version control
4. Use Docker secrets for sensitive data in production
5. Enable SSL/TLS in production

## Cleanup

To remove everything:
```bash
./docker-run.sh cleanup
```

This will remove:
- All containers
- All networks
- All volumes (⚠️ **Data will be lost!**)
- Unused Docker resources