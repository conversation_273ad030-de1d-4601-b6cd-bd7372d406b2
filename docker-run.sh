#!/bin/bash

# Docker Build & Run Script for AIplanet Platform (Supabase)
# This script helps you build and run the Docker container with Supabase

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="aiplanet-platform"
CONTAINER_NAME="aiplanet_app_supabase"
PORT=8000

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f .env ]; then
        print_error ".env file not found!"
        print_warning "Please create a .env file with your DATABASE_URL."
        print_warning "You can use the provided .env template."
        exit 1
    fi
}

# Function to build the Docker image
build_image() {
    print_header "Building Docker Image"
    print_status "Building $IMAGE_NAME..."
    
    docker build -t $IMAGE_NAME . --target production
    
    if [ $? -eq 0 ]; then
        print_status "✅ Image built successfully!"
    else
        print_error "❌ Failed to build image"
        exit 1
    fi
}

# Function to stop and remove existing container
stop_container() {
    if [ "$(docker ps -q -f name=$CONTAINER_NAME)" ]; then
        print_status "Stopping existing container..."
        docker stop $CONTAINER_NAME
    fi
    
    if [ "$(docker ps -aq -f name=$CONTAINER_NAME)" ]; then
        print_status "Removing existing container..."
        docker rm $CONTAINER_NAME
    fi
}

# Function to run the container
run_container() {
    print_header "Running Container"
    stop_container
    
    print_status "Starting container on port $PORT..."
    
    docker run -d \
        --name $CONTAINER_NAME \
        --env-file .env \
        -p $PORT:8000 \
        -v $(pwd)/logs:/app/logs \
        -v $(pwd)/data:/app/data \
        --restart unless-stopped \
        $IMAGE_NAME
    
    if [ $? -eq 0 ]; then
        print_status "✅ Container started successfully!"
        print_status "🚀 API is available at: http://localhost:$PORT"
        print_status "📚 API Documentation: http://localhost:$PORT/api/v1/docs"
        print_status "❤️  Health Check: http://localhost:$PORT/health"
    else
        print_error "❌ Failed to start container"
        exit 1
    fi
}

# Function to run with docker-compose
run_compose() {
    print_header "Running with Docker Compose"
    
    # Check if docker-compose or docker compose is available
    if command -v docker-compose >/dev/null 2>&1; then
        COMPOSE_CMD="docker-compose"
    elif docker compose version >/dev/null 2>&1; then
        COMPOSE_CMD="docker compose"
    else
        print_error "Neither 'docker-compose' nor 'docker compose' is available."
        exit 1
    fi
    
    print_status "Using: $COMPOSE_CMD"
    
    # Stop existing services
    $COMPOSE_CMD down
    
    # Start services
    print_status "Starting services..."
    $COMPOSE_CMD up -d --build
    
    if [ $? -eq 0 ]; then
        print_status "✅ Services started successfully!"
        print_status "🚀 API is available at: http://localhost:8000"
        print_status "📚 API Documentation: http://localhost:8000/api/v1/docs"
        print_status "❤️  Health Check: http://localhost:8000/health"
    else
        print_error "❌ Failed to start services"
        exit 1
    fi
}

# Function to show logs
show_logs() {
    print_header "Container Logs"
    if [ "$(docker ps -q -f name=$CONTAINER_NAME)" ]; then
        docker logs -f $CONTAINER_NAME
    else
        print_error "Container $CONTAINER_NAME is not running"
        exit 1
    fi
}

# Function to show status
show_status() {
    print_header "Container Status"
    docker ps -a -f name=$CONTAINER_NAME
    
    if [ "$(docker ps -q -f name=$CONTAINER_NAME)" ]; then
        print_status "Container is running ✅"
        print_status "API: http://localhost:$PORT"
    else
        print_warning "Container is not running"
    fi
}

# Function to cleanup
cleanup() {
    print_header "Cleanup"
    stop_container
    
    if docker images -q $IMAGE_NAME >/dev/null; then
        print_status "Removing image..."
        docker rmi $IMAGE_NAME
    fi
    
    print_status "Cleanup complete!"
}

# Function to run database migrations
run_migrations() {
    print_header "Running Database Migrations"
    if [ "$(docker ps -q -f name=$CONTAINER_NAME)" ]; then
        print_status "Running Alembic migrations..."
        docker exec $CONTAINER_NAME poetry run alembic upgrade head
        
        if [ $? -eq 0 ]; then
            print_status "✅ Migrations completed successfully!"
        else
            print_error "❌ Migrations failed"
            exit 1
        fi
    else
        print_error "Container $CONTAINER_NAME is not running"
        exit 1
    fi
}

# Function to setup environment
setup_env() {
    print_header "Environment Setup"
    
    # Create necessary directories
    mkdir -p logs data
    
    if [ ! -f .env ]; then
        print_warning ".env file not found. Please create one with your DATABASE_URL."
        print_status "Example .env template has been provided."
    else
        print_status ".env file exists ✅"
    fi
}

# Function to show help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build         Build the Docker image"
    echo "  run           Run the container (build if needed)"
    echo "  compose       Run with docker-compose"
    echo "  logs          Show container logs"
    echo "  status        Show container status"
    echo "  stop          Stop the container"
    echo "  restart       Restart the container"
    echo "  migrate       Run database migrations"
    echo "  setup         Setup environment and directories"
    echo "  cleanup       Stop container and remove image"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build       # Build the image"
    echo "  $0 run         # Build and run"
    echo "  $0 compose     # Use docker-compose"
    echo "  $0 logs        # View logs"
}

# Main script logic
case "${1:-help}" in
    "build")
        check_docker
        check_env_file
        build_image
        ;;
    "run")
        check_docker
        check_env_file
        build_image
        run_container
        ;;
    "compose")
        check_docker
        check_env_file
        run_compose
        ;;
    "logs")
        check_docker
        show_logs
        ;;
    "status")
        check_docker
        show_status
        ;;
    "stop")
        check_docker
        stop_container
        ;;
    "restart")
        check_docker
        check_env_file
        stop_container
        run_container
        ;;
    "migrate")
        check_docker
        run_migrations
        ;;
    "setup")
        setup_env
        ;;
    "cleanup")
        check_docker
        cleanup
        ;;
    "help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac