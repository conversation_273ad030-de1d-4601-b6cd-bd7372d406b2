# AI Planet Platform

> A comprehensive FastAPI-based AI platform with third-party API access, temporal workflow processing, and multi-agent AI capabilities.

[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://www.python.org)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-blue.svg)](https://www.postgresql.org)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🚀 Features

### Core Platform
- **FastAPI Backend** with SQLAlchemy ORM and Pydantic validation
- **PostgreSQL Database** with Alembic migrations
- **Redis Caching** for improved performance
- **JWT Authentication** with refresh token support
- **API Key Management** for third-party access
- **Rate Limiting** with configurable limits
- **Organization-based Data Isolation**

### AI & Workflow Processing
- **Temporal Workflows** for distributed AI processing
- **Multi-Agent AI Systems** with team management
- **AI Tool Integration** and management
- **Session Management** for conversation tracking
- **Streaming AI Responses** with real-time updates

### Third-Party Integration
- **RESTful API** for external applications
- **Scope-based Permissions** for granular access control
- **Organization Isolation** for multi-tenant security
- **Comprehensive API Documentation** with OpenAPI/Swagger

### Development & Deployment
- **Docker & Docker Compose** support
- **Environment-based Configuration**
- **Comprehensive Error Handling**
- **Database Connection Pooling**
- **Health Check Endpoints**

## 📋 Requirements

- **Python 3.10+**
- **Poetry** (for dependency management)
- **PostgreSQL 13+**
- **Docker & Docker Compose** (for containerized deployment)

## Project Structure
```
aiplanet_platform
├── aiplanet_platform
│   ├── __init__.py
│   ├── constants
│   │   ├── __init__.py
│   │   ├── api_key.py
│   │   ├── default_tools.py
│   │   ├── input.py
│   │   ├── organization.py
│   │   ├── output.py
│   │   ├── run_context.py
│   │   ├── team_manager.py
│   │   ├── tool.py
│   │   └── user.py
│   ├── core
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── enhanced_security.py
│   │   ├── logging.py
│   │   ├── rate_limiter.py
│   │   ├── security.py
│   │   └── temporal_config.py
│   ├── exceptions
│   │   └── __init__.py
│   ├── jobs
│   │   ├── __init__.py
│   │   └── temporal_streaming_workflow.py
│   ├── memory
│   ├── middleware
│   │   ├── __init__.py
│   │   └── rate_limit_middleware.py
│   ├── models
│   │   ├── __init__.py
│   │   ├── agent.py
│   │   ├── api_key.py
│   │   ├── input.py
│   │   ├── message.py
│   │   ├── model.py
│   │   ├── organization.py
│   │   ├── output.py
│   │   ├── run.py
│   │   ├── session.py
│   │   ├── settings.py
│   │   ├── team.py
│   │   ├── termination_condition.py
│   │   ├── tool.py
│   │   └── user.py
│   ├── routers
│   │   ├── __init__.py
│   │   └── v1
│   ├── schemas
│   │   ├── __init__.py
│   │   ├── agent.py
│   │   ├── api_key.py
│   │   ├── input.py
│   │   ├── message.py
│   │   ├── model.py
│   │   ├── organization.py
│   │   ├── output.py
│   │   ├── run.py
│   │   ├── session.py
│   │   ├── team.py
│   │   ├── termination_condition.py
│   │   ├── tool.py
│   │   └── user.py
│   ├── services
│   │   ├── __init__.py
│   │   ├── agent_service.py
│   │   ├── api_key_service.py
│   │   ├── builder_service.py
│   │   ├── component_test_service.py
│   │   ├── input_service.py
│   │   ├── message_service.py
│   │   ├── model_service.py
│   │   ├── oauth_service.py
│   │   ├── organization_service.py
│   │   ├── output_service.py
│   │   ├── run_service.py
│   │   ├── session_service.py
│   │   ├── settings_service.py
│   │   ├── team_manager_service.py
│   │   ├── team_service.py
│   │   ├── team_validations.py
│   │   ├── temporal_worker_service.py
│   │   ├── termination_condition_service.py
│   │   ├── tool_service.py
│   │   ├── user_service.py
│   │   ├── validation_service.py
│   │   └── websocket_manager_service.py
│   └── utils
│       ├── __init__.py
│       └── update_resource.py
├── alembic.ini
├── coding
├── docker-compose.yml
├── docker-run.sh
├── DOCKER.md
├── Dockerfile
├── main.py
├── migrations
│   ├── env.py
│   └── script.py.mako
├── poetry.lock
├── pyproject.toml
├── README.md
├── tests
│   ├── __init__.py
│   ├── 1106.4577v1.pdf
│   ├── 2010.05254v1.pdf
│   ├── temporal_server_run.py
│   ├── test_api_key_authentication.py
│   ├── test_api_key_endpoints.py
│   ├── test_api_key_service.py
│   ├── test_middleware_integration.py
│   ├── test_rate_limit.py
│   └── test_rate_limiter_reset.py
└── worker_server.py
```
## 🛠️ Installation

### Local Development

1. **Clone the repository:**
```bash
git clone https://github.com/aiplanethub/aiplanet_platform.git
cd aiplanet_platform
```
2. **Create virtual environment:**
```bash
python -m venv venv
source venv/bin/activate # On Windows use `venv\Scripts\activate`
```

3. **Install dependencies with Poetry:**
```bash
poetry install
```

4. **Setup environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Edit the username and the password**
Edit the username and password of your postgres service in .env (DATABASE_URL)

6. **Create database:**
```bash
create database aiplanet_platform # or use your preferred method
```

7. **Run migrations:**
```bash
alembic upgrade head
```

8. **Start the development server:**
```bash
poetry run uvicorn main:app --reload
```

### Docker Deployment

1. **Build and run with Docker:**
```bash
# Build the Docker image
docker build -t aiplanet-platform .

# Run the container
docker run -d \
  --name aiplanet_app \
  --env-file .env \
  -p 8000:8000 \
  aiplanet-platform
```

2. **Access the application:**
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/api/v1/docs
- **Health Check**: http://localhost:8000/health

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following configuration:

```env
# API Settings
PROJECT_NAME=aiplanet_platform
DEBUG=true
API_V1_PREFIX=/api/v1

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=10080

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/aiplanet_platform
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10

# Redis (Optional)
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=300

# Temporal Configuration
TEMPORAL_ENABLED=true
TEMPORAL_HOST=localhost:7233
TEMPORAL_NAMESPACE=default
TEMPORAL_TASK_QUEUE=team-manager-tasks
TEMPORAL_START_WORKER=true

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# OAuth (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

## 🔌 API Documentation

### Public Endpoints
- `GET /health` - Health check
- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/register` - User registration

### Third-Party API Endpoints
Base URL: `/api/v1/third-party/`

#### Authentication
Use API keys for third-party access:
```bash
# Header method
Authorization: Bearer YOUR_API_KEY

# Alternative header
X-API-Key: YOUR_API_KEY
```

#### Available Private and Third Party Endpoints

| Endpoint | Method | Scope Required | Description |
|----------|--------|----------------|-------------|
| `/health/` | GET | None | System health check |
| `/health/info` | GET | None | API information |
| `/users/` | GET/POST/PUT/DELETE | `read:users` | List users | (Not available for third-party)
| `/organizations/` | GET | `read:organizations` | Get current organization | (Not available for third-party)
| `/agents/` | GET/POST/PUT/DELETE | `read:agents`, `write:agents` | Manage AI agents crud |
| `/tools/` | GET/POST/PUT/DELETE | `read:tools`, `write:tools` | Manage AI tools crud |
| `/models/` | GET/POST/PUT/DELETE | `read:models`, `write:models` | Manage models crud |
| `/workflows/` | GET/POST/PUT/DELETE | `read:workflows`, `write:workflows` | Manage workflows crud |
| `/sessions/` | GET/POST/PUT/DELETE | `read:sessions`, `write:sessions` | Manage sessions crud |
| `/termination_conditions/` | GET/POST/PUT/DELETE | `read:termination_conditions`, `write:termination_conditions` | Manage termination_conditions crud |
| `/inputs/` | GET/POST/PUT/DELETE | `read:inputs`, `write:inputs` | Manage inputs crud |
| `/outputs/` | GET/POST/PUT/DELETE | `read:outputs`, `write:outputs` | Manage outputs crud |


#### Rate Limits
- **Default**: 100 requests/minute, 1000/hour, 10000/day
- **Headers**: `X-RateLimit-Remaining-Minute`, `X-RateLimit-Reset-Minute`
- **Customizable** per API key

#### Example Usage

```bash
# Get API information
curl -X GET "http://localhost:8000/api/v1/third-party/health/info"

# List users (requires API key)
curl -X GET "http://localhost:8000/api/v1/third-party/users/" \
  -H "Authorization: Bearer YOUR_API_KEY"

# Create an agent
curl -X POST "http://localhost:8000/api/v1/third-party/agents/" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"name": "My Agent", "description": "AI Agent for testing"}'
```

## 🤖 Temporal Worker Service

### Architecture
The platform uses Temporal for distributed AI processing with the following components:

- **Workflows**: `TeamStreamingWorkflow` for AI team management
- **Activities**: Environment setup, AI execution, status updates
- **Task Queue**: `team-manager-tasks`
- **Concurrency**: Configurable concurrent activities and workflow tasks

### Worker Management

#### Development Mode
```bash
# Start worker with main server
TEMPORAL_START_WORKER=true
```

#### Production Mode
```bash
# Start dedicated worker processes
python worker_server.py

# Or with environment variables
WORKER_ID=worker-1 python worker_server.py
```

#### Worker Configuration
```env
TEMPORAL_ENABLED=true
TEMPORAL_HOST=localhost:7233
TEMPORAL_NAMESPACE=default
TEMPORAL_TASK_QUEUE=team-manager-tasks
WORKER_ID=worker-1
```

## 🚀 Production Deployment

### Docker Production Setup

1. **Prepare environment:**
```bash
cp .env.example .env
# Edit production settings
```

2. **Build and deploy:**
```bash
# Build the image
docker build -t aiplanet-platform .

# Run in production mode
docker run -d \
  --name aiplanet_app \
  --env-file .env \
  -p 8000:8000 \
  --restart unless-stopped \
  aiplanet-platform
```

3. **With Docker Compose (if using external services):**
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - db
      - redis
  
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: aiplanet_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### Production Checklist

- [ ] Change default passwords
- [ ] Use environment-specific `.env` files
- [ ] Enable SSL/TLS certificates
- [ ] Configure proper resource limits
- [ ] Set up monitoring and alerting
- [ ] Configure database backups
- [ ] Set up load balancing for workers
- [ ] Configure log aggregation

### Docker Commands

```bash
# Build the image
docker build -t aiplanet-platform .

# Run the container
docker run -d --name aiplanet_app --env-file .env -p 8000:8000 aiplanet-platform

# View logs
docker logs -f aiplanet_app

# Stop the container
docker stop aiplanet_app

# Remove the container
docker rm aiplanet_app
```

## 🔐 Security Features

### Authentication Methods
- **JWT Tokens** with refresh capability
- **API Keys** with scope-based permissions
- **OAuth Integration** (Google, GitHub)

### Security Measures
- **Rate Limiting** with configurable limits
- **Organization Isolation** for multi-tenant security
- **Scope-based Access Control** for granular permissions
- **SQL Injection Protection** via SQLAlchemy ORM
- **Input Validation** with Pydantic models

### Available Scopes
- `read:users` - Read user data
- `write:users` - Create/update users
- `read:organizations` - Read organization data
- `read:agents` - Read AI agents
- `write:agents` - Create/update agents
- `read:tools` - Read tools
- `write:tools` - Create/update tools
- `read:sessions` - Read sessions
- `write:sessions` - Create/update sessions
- `read:termination_conditions` - Read termination conditions
- `write:termination_conditions` - Create/update termination conditions
- `read:inputs` - Read inputs
- `write:inputs` - Create/update inputs
- `read:outputs` - Read outputs
- `write:outputs` - Create/update outputs
- `read:models` - Read models
- `write:models` - Create/update models
- `read:workflows` - Read workflows
- `write:workflows` - Create/update workflows

## 📊 Database Schema

### Core Tables
- **users** - User accounts and authentication
- **organizations** - Multi-tenant organization data
- **api_keys** - Third-party API access keys
- **agents** - AI agent configurations
- **tools** - AI tool definitions
- **sessions** - Conversation sessions
- **teams** - AI team configurations
- **termination_conditions** - Worflow termination conditions
- **inputs** - Session inputs
- **outputs** - Session outputs
- **models** - AI model configurations
- **workflows** - AI workflow configurations

### Migrations
```bash
# Create new migration
poetry run alembic revision --autogenerate -m "Description"

# Apply migrations
poetry run alembic upgrade head

# Rollback migration
poetry run alembic downgrade -1
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=aiplanet_platform

# Run specific test file
poetry run pytest tests/test_api_key_endpoints.py
```

### Test Categories
- **Unit Tests** - Individual component testing
- **Integration Tests** - API endpoint testing
- **Security Tests** - Authentication and authorization
- **Performance Tests** - Rate limiting and load testing

## 🔍 Monitoring & Logging

### Health Checks
- `GET /health` - Basic application health
- `GET /api/v1/third-party/health/authenticated` - API key validation

### Logging Configuration
```env
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

### Monitoring Endpoints
- Application metrics
- Database connection status
- Temporal worker health
- API key usage statistics

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch:**
```bash
git checkout -b feature/your-feature-name
```

3. **Install dependencies:**
```bash
poetry install
```

4. **Run pre-commit hooks:**
```bash
pre-commit install
pre-commit run --all-files
```

5. **Make your changes and test:**
```bash
poetry run pytest
```

6. **Submit a pull request**

### Code Style
- **Black** for code formatting
- **isort** for import sorting
- **flake8** for linting
- **mypy** for type checking

### Commit Convention
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation updates
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Test additions/updates
- `chore:` - Maintenance tasks

## 📚 Documentation

### API Documentation
- **Interactive Docs**: http://localhost:8000/api/v1/docs
- **ReDoc**: http://localhost:8000/api/v1/redoc
- **OpenAPI Spec**: http://localhost:8000/api/v1/openapi.json

### Additional Resources
- [API Development Guide](docs/api-development.md)
- [Temporal Workflow Guide](docs/temporal-workflows.md)
- [Security Best Practices](docs/security.md)

## 🐛 Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check database status (if using Docker Compose)
docker-compose logs db

# Restart database
docker-compose restart db
```

#### Port Already in Use
```bash
# Use different port
docker run -d --name aiplanet_app --env-file .env -p 8001:8000 aiplanet-platform
```

#### Migration Errors
```bash
# Run migrations manually
docker exec -it aiplanet_app poetry run alembic upgrade head
```

#### Worker Connection Issues
```bash
# Check application logs
docker logs aiplanet_app

# Restart container
docker restart aiplanet_app
```

### Getting Help
- **GitHub Issues**: Report bugs and request features
- **Documentation**: Check the docs folder
- **Email**: <EMAIL>

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🎯 Roadmap

### Current Version (v1.0)
- ✅ Core API functionality
- ✅ Third-party API access
- ✅ Temporal workflow processing
- ✅ Docker deployment

### Upcoming Features (v1.1)
- [ ] Memroy Component support
- [ ] Knolwedgebase Component support
- [ ] Real-time WebSocket connections 
- [ ] Enhanced monitoring dashboard

### Future Plans (v2.0)
- [ ] ChatBuilder for workflow creation
- [ ] Advanced AI orchestration
- [ ] Plugin system
- [ ] Enterprise features

## 🙏 Acknowledgments

- **FastAPI** - For the excellent web framework
- **Temporal** - For workflow orchestration
- **SQLAlchemy** - For database ORM
- **Pydantic** - For data validation
- **Docker** - For containerization

---

<div align="center">
  <p>Built with ❤️ by the AI Planet team</p>
  <p>
    <a href="https://github.com/aiplanethub/aiplanet_platform">GitHub</a> •
    <a href="https://docs.aiplanet.com">Documentation</a> •
    <a href="https://discord.gg/aiplanet">Discord</a> •
    <a href="https://twitter.com/aiplanethub">Twitter</a>
  </p>
</div>
