#!/usr/bin/env python3
"""
Script to update tables with default tools and termination conditions for old users.
Uses the existing ToolService.bulk_add_tools and TerminationConditionService methods.
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Import your models and database configuration
from aiplanet_platform.core.config import get_settings
from aiplanet_platform.services.tool_service import ToolService
from aiplanet_platform.services.termination_condition_service import (
    TerminationConditionService,
)

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def create_db_session():
    """Create database session"""
    try:
        db_url = get_settings().DATABASE_URL
        engine = create_engine(db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        return SessionLocal(), engine
    except Exception as e:
        logger.error(f"Failed to create database session: {e}")
        raise


def get_organizations_needing_defaults():
    """
    Get all organizations that have existing tools or termination conditions.
    """
    session, engine = create_db_session()

    try:
        # Get all unique organization_ids from existing tools and termination conditions
        query = text(
            """
            SELECT DISTINCT organization_id FROM (
                -- Get organizations from existing tools
                SELECT organization_id FROM tools WHERE is_deleted = false
                
                UNION
                
                -- Get organizations from existing termination conditions  
                SELECT organization_id FROM terminationconditions WHERE is_deleted = false
                
                UNION
                
                -- Get organizations from user_organizations junction table
                SELECT organization_id FROM user_organizations
            ) AS all_org_ids
            ORDER BY organization_id NULLS LAST
        """
        )

        result = session.execute(query)
        organizations = [row[0] for row in result.fetchall()]

        logger.info(f"Found {len(organizations)} organizations to process")

        return organizations

    except Exception as e:
        logger.error(f"Error getting organizations: {e}")
        raise
    finally:
        session.close()


def add_default_tools_using_service(organization_id):
    """
    Add default tools using the existing ToolService.bulk_add_tools method.
    """
    session, engine = create_db_session()

    try:
        # Initialize ToolService with the session
        tool_service = ToolService(session)

        # Use the existing bulk_add_tools method
        # We pass user_id as None since this is for system defaults
        user_id = None
        tool_service.bulk_add_tools(user_id, organization_id)

        org_display = (
            f"organization {organization_id}"
            if organization_id
            else "global organization"
        )
        logger.info(f"Successfully processed default tools for {org_display}")

        return {"successful": "processed"}  # ToolService doesn't return detailed stats

    except Exception as e:
        logger.error(f"Error adding tools for organization {organization_id}: {e}")
        raise
    finally:
        session.close()


def add_default_termination_conditions_using_service(organization_id):
    """
    Add default termination conditions using TerminationConditionService.
    """
    session, engine = create_db_session()

    try:
        # Initialize TerminationConditionService with the session
        termination_service = TerminationConditionService(session)

        # Check if the service has a bulk add method, otherwise we'd need to implement it
        if hasattr(
            termination_service,
            "create_default_termination_conditions_for_organization",
        ):
            termination_service.create_default_termination_conditions_for_organization(
                organization_id
            )
        else:
            # If no bulk method exists, we can add one or handle it differently
            logger.warning(
                f"No bulk method found for termination conditions, skipping organization {organization_id}"
            )
            return {"successful": "skipped"}

        org_display = (
            f"organization {organization_id}"
            if organization_id
            else "global organization"
        )
        logger.info(
            f"Successfully processed default termination conditions for {org_display}"
        )

        return {"successful": "processed"}

    except Exception as e:
        logger.error(
            f"Error adding termination conditions for organization {organization_id}: {e}"
        )
        raise
    finally:
        session.close()


def get_current_stats():
    """Get current statistics about tools and termination conditions"""
    session, engine = create_db_session()

    try:
        stats = {}

        tables = ["tools", "terminationconditions"]

        for table in tables:
            # Count total components
            total_query = text(f"SELECT COUNT(*) FROM {table} WHERE is_deleted = false")
            total_count = session.execute(total_query).scalar()

            # Count components by organization
            org_query = text(
                f"""
                SELECT organization_id, COUNT(*) 
                FROM {table} 
                WHERE is_deleted = false 
                GROUP BY organization_id
                ORDER BY organization_id
            """
            )
            org_counts = session.execute(org_query).fetchall()

            stats[table] = {
                "total": total_count,
                "by_organization": {
                    str(org_id) if org_id else "no_organization": count
                    for org_id, count in org_counts
                },
            }

        logger.info("=== Current Statistics ===")
        for table_name, table_stats in stats.items():
            logger.info(f"{table_name}: {table_stats['total']} total components")
            for org, count in table_stats["by_organization"].items():
                logger.info(f"  {org}: {count} components")

        return stats

    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        raise
    finally:
        session.close()


def update_default_components():
    """
    Update tools and termination conditions for each organization using existing service methods.
    """
    try:
        logger.info(
            "Starting default tools and termination conditions update process..."
        )
        logger.info(
            "Using existing ToolService.bulk_add_tools and TerminationConditionService methods"
        )

        organizations = get_organizations_needing_defaults()

        for org_id in organizations:
            org_display = f"organization {org_id}" if org_id else "global organization"
            logger.info(f"Processing {org_display}...")

            # Add default tools using existing ToolService.bulk_add_tools
            try:
                add_default_tools_using_service(org_id)
            except Exception as e:
                logger.error(f"Failed to add tools for {org_display}: {e}")

            # Add default termination conditions using TerminationConditionService
            try:
                add_default_termination_conditions_using_service(org_id)
            except Exception as e:
                logger.error(
                    f"Failed to add termination conditions for {org_display}: {e}"
                )

        logger.info("Default tools and termination conditions update completed!")

    except Exception as e:
        logger.error(f"Error during default components update: {e}")
        raise


def main():
    """Main function"""
    try:
        logger.info("=== Default Tools and Termination Conditions Update Script ===")
        logger.info("Using existing service methods instead of raw SQL")

        # Show current statistics
        logger.info("Current state before update:")
        get_current_stats()

        # Perform the update
        update_default_components()

        # Show updated statistics
        logger.info("\nState after update:")
        get_current_stats()

    except Exception as e:
        logger.error(f"Script failed: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
