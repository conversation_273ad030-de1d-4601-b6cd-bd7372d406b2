#!/usr/bin/env python3
"""
Script to update user_organizations table by matching email addresses
between users and organizations tables.
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

# Import your models and database configuration
from aiplanet_platform.core.config import get_settings

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def create_db_session():
    """Create database session"""
    try:
        db_url = get_settings().DATABASE_URL
        engine = create_engine(db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        return SessionLocal(), engine
    except Exception as e:
        logger.error(f"Failed to create database session: {e}")
        raise


def update_user_organizations():
    """
    Update user_organizations table by matching email addresses
    between users and organizations tables.
    """
    session, engine = create_db_session()

    try:
        logger.info("Starting user_organizations update process...")

        # Query to find users and organizations with matching email addresses
        # This query will return pairs of (user_id, organization_id) where emails match
        query = text(
            """
            SELECT DISTINCT u.id as user_id, o.id as organization_id
            FROM users u
            INNER JOIN organizations o ON LOWER(u.email) = LOWER(o.email)
            WHERE u.is_deleted = false 
            AND o.is_deleted = false
            AND NOT EXISTS (
                SELECT 1 FROM user_organizations uo 
                WHERE uo.user_id = u.id 
                AND uo.organization_id = o.id
            )
        """
        )

        # Execute the query
        result = session.execute(query)
        matches = result.fetchall()

        if not matches:
            logger.info("No new email matches found between users and organizations.")
            return

        logger.info(f"Found {len(matches)} new email matches to process.")

        # Insert new relationships
        successful_inserts = 0
        failed_inserts = 0

        for match in matches:
            try:
                # Insert into user_organizations table
                insert_query = text(
                    """
                    INSERT INTO user_organizations (user_id, organization_id)
                    VALUES (:user_id, :organization_id)
                """
                )

                session.execute(
                    insert_query,
                    {
                        "user_id": match.user_id,
                        "organization_id": match.organization_id,
                    },
                )

                successful_inserts += 1
                logger.debug(
                    f"Linked user {match.user_id} to organization {match.organization_id}"
                )

            except IntegrityError as e:
                logger.warning(
                    f"Failed to link user {match.user_id} to organization {match.organization_id}: {e}"
                )
                failed_inserts += 1
                session.rollback()
                continue
            except Exception as e:
                logger.error(
                    f"Unexpected error linking user {match.user_id} to organization {match.organization_id}: {e}"
                )
                failed_inserts += 1
                session.rollback()
                continue

        # Commit all successful inserts
        session.commit()

        logger.info("Update completed successfully!")
        logger.info(
            f"Successfully linked: {successful_inserts} user-organization pairs"
        )
        if failed_inserts > 0:
            logger.warning(f"Failed to link: {failed_inserts} user-organization pairs")

    except Exception as e:
        logger.error(f"Error during user_organizations update: {e}")
        session.rollback()
        raise
    finally:
        session.close()


def get_current_stats():
    """Get current statistics about user_organizations table"""
    session, engine = create_db_session()

    try:
        # Count total relationships
        total_relationships = session.execute(
            text("SELECT COUNT(*) FROM user_organizations")
        ).scalar()

        # Count users with organizations
        users_with_orgs = session.execute(
            text("SELECT COUNT(DISTINCT user_id) FROM user_organizations")
        ).scalar()

        # Count organizations with users
        orgs_with_users = session.execute(
            text("SELECT COUNT(DISTINCT organization_id) FROM user_organizations")
        ).scalar()

        # Count potential matches (users and orgs with same email)
        potential_matches = session.execute(
            text(
                """
                SELECT COUNT(*)
                FROM users u
                INNER JOIN organizations o ON LOWER(u.email) = LOWER(o.email)
                WHERE u.is_deleted = false AND o.is_deleted = false
            """
            )
        ).scalar()

        logger.info("=== Current Statistics ===")
        logger.info(f"Total user-organization relationships: {total_relationships}")
        logger.info(f"Users with organizations: {users_with_orgs}")
        logger.info(f"Organizations with users: {orgs_with_users}")
        logger.info(f"Potential email matches: {potential_matches}")

        return {
            "total_relationships": total_relationships,
            "users_with_orgs": users_with_orgs,
            "orgs_with_users": orgs_with_users,
            "potential_matches": potential_matches,
        }

    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        raise
    finally:
        session.close()


def main():
    """Main function"""
    try:
        logger.info("=== User Organizations Update Script ===")

        # Show current statistics
        get_current_stats()

        # Perform the update
        update_user_organizations()

        # Show updated statistics
        logger.info("\n=== After Update ===")
        get_current_stats()

    except Exception as e:
        logger.error(f"Script failed: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
