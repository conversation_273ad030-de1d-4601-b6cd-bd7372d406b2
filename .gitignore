# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Virtual Environment
venv/
ENV/
.env
.venv
env/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Project specific
.env
*.db
*.sqlite
*.sqlite3
logs/
data/
uploads/

# Docker
.dockerignore
docker-compose.override.yml

# Jupyter Notebook
.ipynb_checkpoints

# mypy
.mypy_cache/

# workflow json
workflows/