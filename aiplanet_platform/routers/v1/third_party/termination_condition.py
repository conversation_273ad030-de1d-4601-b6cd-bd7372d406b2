"""
Router for termination condition endpoints
"""
import logging
from fastapi import APIRouter, Query, Path, HTTPException, status, Depends, Request
from typing import Dict, Optional
from uuid import UUID
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.services.termination_condition_service import (
    TerminationConditionService,
)
from aiplanet_platform.schemas.termination_condition import (
    TerminationConditionCreate,
    TerminationConditionUpdate,
    TerminationConditionResponse,
    TerminationConditionList,
)
from aiplanet_platform.utils.update_resource import update_component_fields
from aiplanet_platform.core.enhanced_security import (
    APIKeyAuth,
    create_api_key_auth,
    get_organization_id,
)


router = APIRouter(
    prefix="/termination_conditions",
    tags=["termination_conditions"],
)
logger = logging.getLogger(__name__)


@router.get("/", response_model=TerminationConditionList)
async def get_termination_conditions(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:termination_conditions")),
    db: Session = Depends(get_db),
):
    """
    Get a list of termination_conditions with filtering, sorting, and pagination.
    """
    organization_id = get_organization_id(request)
    logger.info(
        f"Current organization {organization_id} accessing get_termination_conditions"
    )

    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if organization_id:
        filters["organization_id"] = organization_id

    service = TerminationConditionService(db)
    termination_conditions = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = service.count_resources(filters)

    return {
        "items": termination_conditions,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=TerminationConditionResponse)
async def get_termination_condition_by_id(
    resource_id: UUID = Path(..., description="ID of the termination_condition to get"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:termination_conditions")),
    db: Session = Depends(get_db),
):
    """
    Get a specific termination_condition by ID.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} accessing get_termination_condition_by_id"
    )
    # Initialize service
    service = TerminationConditionService(db)
    termination_condition = service.fetch_resource_by_id(resource_id)
    if not termination_condition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return termination_condition


@router.post(
    "/",
    response_model=TerminationConditionResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_termination_condition(
    request: Request,
    termination_condition_data: TerminationConditionCreate,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:termination_conditions")),
    db: Session = Depends(get_db),
):
    """
    Create a new termination_condition.
    """
    organization_id = get_organization_id(request)
    logger.info(
        f"Current organization {organization_id} accessing create_termination_condition"
    )
    # Add organization id
    termination_condition_data.organization_id = organization_id

    # Initialize service
    service = TerminationConditionService(db)

    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters(
        {
            "component": termination_condition_data.component.model_dump_json(),
            "organization_id": termination_condition_data.organization_id,
        }
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(termination_condition_data.model_dump())


@router.put("/{resource_id}", response_model=TerminationConditionResponse)
async def update_termination_condition(
    request: Request,
    resource_id: UUID = Path(
        ..., description="ID of the termination_condition to update"
    ),
    termination_condition_data: TerminationConditionUpdate = None,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:termination_conditions")),
    db: Session = Depends(get_db),
):
    """
    Update a termination_condition.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} accessing update_termination_condition"
    )
    # Initialize service
    service = TerminationConditionService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = termination_condition_data.component.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    existing_data = existing.component

    updated_data = update_component_fields(existing_data, data)
    termination_condition_data.component = updated_data
    termination_condition_data.organization_id = request.state.user.organization_id

    termination_condition = service.update_resource_by_id(
        resource_id, termination_condition_data.model_dump()
    )
    return termination_condition


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_termination_condition(
    resource_id: UUID = Path(
        ..., description="ID of the termination_condition to delete"
    ),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:termination_conditions")),
    db: Session = Depends(get_db),
):
    """
    Delete a termination_condition. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing delete_termination_condition"
    )
    # Initialize service
    service = TerminationConditionService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=TerminationConditionResponse)
async def restore_termination_condition(
    resource_id: UUID = Path(
        ..., description="ID of the termination_condition to restore", gt=0
    ),
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:termination_conditions")),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted termination_condition.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing restore_termination_condition"
    )
    # Initialize service
    service = TerminationConditionService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource
