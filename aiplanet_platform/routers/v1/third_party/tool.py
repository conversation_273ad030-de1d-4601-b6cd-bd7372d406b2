"""
Router for tool endpoints
"""
import logging
from typing import Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status, Request
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.tool import (
    ToolCreate,
    ToolList,
    ToolResponse,
    ToolUpdate,
)
from aiplanet_platform.services.tool_service import ToolService
from aiplanet_platform.utils.update_resource import update_component_fields
from aiplanet_platform.models.user import User
from aiplanet_platform.core.enhanced_security import create_mixed_auth_dependency

router = APIRouter(
    prefix="/tools",
    tags=["tools"],
)
logger = logging.getLogger(__name__)


@router.get("/", response_model=ToolList)
async def get_tools(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(create_mixed_auth_dependency("read:tools")),
    db: Session = Depends(get_db),
):
    """
    Get a list of tools with filtering, sorting, and pagination.
    """
    logger.info(f"Current user {current_user.id} accessing get_tools")
    # Add organization id
    user = request.state.user

    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if user.organization_id:
        filters["organization_id"] = user.organization_id

    service = ToolService(db)
    tools = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = len(
        tools
    )  # This should be replaced with a proper count query in a real app

    return {
        "items": tools,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=ToolResponse)
async def get_tool_by_id(
    resource_id: UUID = Path(..., description="ID of the tool to get"),
    current_user: User = Depends(create_mixed_auth_dependency("read:tools")),
    db: Session = Depends(get_db),
):
    """
    Get a specific tool by ID.
    """
    logger.info(f"Current user {current_user.id} accessing get_tool_by_id")
    # Initialize service
    service = ToolService(db)
    tool = service.fetch_resource_by_id(resource_id)
    if not tool:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return tool


@router.post("/", response_model=ToolResponse, status_code=status.HTTP_201_CREATED)
async def create_tool(
    request: Request,
    tool_data: ToolCreate,
    current_user: User = Depends(create_mixed_auth_dependency("write:tools")),
    db: Session = Depends(get_db),
):
    """
    Create a new tool.
    """
    logger.info(f"Current user {current_user.id} accessing create_tool")
    # Add organization id
    tool_data.organization_id = request.state.user.organization_id

    # Initialize service
    service = ToolService(db)

    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters(
        {
            "component": tool_data.component.model_dump_json(),
            "organization_id": tool_data.organization_id,
        }
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(tool_data.model_dump())


@router.put("/{resource_id}", response_model=ToolResponse)
async def update_tool(
    request: Request,
    resource_id: UUID = Path(..., description="ID of the tool to update"),
    tool_data: ToolUpdate = None,
    current_user: User = Depends(create_mixed_auth_dependency("write:tools")),
    db: Session = Depends(get_db),
):
    """
    Update a tool.
    """
    logger.info(f"Current user {current_user.id} accessing update_tool")
    # Initialize service
    service = ToolService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = tool_data.component.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    existing_data = existing.component

    updated_data = update_component_fields(existing_data, data)
    tool_data.component = updated_data
    tool_data.organization_id = request.state.user.organization_id

    tool = service.update_resource_by_id(resource_id, tool_data.model_dump())
    return tool


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_tool(
    resource_id: UUID = Path(..., description="ID of the tool to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    current_user: User = Depends(create_mixed_auth_dependency("write:tools")),
    db: Session = Depends(get_db),
):
    """
    Delete a tool. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    logger.info(f"Current user {current_user.id} accessing delete_tool")
    # Initialize service
    service = ToolService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=ToolResponse)
async def restore_tool(
    resource_id: UUID = Path(..., description="ID of the tool to restore", gt=0),
    current_user: User = Depends(create_mixed_auth_dependency("write:tools")),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted tool.
    """
    logger.info(f"Current user {current_user.id} accessing restore_tool")
    # Initialize service
    service = ToolService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource
