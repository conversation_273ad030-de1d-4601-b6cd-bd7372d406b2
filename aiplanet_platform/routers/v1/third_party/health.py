# aiplanet_platform/routers/v1/third_party/health.py
"""
Third-party API endpoints for system health and information.
These endpoints provide system status and API information to external applications.
"""
from datetime import datetime

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.enhanced_security import (
    APIKeyAuth,
    create_api_key_auth,
)
from aiplanet_platform.core.rate_limiter import rate_limiter
from aiplanet_platform.services.api_key_service import APIKeyService

router = APIRouter(
    prefix="/health",
    tags=["third-party health"],
)


@router.get("/")
async def health_check():
    """
    Basic health check endpoint.

    **Authentication:** None required

    **Rate Limits:** None

    **Returns:** Basic system status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "AI Planet Platform API",
        "version": "1.0.0",
    }


@router.get("/info")
async def api_info():
    """
    Get API information and available endpoints.

    **Authentication:** None required

    **Rate Limits:** None

    **Returns:** API information and documentation links
    """
    return {
        "api_name": "AI Planet Platform Third-Party API",
        "version": "1.0.0",
        "description": "Third-party API access for AI Planet Platform",
        "documentation": {
            "openapi": "/api/v1/docs",
            "redoc": "/api/v1/redoc",
            "github": "https://github.com/aiplanethub/aiplanet_platform",
        },
        "authentication": {
            "type": "API Key",
            "headers": [
                "Authorization: Bearer YOUR_API_KEY",
                "X-API-Key: YOUR_API_KEY",
            ],
        },
        "rate_limits": {
            "default": {"per_minute": 100, "per_hour": 1000, "per_day": 10000},
            "headers": ["X-RateLimit-Remaining-Minute", "X-RateLimit-Reset-Minute"],
        },
        "available_scopes": APIKeyService.get_available_scopes(),
        "endpoints": {
            "users": "/api/v1/third-party/users",
            "organizations": "/api/v1/third-party/organizations",
            "agents": "/api/v1/third-party/agents",
            "tools": "/api/v1/third-party/tools",
            "sessions": "/api/v1/third-party/sessions",
        },
    }


@router.get("/authenticated")
async def authenticated_health_check(
    api_key: APIKeyAuth = Depends(create_api_key_auth()),
    db: Session = Depends(get_db),
):
    """
    Authenticated health check with API key status.

    **Required Scope:** None (any valid API key)

    **Rate Limits:** Subject to API key rate limits

    **Returns:** API key status and rate limit information
    """
    rate_info = rate_limiter.get_rate_limit_info(api_key)
    remaining = rate_limiter.get_remaining_requests(api_key)

    return {
        "status": "authenticated",
        "timestamp": datetime.utcnow().isoformat(),
        "api_key": {
            "id": api_key.id,
            "name": api_key.name,
            "prefix": api_key.key_prefix,
            "scopes": api_key.scopes,
            "organization_id": str(api_key.organization_id)
            if api_key.organization_id
            else None,
            "is_active": api_key.is_active,
            "expires_at": api_key.expires_at.isoformat()
            if api_key.expires_at
            else None,
            "last_used_at": api_key.last_used_at.isoformat()
            if api_key.last_used_at
            else None,
            "total_requests": api_key.total_requests,
        },
        "rate_limits": {
            "minute": {
                "limit": rate_info["minute"]["limit"],
                "used": rate_info["minute"]["used"],
                "remaining": remaining["minute"],
                "reset_at": rate_info["minute"]["reset_at"].isoformat()
                if rate_info["minute"]["reset_at"]
                else None,
            },
            "hour": {
                "limit": rate_info["hour"]["limit"],
                "used": rate_info["hour"]["used"],
                "remaining": remaining["hour"],
                "reset_at": rate_info["hour"]["reset_at"].isoformat()
                if rate_info["hour"]["reset_at"]
                else None,
            },
            "day": {
                "limit": rate_info["day"]["limit"],
                "used": rate_info["day"]["used"],
                "remaining": remaining["day"],
                "reset_at": rate_info["day"]["reset_at"].isoformat()
                if rate_info["day"]["reset_at"]
                else None,
            },
        },
    }


@router.get("/rate-limits")
async def get_rate_limit_status(
    api_key: APIKeyAuth = Depends(create_api_key_auth()),
):
    """
    Get current rate limit status for the API key.

    **Required Scope:** None (any valid API key)

    **Rate Limits:** Subject to API key rate limits

    **Returns:** Detailed rate limit information
    """
    rate_info = rate_limiter.get_rate_limit_info(api_key)
    remaining = rate_limiter.get_remaining_requests(api_key)
    is_limited = rate_limiter.is_rate_limited(api_key)

    return {
        "api_key_id": api_key.id,
        "is_rate_limited": is_limited,
        "windows": {
            "minute": {
                "limit": rate_info["minute"]["limit"],
                "used": rate_info["minute"]["used"],
                "remaining": remaining["minute"],
                "reset_at": rate_info["minute"]["reset_at"].isoformat()
                if rate_info["minute"]["reset_at"]
                else None,
                # "window_start": rate_info["minute"]["window_start"].isoformat()
            },
            "hour": {
                "limit": rate_info["hour"]["limit"],
                "used": rate_info["hour"]["used"],
                "remaining": remaining["hour"],
                "reset_at": rate_info["hour"]["reset_at"].isoformat(),
                # "window_start": rate_info["hour"]["window_start"].isoformat()
            },
            "day": {
                "limit": rate_info["day"]["limit"],
                "used": rate_info["day"]["used"],
                "remaining": remaining["day"],
                "reset_at": rate_info["day"]["reset_at"].isoformat(),
                # "window_start": rate_info["day"]["window_start"].isoformat()
            },
        },
    }


@router.get("/scope-info")
async def get_scope_information():
    """
    Get information about available API scopes.

    **Authentication:** None required

    **Rate Limits:** None

    **Returns:** Available scopes and their descriptions
    """
    available_scopes = APIKeyService.get_available_scopes()

    # Group scopes by category
    scope_categories = {}
    for scope_info in available_scopes:
        category = scope_info["category"]
        if category not in scope_categories:
            scope_categories[category] = []
        scope_categories[category].append(
            {"scope": scope_info["scope"], "description": scope_info["description"]}
        )

    return {
        "total_scopes": len(available_scopes),
        "categories": scope_categories,
        "scope_format": "action:resource (e.g., read:users, write:agents)",
        "documentation": "Scopes determine which API endpoints your key can access",
    }
