"""
Router for input endpoints
"""
import logging
from typing import Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status, Request
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.input import (
    InputCreate,
    InputList,
    InputResponse,
    InputUpdate,
)
from aiplanet_platform.services.input_service import InputService
from aiplanet_platform.utils.update_resource import update_component_fields
from aiplanet_platform.core.enhanced_security import (
    APIKeyAuth,
    create_api_key_auth,
    get_organization_id,
)

router = APIRouter(
    prefix="/inputs",
    tags=["third-party inputs"],
)
logger = logging.getLogger(__name__)


@router.get("/", response_model=InputList)
async def get_inputs(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:inputs")),
    db: Session = Depends(get_db),
):
    """
    Get a list of inputs with filtering, sorting, and pagination.
    """
    organization_id = get_organization_id(api_key)
    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if organization_id:
        filters["organization_id"] = organization_id

    service = InputService(db)
    inputs = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = len(
        inputs
    )  # This should be replaced with a proper count query in a real app

    return {
        "items": inputs,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=InputResponse)
async def get_input_by_id(
    resource_id: UUID = Path(..., description="ID of the input to get"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:inputs")),
    db: Session = Depends(get_db),
):
    """
    Get a specific input by ID.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing get_input_by_id"
    )
    service = InputService(db)
    input = service.fetch_resource_by_id(resource_id)
    if not input:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return input


@router.post("/", response_model=InputResponse, status_code=status.HTTP_201_CREATED)
async def create_input(
    request: Request,
    input_data: InputCreate,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:inputs")),
    db: Session = Depends(get_db),
):
    """
    Create a new input.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing create_input"
    )
    # Add organization id
    input_data.organization_id = organization_id

    # Initialize service
    service = InputService(db)

    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters(
        {
            "component": input_data.component.model_dump_json(),
            "organization_id": input_data.organization_id,
        }
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(input_data.model_dump())


@router.put("/{resource_id}", response_model=InputResponse)
async def update_input(
    request: Request,
    resource_id: UUID = Path(..., description="ID of the input to update"),
    input_data: InputUpdate = None,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:inputs")),
    db: Session = Depends(get_db),
):
    """
    Update a input.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing update_input"
    )
    service = InputService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = input_data.component.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    existing_data = existing.component

    updated_data = update_component_fields(existing_data, data)
    input_data.component = updated_data
    input_data.organization_id = organization_id

    input = service.update_resource_by_id(resource_id, input_data.model_dump())
    return input


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_input(
    resource_id: UUID = Path(..., description="ID of the input to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:inputs")),
    db: Session = Depends(get_db),
):
    """
    Delete a input. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing delete_input"
    )
    service = InputService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=InputResponse)
async def restore_input(
    resource_id: UUID = Path(..., description="ID of the input to restore", gt=0),
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:inputs")),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted input.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing restore_input"
    )
    service = InputService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource
