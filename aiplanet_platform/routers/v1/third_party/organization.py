# aiplanet_platform/routers/v1/third_party/organization.py
"""
Third-party API endpoints for organization management.
These endpoints allow external applications to access organization data.
"""
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.enhanced_security import (
    APIKeyAuth,
    create_api_key_auth,
    get_organization_id,
)
from aiplanet_platform.schemas.organization import (
    OrganizationResponse,
)
from aiplanet_platform.services.organization_service import OrganizationService

router = APIRouter(
    prefix="/organizations",
    tags=["third-party organizations"],
)


@router.get("/current", response_model=OrganizationResponse)
async def get_current_organization(
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:organizations")),
    db: Session = Depends(get_db),
):
    """
    Get the current organization associated with the API key.

    **Required Scope:** `read:organizations`

    **Rate Limits:** Subject to API key rate limits

    **Returns:** Organization details for the API key's organization
    """
    organization_id = get_organization_id(api_key)

    if not organization_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No organization associated with this API key",
        )

    service = OrganizationService(db)
    organization = service.fetch_resource_by_id(organization_id)

    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
        )

    return organization


@router.get("/current/users")
async def get_organization_users(
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(
        100, description="Maximum number of records to return", le=1000, gt=0
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    api_key: APIKeyAuth = Depends(
        create_api_key_auth("read:organizations", "read:users")
    ),
    db: Session = Depends(get_db),
):
    """
    Get users in the current organization.

    **Required Scopes:** `read:organizations` AND `read:users`

    **Rate Limits:** Subject to API key rate limits

    **Returns:** List of users in the API key's organization
    """
    organization_id = get_organization_id(api_key)

    if not organization_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No organization associated with this API key",
        )

    # Import here to avoid circular imports
    from aiplanet_platform.services.user_service import UserService

    # Build filters
    filters = {"organization_id": organization_id}
    if is_active is not None:
        filters["is_active"] = is_active

    user_service = UserService(db)
    users = user_service.fetch_resource_by_filters(filters, skip=skip, limit=limit)

    total_count = user_service.count_resources(filters)

    return {
        "items": users,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
        "organization_id": str(organization_id),
    }


@router.get("/current/stats")
async def get_organization_stats(
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:organizations")),
    db: Session = Depends(get_db),
):
    """
    Get statistics for the current organization.

    **Required Scope:** `read:organizations`

    **Rate Limits:** Subject to API key rate limits

    **Returns:** Organization statistics and metrics
    """
    organization_id = get_organization_id(api_key)

    if not organization_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No organization associated with this API key",
        )

    # Import services here to avoid circular imports
    from aiplanet_platform.services.user_service import UserService
    from aiplanet_platform.services.api_key_service import APIKeyService

    user_service = UserService(db)
    api_key_service = APIKeyService(db)

    # Get user statistics
    total_users = user_service.count_resources({"organization_id": organization_id})
    active_users = user_service.count_resources(
        {"organization_id": organization_id, "is_active": True}
    )

    # Get API key statistics
    total_api_keys = api_key_service.count_resources(
        {"organization_id": organization_id}
    )
    active_api_keys = api_key_service.count_resources(
        {"organization_id": organization_id, "is_active": True}
    )

    return {
        "organization_id": str(organization_id),
        "users": {
            "total": total_users,
            "active": active_users,
            "inactive": total_users - active_users,
        },
        "api_keys": {
            "total": total_api_keys,
            "active": active_api_keys,
            "inactive": total_api_keys - active_api_keys,
        },
    }


@router.get("/current/api-keys")
async def get_organization_api_keys(
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(
        100, description="Maximum number of records to return", le=1000, gt=0
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    api_key: APIKeyAuth = Depends(
        create_api_key_auth("read:organizations", "read:api_keys")
    ),
    db: Session = Depends(get_db),
):
    """
    Get API keys for the current organization.

    **Required Scopes:** `read:organizations` AND `read:api_keys`

    **Rate Limits:** Subject to API key rate limits

    **Returns:** List of API keys in the organization (excluding sensitive data)
    """
    organization_id = get_organization_id(api_key)

    if not organization_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No organization associated with this API key",
        )

    from aiplanet_platform.services.api_key_service import APIKeyService

    # Build filters
    filters = {"organization_id": organization_id}
    if is_active is not None:
        filters["is_active"] = is_active

    api_key_service = APIKeyService(db)
    api_keys = api_key_service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit
    )

    # Remove sensitive information
    safe_api_keys = []
    for key in api_keys:
        safe_api_keys.append(
            {
                "id": key.id,
                "name": key.name,
                "key_prefix": key.key_prefix,
                "scopes": key.scopes,
                "is_active": key.is_active,
                "created_at": key.created_at,
                "expires_at": key.expires_at,
                "last_used_at": key.last_used_at,
                "total_requests": key.total_requests,
                "rate_limit_per_minute": key.rate_limit_per_minute,
                "rate_limit_per_hour": key.rate_limit_per_hour,
                "rate_limit_per_day": key.rate_limit_per_day,
            }
        )

    total_count = api_key_service.count_resources(filters)

    return {
        "items": safe_api_keys,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
        "organization_id": str(organization_id),
    }
