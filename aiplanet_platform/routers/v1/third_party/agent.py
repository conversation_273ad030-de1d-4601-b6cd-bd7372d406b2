"""
Router for agent endpoints
"""
import logging
from typing import Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status, Request
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.agent import (
    <PERSON><PERSON><PERSON>,
    AgentList,
    AgentResponse,
    AgentUpdate,
)
from aiplanet_platform.services.agent_service import AgentService
from aiplanet_platform.utils.update_resource import update_component_fields
from aiplanet_platform.core.enhanced_security import (
    APIKeyAuth,
    create_api_key_auth,
    get_organization_id,
)

router = APIRouter(
    prefix="/agents",
    tags=["third-party agents"],
)
logger = logging.getLogger(__name__)


@router.get("/", response_model=AgentList)
async def get_agents(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:agents")),
    db: Session = Depends(get_db),
):
    """
    Get a list of agents with filtering, sorting, and pagination.
    """
    organization_id = get_organization_id(api_key)

    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if organization_id:
        filters["organization_id"] = organization_id

    service = AgentService(db)
    agents = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    # Get total count for pagination
    total_count = service.count_resources(filters)

    return {
        "items": agents,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=AgentResponse)
async def get_agent_by_id(
    resource_id: UUID = Path(..., description="ID of the agent to get"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:agents")),
    db: Session = Depends(get_db),
):
    """
    Get a specific agent by ID.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing get_agent_by_id"
    )
    # Initialize service
    service = AgentService(db)
    agent = service.fetch_resource_by_id(resource_id)
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return agent


@router.post("/", response_model=AgentResponse, status_code=status.HTTP_201_CREATED)
async def create_agent(
    request: Request,
    agent_data: AgentCreate,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:agents")),
    db: Session = Depends(get_db),
):
    """
    Create a new agent.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing create_agent"
    )

    # Add organization id
    agent_data.organization_id = organization_id

    # Initialize service
    service = AgentService(db)

    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters(
        {
            "component": agent_data.component.model_dump_json(),
            "organization_id": agent_data.organization_id,
        }
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    # Extract tool_ids before creating agent
    tool_ids = agent_data.tool_ids or []

    return service.create_resource(
        agent_data.model_dump(exclude={"tool_ids"}), tool_ids
    )


@router.put("/{resource_id}", response_model=AgentResponse)
async def update_agent(
    request: Request,
    resource_id: UUID = Path(..., description="ID of the agent to update"),
    agent_data: AgentUpdate = None,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:agents")),
    db: Session = Depends(get_db),
):
    """
    Update a agent.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing update_agent"
    )
    # Initialize service
    service = AgentService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = agent_data.component.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    existing_data = existing.component

    updated_data = update_component_fields(existing_data, data)
    agent_data.component = updated_data
    agent_data.organization_id = organization_id
    tool_ids = agent_data.tool_ids or []

    agent = service.update_resource_by_id(
        resource_id, agent_data.model_dump(exclude={"tool_ids"}), tool_ids
    )
    return agent


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_agent(
    resource_id: UUID = Path(..., description="ID of the agent to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:agents")),
    db: Session = Depends(get_db),
):
    """
    Delete a agent. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing delete_agent"
    )
    # Initialize service
    service = AgentService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=AgentResponse)
async def restore_agent(
    resource_id: UUID = Path(..., description="ID of the agent to restore", gt=0),
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:agents")),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted agent.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing restore_agent"
    )
    # Initialize service
    service = AgentService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource
