# aiplanet_platform/routers/v1/third_party/__init__.py
"""
Third-party API router.
This module provides API endpoints for external applications using API keys.
"""
from fastapi import APIRouter

from aiplanet_platform.routers.v1.third_party.health import router as health_router
from aiplanet_platform.routers.v1.third_party.agent import router as agent_router
from aiplanet_platform.routers.v1.third_party.tool import router as tool_router
from aiplanet_platform.routers.v1.third_party.session import router as session_router
from aiplanet_platform.routers.v1.third_party.model import router as model_router
from aiplanet_platform.routers.v1.third_party.termination_condition import (
    router as termination_condition_router,
)
from aiplanet_platform.routers.v1.third_party.output import router as output_router
from aiplanet_platform.routers.v1.third_party.input import router as input_router
from aiplanet_platform.routers.v1.third_party.validation import (
    router as validation_router,
)
from aiplanet_platform.routers.v1.third_party.team import router as team_router

router = APIRouter(
    prefix="/third-party",
    tags=["third-party apis"],
)

# Include all third-party routers
router.include_router(health_router)
router.include_router(model_router)
router.include_router(tool_router)
router.include_router(agent_router)
router.include_router(session_router)
router.include_router(termination_condition_router)
router.include_router(output_router)
router.include_router(input_router)
router.include_router(validation_router)
router.include_router(team_router)
