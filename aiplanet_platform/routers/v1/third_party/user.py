# aiplanet_platform/routers/v1/third_party/user.py
"""
Third-party API endpoints for user management.
These endpoints are used by external applications with API keys.
"""
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.enhanced_security import (
    APIKeyAuth,
    create_api_key_auth,
    get_organization_id,
)
from aiplanet_platform.schemas.user import UserList, UserResponse
from aiplanet_platform.services.user_service import UserService

router = APIRouter(
    prefix="/users",
    tags=["third-party users"],
)


@router.get("/", response_model=UserList)
async def list_users(
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(
        100, description="Maximum number of records to return", le=1000, gt=0
    ),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    email: Optional[str] = Query(None, description="Filter by email"),
    email_like: Optional[str] = Query(
        None, description="Filter by email (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:users")),
    db: Session = Depends(get_db),
):
    """
    List users for third-party applications.

    **Required Scope:** `read:users`

    **Rate Limits:** Subject to API key rate limits

    **Organization Isolation:** Only returns users from the same organization as the API key
    """
    # Get organization ID from API key for data isolation
    organization_id = get_organization_id(api_key)

    # Build filters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if email:
        filters["email"] = email
    if email_like:
        filters["email_like"] = email_like
    if is_active is not None:
        filters["is_active"] = is_active
    if organization_id:
        filters["organization_id"] = organization_id

    service = UserService(db)
    users = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    # Get total count for pagination
    total_count = service.count_resources(filters)

    return {
        "items": users,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{user_id}", response_model=UserResponse)
async def get_user_by_id(
    user_id: UUID,
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:users")),
    db: Session = Depends(get_db),
):
    """
    Get a specific user by ID for third-party applications.

    **Required Scope:** `read:users`

    **Organization Isolation:** Only returns users from the same organization as the API key
    """
    organization_id = get_organization_id(api_key)

    service = UserService(db)
    user = service.fetch_resource_by_id(user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Check organization isolation
    if organization_id and user.organization_id != organization_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    return user


@router.get("/by-email/{email}", response_model=UserResponse)
async def get_user_by_email(
    email: str,
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:users")),
    db: Session = Depends(get_db),
):
    """
    Get a user by email address for third-party applications.

    **Required Scope:** `read:users`

    **Organization Isolation:** Only returns users from the same organization as the API key
    """
    organization_id = get_organization_id(api_key)

    service = UserService(db)

    # Build filters for email lookup
    filters = {"email": email}
    if organization_id:
        filters["organization_id"] = organization_id

    users = service.fetch_resource_by_filters(filters, limit=1)

    if not users:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    return users[0]


@router.get("/stats/summary")
async def get_user_stats(
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:users")),
    db: Session = Depends(get_db),
):
    """
    Get user statistics for third-party applications.

    **Required Scope:** `read:users`

    **Returns:** Summary statistics about users in the organization
    """
    organization_id = get_organization_id(api_key)

    service = UserService(db)

    # Build base filter with organization
    base_filters = {}
    if organization_id:
        base_filters["organization_id"] = organization_id

    # Get various counts
    total_users = service.count_resources(base_filters)
    active_users = service.count_resources({**base_filters, "is_active": True})
    inactive_users = service.count_resources({**base_filters, "is_active": False})

    return {
        "total_users": total_users,
        "active_users": active_users,
        "inactive_users": inactive_users,
        "organization_id": str(organization_id) if organization_id else None,
    }
