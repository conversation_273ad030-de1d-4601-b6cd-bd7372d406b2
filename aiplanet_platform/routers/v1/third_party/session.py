"""
Router for session endpoints
"""
import logging
from uuid import <PERSON>UI<PERSON>
from typing import Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Request
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.services.session_service import SessionService
from aiplanet_platform.services.run_service import RunService
from aiplanet_platform.schemas.session import (
    SessionCreate,
    SessionUpdate,
    SessionResponse,
    SessionList,
    RunCreate,
)
from aiplanet_platform.schemas.run import RunChatMessage
from aiplanet_platform.core.enhanced_security import (
    APIKeyAuth,
    create_api_key_auth,
    get_organization_id,
    get_user_id,
)


router = APIRouter(
    prefix="/sessions",
    tags=["third-party sessions"],
)
logger = logging.getLogger(__name__)


@router.get("/", response_model=SessionList)
async def get_sessions(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[list[str]] = Query([], description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    team_id: Optional[UUID] = Query(None, description="Filter by team ID"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:sessions")),
    db: Session = Depends(get_db),
):
    """
    Get a list of sessions with filtering, sorting, and pagination.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing get_sessions"
    )
    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if team_id:
        filters["team_id"] = team_id
    if organization_id:
        filters["organization_id"] = organization_id

    service = SessionService(db)
    sessions = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = service.count_resources(filters)

    return {
        "items": sessions,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=SessionResponse)
async def get_session_by_id(
    resource_id: UUID = Path(..., description="ID of the session to get"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:sessions")),
    db: Session = Depends(get_db),
):
    """
    Get a specific session by ID.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing get_session_by_id"
    )

    # Initialize service
    service = SessionService(db)
    session = service.fetch_resource_by_id(resource_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return session


@router.post("/", response_model=SessionResponse, status_code=status.HTTP_201_CREATED)
async def create_session(
    session_data: SessionCreate,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:sessions")),
    db: Session = Depends(get_db),
):
    """
    Create a new session.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing create_session"
    )

    # Initialize service
    service = SessionService(db)

    return service.create_resource(session_data.model_dump())


@router.put("/{resource_id}", response_model=SessionResponse)
async def update_session(
    resource_id: UUID = Path(..., description="ID of the session to update"),
    session_data: SessionUpdate = None,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:sessions")),
    db: Session = Depends(get_db),
):
    """
    Update a session.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing update_session"
    )
    # Initialize service
    service = SessionService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = session_data.model_dump(exclude_unset=True)

    # Check if name is being updated and if it conflicts with existing resources
    if "name" in data and data["name"] != existing.name:
        name_conflicts = service.fetch_resource_by_filters({"name": data["name"]})
        if name_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Resource with this name already exists",
            )

    session = service.update_resource_by_id(resource_id, data)
    return session


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_session(
    resource_id: UUID = Path(..., description="ID of the session to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("delete:sessions")),
    db: Session = Depends(get_db),
):
    """
    Delete a session. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing delete_session"
    )
    # Initialize service
    service = SessionService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=SessionResponse)
async def restore_session(
    resource_id: UUID = Path(..., description="ID of the session to restore", gt=0),
    api_key: APIKeyAuth = Depends(create_api_key_auth("delete:sessions")),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted session.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing restore_session"
    )

    # Initialize service
    service = SessionService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource


@router.post("/generate-run")
async def run_session(
    request: Request,
    run_data: RunCreate,
    api_key: APIKeyAuth = Depends(create_api_key_auth("write:sessions")),
    db: Session = Depends(get_db),
):
    """
    Run a session.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing generate-run"
    )
    # Initialize service
    service = RunService(db)

    return service.create_resource(run_data.model_dump())


@router.post("/chat/{run_id}")
async def get_run(
    request: Request,
    chat_data: RunChatMessage,
    run_id: UUID = Path(..., description="ID of the run to get"),
    api_key: APIKeyAuth = Depends(create_api_key_auth("read:sessions")),
    db: Session = Depends(get_db),
):
    """
    Get a specific run by ID.
    """
    organization_id = get_organization_id(api_key)
    logger.info(
        f"Current organization {organization_id} using api key for accessing get_run"
    )
    # Initialize service
    service = RunService(db)

    user_id = get_user_id(api_key)
    result = await service.chat_run(run_id, user_id, chat_data)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return result
