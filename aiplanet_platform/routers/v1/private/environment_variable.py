from fastapi import APIRouter, Depends, HTTPException, Query, Request
from typing import List
from uuid import UUID
from sqlalchemy.orm import Session

from aiplanet_platform.models.environment_variable import EnvironmentVariableType
from aiplanet_platform.services.environment_variable_service import (
    EnvironmentVariableService,
)
from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.environment_variable import (
    EnvironmentVariableCreate,
    EnvironmentVariableUpdate,
    EnvironmentVariableOut,
)

router = APIRouter(prefix="/envs", tags=["Environment Variables"])


@router.post("/", response_model=EnvironmentVariableOut)
def create_env_var(
    request: Request,
    data: EnvironmentVariableCreate,
    db: Session = Depends(get_db),
):
    try:
        current_user = request.state.user
        service = EnvironmentVariableService(db)
        data_dict = data.model_dump()
        data_dict["user_id"] = current_user.id
        env_var = service.create_resource(data_dict)

        return {
            "id": str(env_var.id),
            "name": str(env_var.name),
            "value": (
                env_var.value_prefix
                if env_var.type == EnvironmentVariableType.secret  # type:ignore
                else str(env_var.value)
            ),
            "type": str(env_var.type),
            "required": bool(env_var.required),
            "created_at": str(env_var.created_at),
            "updated_at": str(env_var.updated_at),
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to create environment variable: {str(e)}"
        )


@router.get("/")
def get_env_vars_by_user(
    request: Request,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of records to return"
    ),
    name_like: str = Query(None, description="Filter by name (partial match)"),
    sort: str = Query("name", description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order: 'asc' or 'desc'"),
    db: Session = Depends(get_db),
):
    current_user = request.state.user
    service = EnvironmentVariableService(db)
    filters = {"user_id": current_user.id}
    allowed_sort_fields = {"created_at", "updated_at", "name"}
    if sort not in allowed_sort_fields:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid sort field: {sort}. Allowed fields: created_at, updated_at, name",
        )
    # Add name_like to filters for DB-level filtering
    if name_like:
        filters["name_like"] = name_like
    try:
        env_vars = service.fetch_resource_by_filters(
            filters, skip=skip, limit=limit, sort_by=sort, sort_order=sort_order
        )
        total_count = (
            service.db.query(service.model).filter_by(user_id=current_user.id).count()
        )
        page = skip // limit + 1 if limit > 0 else 1
        pages = (total_count + limit - 1) // limit if limit > 0 else 1

        response = {
            "items": [
                {
                    "id": str(env.id),
                    "name": str(env.name),
                    "value": (
                        env.value_prefix
                        if env.type == EnvironmentVariableType.secret  # type:ignore
                        else str(env.value)
                    ),
                    "type": str(env.type.value),
                    "required": bool(env.required),
                    "created_at": str(env.created_at),
                    "updated_at": str(env.updated_at),
                }
                for env in env_vars
            ],
            "total": total_count,
            "page": page,
            "pages": pages,
            "size": limit,
        }

        return response
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to fetch environment variables: {str(e)}"
        )


@router.put("/{env_var_id}", response_model=EnvironmentVariableOut)
def update_env_var(
    env_var_id: UUID,
    data: EnvironmentVariableUpdate,
    db: Session = Depends(get_db),
):
    service = EnvironmentVariableService(db)
    env_var = service.update_resource_by_id(
        env_var_id, {k: v for k, v in data.model_dump().items() if v is not None}
    )
    if not env_var:
        raise HTTPException(status_code=404, detail="Environment variable not found")
    return EnvironmentVariableOut.model_validate(env_var)


@router.delete("/{env_var_id}/hard", response_model=dict)
def hard_delete_env_var(
    env_var_id: UUID,
    db: Session = Depends(get_db),
):
    try:
        service = EnvironmentVariableService(db)
        success = service.hard_delete_resource_by_id(env_var_id)
        if not success:
            raise HTTPException(
                status_code=404, detail="Environment variable not found"
            )
        return {"success": True}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to delete environment variable: {str(e)}"
        )
