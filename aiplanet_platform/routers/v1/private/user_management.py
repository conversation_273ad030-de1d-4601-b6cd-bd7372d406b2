"""
User rate limit and scope management endpoints
"""
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.enhanced_security import get_current_user
from aiplanet_platform.models.user import User
from aiplanet_platform.services.user_service import UserService
from aiplanet_platform.constants.api_key import API_PERMISSIONS
from aiplanet_platform.core.rate_limiter import rate_limiter

router = APIRouter(prefix="/admin", tags=["user management"])


class UserRateLimitUpdate(BaseModel):
    """Schema for updating user rate limits"""

    rate_limit_per_minute: Optional[int] = Field(None, ge=1, le=10000)
    rate_limit_per_hour: Optional[int] = Field(None, ge=1, le=100000)
    rate_limit_per_day: Optional[int] = Field(None, ge=1, le=1000000)


class UserScopeUpdate(BaseModel):
    """Schema for updating user scopes"""

    scopes: List[str] = Field(..., description="List of scopes to assign to user")


class UserRateLimitResponse(BaseModel):
    """Response schema for user rate limits"""

    user_id: UUID
    rate_limit_per_minute: int
    rate_limit_per_hour: int
    rate_limit_per_day: int
    current_usage: dict


class UserScopeResponse(BaseModel):
    """Response schema for user scopes"""

    user_id: UUID
    scopes: List[str]


@router.get("/users/{user_id}/rate-limits", response_model=UserRateLimitResponse)
async def get_user_rate_limits(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get rate limits for a specific user.
    Only admin users or the user themselves can access this.
    """
    # Check if current user can access this information
    if current_user.id != user_id and current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied. You can only view your own rate limits or be an admin.",
        )

    service = UserService(db)
    user = service.fetch_resource_by_id(user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Get current usage from rate limiter
    current_usage = rate_limiter.get_rate_limit_info(user)

    return UserRateLimitResponse(
        user_id=user.id,
        rate_limit_per_minute=user.rate_limit_per_minute,
        rate_limit_per_hour=user.rate_limit_per_hour,
        rate_limit_per_day=user.rate_limit_per_day,
        current_usage=current_usage,
    )


@router.put("/users/{user_id}/rate-limits", response_model=UserRateLimitResponse)
async def update_user_rate_limits(
    user_id: UUID,
    rate_limit_data: UserRateLimitUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update rate limits for a specific user.
    Only admin users can update rate limits.
    """
    # Only admins can update rate limits
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied. Only admins can update user rate limits.",
        )

    service = UserService(db)
    user = service.fetch_resource_by_id(user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Update only provided fields
    update_data = {}
    if rate_limit_data.rate_limit_per_minute is not None:
        update_data["rate_limit_per_minute"] = rate_limit_data.rate_limit_per_minute
    if rate_limit_data.rate_limit_per_hour is not None:
        update_data["rate_limit_per_hour"] = rate_limit_data.rate_limit_per_hour
    if rate_limit_data.rate_limit_per_day is not None:
        update_data["rate_limit_per_day"] = rate_limit_data.rate_limit_per_day

    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No rate limit fields provided for update",
        )

    # Update the user
    updated_user = service.update_resource_by_id(user_id, update_data)

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user rate limits",
        )

    # Reset rate limiter for this user to apply new limits immediately
    rate_limiter.reset_limits(updated_user)

    # Get current usage from rate limiter
    current_usage = rate_limiter.get_rate_limit_info(updated_user)

    return UserRateLimitResponse(
        user_id=updated_user.id,
        rate_limit_per_minute=updated_user.rate_limit_per_minute,
        rate_limit_per_hour=updated_user.rate_limit_per_hour,
        rate_limit_per_day=updated_user.rate_limit_per_day,
        current_usage=current_usage,
    )


@router.get("/users/{user_id}/scopes", response_model=UserScopeResponse)
async def get_user_scopes(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get scopes for a specific user.
    Only admin users or the user themselves can access this.
    """
    # Check if current user can access this information
    if current_user.id != user_id and current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied. You can only view your own scopes or be an admin.",
        )

    service = UserService(db)
    user = service.fetch_resource_by_id(user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    return UserScopeResponse(user_id=user.id, scopes=user.scopes or ["*"])


@router.put("/users/{user_id}/scopes", response_model=UserScopeResponse)
async def update_user_scopes(
    user_id: UUID,
    scope_data: UserScopeUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update scopes for a specific user.
    Only admin users can update scopes.
    """
    # Only admins can update scopes
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied. Only admins can update user scopes.",
        )

    service = UserService(db)
    user = service.fetch_resource_by_id(user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Validate scopes
    valid_scopes = [perm["scope"] for perm in API_PERMISSIONS] + ["*"]
    invalid_scopes = [scope for scope in scope_data.scopes if scope not in valid_scopes]

    if invalid_scopes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid scopes: {invalid_scopes}. Valid scopes are: {valid_scopes}",
        )

    # Update the user
    updated_user = service.update_resource_by_id(user_id, {"scopes": scope_data.scopes})

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user scopes",
        )

    return UserScopeResponse(user_id=updated_user.id, scopes=updated_user.scopes)


@router.get("/available-scopes")
async def get_available_scopes(
    current_user: User = Depends(get_current_user),
):
    """
    Get all available scopes that can be assigned to users.
    """
    return {
        "scopes": API_PERMISSIONS
        + [
            {
                "scope": "*",
                "description": "All permissions (wildcard)",
                "category": "System",
            }
        ]
    }


@router.post("/users/{user_id}/reset-rate-limits")
async def reset_user_rate_limits(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Reset rate limiting counters for a specific user.
    Only admin users can reset rate limits.
    """
    # Only admins can reset rate limits
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied. Only admins can reset user rate limits.",
        )

    service = UserService(db)
    user = service.fetch_resource_by_id(user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Reset rate limiter for this user
    rate_limiter.reset_limits(user)

    return {"message": f"Rate limits reset for user {user_id}"}


@router.get("/users/{user_id}/rate-limit-status")
async def get_user_rate_limit_status(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get current rate limit status for a user.
    Shows remaining requests and reset times.
    """
    # Check if current user can access this information
    if current_user.id != user_id and current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied. You can only view your own rate limit status or be an admin.",
        )

    service = UserService(db)
    user = service.fetch_resource_by_id(user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Get detailed rate limit info
    rate_info = rate_limiter.get_rate_limit_info(user)
    remaining = rate_limiter.get_remaining_requests(user)

    return {
        "user_id": user_id,
        "rate_limits": {
            "per_minute": user.rate_limit_per_minute,
            "per_hour": user.rate_limit_per_hour,
            "per_day": user.rate_limit_per_day,
        },
        "current_usage": rate_info,
        "remaining_requests": remaining,
        "is_rate_limited": not rate_limiter.is_allowed(user),
    }
