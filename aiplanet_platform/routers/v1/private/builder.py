"""
Router for builder endpoints
"""
from typing import List

# from aiplanet_platform.schemas.builder import BuilderCreate, BuilderUpdate, BuilderResponse, BuilderList
from autogen_core import ComponentModel
from fastapi import APIRouter

from aiplanet_platform.services.builder_service import BuilderService

router = APIRouter(
    prefix="/builders",
    tags=["builders"],
)

builder_service = BuilderService()

# @router.get("/", response_model=BuilderList)
# async def get_builders(
#     skip: int = Query(0, description="Number of records to skip", ge=0),
#     limit: int = Query(100, description="Maximum number of records to return", le=1000),
#     sort_by: Optional[str] = Query(None, description="Field to sort by"),
#     sort_order: str = Query("asc", description="Sort order (asc or desc)"),
#     name: Optional[str] = Query(None, description="Filter by name"),
#     name_like: Optional[str] = Query(None, description="Filter by name (partial match)"),
#     is_active: Optional[bool] = Query(None, description="Filter by active status"),
#     db: Session = Depends(get_db),
# ):
#     """
#     Get a list of builders with filtering, sorting, and pagination.
#     """
#     # Build filters dictionary from query parameters
#     filters = {}
#     if name:
#         filters["name"] = name
#     if name_like:
#         filters["name_like"] = name_like
#     if is_active is not None:
#         filters["is_active"] = is_active

#     service = BuilderService(db)
#     builders = service.fetch_resource_by_filters(
#         filters,
#         skip=skip,
#         limit=limit,
#         sort_by=sort_by,
#         sort_order=sort_order
#     )

#     total_count = len(builders)  # This should be replaced with a proper count query in a real app

#     return {
#         "items": builders,
#         "total": total_count,
#         "page": skip // limit + 1 if limit > 0 else 1,
#         "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
#         "size": limit,
#     }


# @router.get("/{resource_id}", response_model=BuilderResponse)
# async def get_builder_by_id(
#     resource_id: UUID = Path(..., description="ID of the builder to get"),
#     db: Session = Depends(get_db),
# ):
#     """
#     Get a specific builder by ID.
#     """
#     service = BuilderService(db)
#     builder = service.fetch_resource_by_id(resource_id)
#     if not builder:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail="Resource not found"
#         )
#     return builder


# @router.post("/", response_model=BuilderResponse, status_code=status.HTTP_201_CREATED)
# async def create_builder(
#     builder_data: BuilderCreate,
#     db: Session = Depends(get_db),
# ):
#     """
#     Create a new builder.
#     """
#     service = BuilderService(db)

#     # Check if a resource with the same name already exists
#     existing = service.fetch_resource_by_filters({"name": builder_data.name})
#     if existing:
#         raise HTTPException(
#             status_code=status.HTTP_409_CONFLICT,
#             detail="Resource with this name already exists"
#         )

#     return service.create_resource(builder_data.model_dump())


# @router.put("/{resource_id}", response_model=BuilderResponse)
# async def update_builder(
#     resource_id: UUID = Path(..., description="ID of the builder to update"),
#     builder_data: BuilderUpdate = None,
#     db: Session = Depends(get_db),
# ):
#     """
#     Update a builder.
#     """
#     service = BuilderService(db)

#     # Verify resource exists
#     existing = service.fetch_resource_by_id(resource_id)
#     if not existing:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail="Resource not found"
#         )

#     # Exclude None values to implement partial updates
#     data = builder_data.model_dump(exclude_unset=True)

#     # Check if name is being updated and if it conflicts with existing resources
#     if "name" in data and data["name"] != existing.name:
#         name_conflicts = service.fetch_resource_by_filters({"name": data["name"]})
#         if name_conflicts:
#             raise HTTPException(
#                 status_code=status.HTTP_409_CONFLICT,
#                 detail="Resource with this name already exists"
#             )

#     builder = service.update_resource_by_id(resource_id, data)
#     return builder


# @router.delete("/{resource_id}", response_model=Dict[str, bool])
# async def delete_builder(
#     resource_id: UUID = Path(..., description="ID of the builder to delete"),
#     permanent: bool = Query(False, description="Permanently delete the resource"),
#     db: Session = Depends(get_db),
# ):
#     """
#     Delete a builder. By default, this is a soft delete.
#     Set permanent=true to permanently delete the resource.
#     """
#     service = BuilderService(db)

#     if permanent:
#         success = service.hard_delete_resource_by_id(resource_id)
#     else:
#         success = service.remove_resource_by_id(resource_id)

#     if not success:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail="Resource not found"
#         )

#     return {"success": True}


# @router.post("/{resource_id}/restore", response_model=BuilderResponse)
# async def restore_builder(
#     resource_id: UUID = Path(..., description="ID of the builder to restore", gt=0),
#     db: Session = Depends(get_db),
# ):
#     """
#     Restore a soft-deleted builder.
#     """
#     service = BuilderService(db)

#     # Try to fetch the resource including deleted ones
#     query = db.query(service.model).filter(service.model.id == resource_id)
#     resource = query.first()

#     if not resource:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail="Resource not found"
#         )

#     if not resource.is_deleted:
#         raise HTTPException(
#             status_code=status.HTTP_400_BAD_REQUEST,
#             detail="Resource is not deleted"
#         )

#     # Restore the resource
#     resource.is_deleted = False
#     db.commit()
#     db.refresh(resource)

#     return resource


@router.get("/agent_config", response_model=List[ComponentModel])
async def get_agent_config():
    """
    Get the agent config.
    """
    return builder_service.get_assistant_agent_config()


@router.get("/model_configs", response_model=List[ComponentModel])
async def get_model_configs():
    """
    Get the model configs.
    """
    return builder_service.get_model_configs()


@router.get("/tool_configs", response_model=List[ComponentModel])
async def get_tool_configs():
    """
    Get the tool configs.
    """
    return builder_service.get_tool_configs()


@router.get("/termination_condition_configs", response_model=List[ComponentModel])
async def get_termination_condition_configs():
    """
    Get the termination condition configs.
    """
    return builder_service.get_termination_condition_configs()


@router.get("/team_configs", response_model=List[ComponentModel])
async def get_team_configs():
    """
    Get the team configs.
    """
    return builder_service.get_team_configs()


@router.get("/input_configs", response_model=list[ComponentModel])
async def get_input_configs():
    """
    Get the input configs.
    """
    return builder_service.get_input_configs()


@router.get("/output_configs", response_model=list[ComponentModel])
async def get_output_configs():
    """
    Get the output configs.
    """
    return builder_service.get_output_configs()
