"""
Router for team endpoints
"""

import logging
from typing import Dict, Optional
from uuid import UUID

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Path,
    Query,
    status,
    Request,
)
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.team import (
    Team<PERSON><PERSON>,
    TeamList,
    TeamResponse,
    TeamUpdate,
    TeamTest,
)
from aiplanet_platform.schemas.team_template import TeamFromTemplateRequest
from aiplanet_platform.services.team_service import TeamService
from aiplanet_platform.services.team_template_service import TeamTemplateService
from aiplanet_platform.utils.update_resource import update_component_fields
from aiplanet_platform.services.team_validations import TeamValidationService
from aiplanet_platform.models.user import User
from aiplanet_platform.core.enhanced_security import create_mixed_auth_dependency

router = APIRouter(
    prefix="/teams",
    tags=["teams"],
)
logger = logging.getLogger(__name__)


@router.get("/", response_model=TeamList)
async def get_teams(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[list[str]] = Query([], description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(create_mixed_auth_dependency("read:workflows")),
    db: Session = Depends(get_db),
):
    """
    Get a list of teams with filtering, sorting, and pagination.
    """
    logger.info(f"Current user {current_user.id} accessing get_teams")
    # Add organization id
    current_user = request.state.user

    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if current_user.organization_id:
        filters["organization_id"] = current_user.organization_id

    service = TeamService(db)
    teams = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    # Get proper total count using the count method (for pagination)
    total_count = service.count_resources(filters)

    # Calculate total workflows for the organization (all teams, including inactive)
    # This should NOT include search/filter constraints - only organization filter
    org_filters = (
        {"organization_id": current_user.organization_id}
        if current_user.organization_id
        else {}
    )
    total_workflow = service.count_resources(org_filters)

    # Calculate active workflows for the organization (teams that are not deleted and are active)
    # This should also NOT include search/filter constraints - only organization filter
    active_filters = org_filters.copy()
    active_filters["is_active"] = True
    active_workflow = service.count_resources(active_filters)

    return {
        "items": teams,
        "total": total_count,
        "active_workflow": active_workflow,
        "total_workflow": total_workflow,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=TeamResponse)
async def get_team_by_id(
    resource_id: UUID = Path(..., description="ID of the team to get"),
    current_user: User = Depends(create_mixed_auth_dependency("read:workflows")),
    db: Session = Depends(get_db),
):
    """
    Get a specific team by ID.
    """
    logger.info(f"Current user {current_user.id} accessing get_team_by_id")
    # Initialize service
    service = TeamService(db)
    team = service.fetch_resource_by_id(resource_id)
    if not team:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return team


@router.post("/", response_model=TeamResponse, status_code=status.HTTP_201_CREATED)
async def create_team(
    request: Request,
    team_data: TeamCreate,
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Create a new team.
    """
    logger.info(f"Current user {current_user.id} accessing create_team")
    # Add organization id
    team_data.organization_id = request.state.user.organization_id

    # Validate ChatInput count
    validation_service = TeamValidationService(db)
    validation_service.validate_chat_input_count(team_data.team_input_ids)

    # Initialize service
    service = TeamService(db)

    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters(
        {
            "component": team_data.component.model_dump_json(),
            "organization_id": team_data.organization_id,
        }
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(
        team_data.model_dump(
            exclude={
                "team_agent_ids": True,
                "team_input_ids": True,
                "team_output_ids": True,
                "team_termination_condition_ids": True,
            }
        ),
        team_data.team_agent_ids,
        team_data.team_input_ids,
        team_data.team_output_ids,
        team_data.team_termination_condition_ids,
    )


@router.put("/{resource_id}", response_model=TeamResponse)
async def update_team(
    request: Request,
    resource_id: UUID = Path(..., description="ID of the team to update"),
    team_data: TeamUpdate = None,
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Update a team.
    """
    logger.info(f"Current user {current_user.id} accessing update_team")
    # Initialize service
    service = TeamService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = team_data.component.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    # Validate ChatInput count
    validation_service = TeamValidationService(db)
    validation_service.validate_chat_input_count(team_data.team_input_ids)

    existing_data = existing.component

    updated_data = update_component_fields(existing_data, data)
    team_data.component = updated_data
    team_data.organization_id = request.state.user.organization_id

    team = service.update_resource_by_id(
        resource_id,
        team_data.model_dump(
            exclude={
                "team_agent_ids": True,
                "team_input_ids": True,
                "team_output_ids": True,
                "team_termination_condition_ids": True,
            }
        ),
        team_data.team_agent_ids,
        team_data.team_input_ids,
        team_data.team_output_ids,
        team_data.team_termination_condition_ids,
    )
    return team


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_team(
    resource_id: UUID = Path(..., description="ID of the team to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Delete a team. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    logger.info(f"Current user {current_user.id} accessing delete_team")
    # Initialize service
    service = TeamService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=TeamResponse)
async def restore_team(
    resource_id: UUID = Path(..., description="ID of the team to restore", gt=0),
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted team.
    """
    logger.info(f"Current user {current_user.id} accessing restore_team")
    # Initialize service
    service = TeamService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource


@router.post("/{resource_id}/deploy")
async def deploy_team(
    resource_id: UUID = Path(..., description="ID of the team to deploy"),
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Deploy team.
    """
    logger.info(f"Current user {current_user.id} accessing deploy_team")
    service = TeamService(db)
    return service.deploy_resource_by_id(resource_id)


@router.post("/{resource_id}/stop-deployment")
async def undeploy_team(
    resource_id: UUID = Path(..., description="ID of the team to undeploy"),
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Undeploy team.
    """
    logger.info(f"Current user {current_user.id} accessing undeploy_team")
    service = TeamService(db)
    return service.undeploy_resource_by_id(resource_id)


@router.post(
    "/from-template", response_model=TeamResponse, status_code=status.HTTP_201_CREATED
)
async def create_team_from_template(
    request: Request,
    template_request: TeamFromTemplateRequest,
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Create a team from a template.
    """
    logger.info(f"Current user {current_user.id} accessing create_team_from_template")
    current_user = request.state.user

    # Initialize template service
    template_service = TeamTemplateService(db)

    # Create team from template
    team = template_service.create_team_from_template(
        template_id=template_request.template_id,
        organization_id=current_user.organization_id,
        name_override=template_request.name,
        customizations=template_request.customizations,
    )

    return team


# TODO: Remove it before release
@router.post("/{resource_id}/test")
async def test_team(
    resource_id: UUID = Path(..., description="ID of the team to test"),
    test_data: TeamTest = None,
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Test team.
    """
    logger.info(f"Current user {current_user.id} accessing test_team")
    service = TeamService(db)
    return await service.test_resource_by_id(resource_id, test_data.model_dump())
