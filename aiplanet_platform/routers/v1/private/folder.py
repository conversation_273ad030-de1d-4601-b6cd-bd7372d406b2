"""
Router for knowledgebase endpoints
"""
import asyncio
import io
import logging
import json
from uuid import UUID
from typing import Dict, List, Optional
from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    UploadFile,
    status,
    Request,
    Query,
    Path,
)
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.enhanced_security import create_mixed_auth_dependency
from aiplanet_platform.models.user import User
from aiplanet_platform.services.folder_service import FolderService
from aiplanet_platform.services.file_service import FileService
from aiplanet_platform.schemas.folder import (
    FolderUpdate,
    FolderResponse,
    FolderList,
)
from aiplanet_platform.schemas.s3 import (
    S3FileListResponse,
    S3FileMetadata,
    S3FolderCreateResponse,
    S3PresignedUrlResponse,
    S3UploadResponse,
)
from aiplanet_platform.schemas.file import FileStatusUpdate
from aiplanet_platform.services.s3_service import S3Service
from aiplanet_platform.prefect.knowledgebase import trigger_prefect_flow_run


router = APIRouter(
    prefix="/knowledgebases",
    tags=["knowledgebases"],
)
logger = logging.getLogger(__name__)


@router.post("/create-folder-and-uploads", response_model=S3FolderCreateResponse)
async def create_folder_and_upload_files(
    request: Request,
    knowledgebase_name: str = Form(
        ..., description="Name of the knowledgebase to create"
    ),
    folder_description: str = Form(None, description="Knowledgebase description"),
    chunking_config: str = Form(
        None, description="Chunking configuration (JSON string)"
    ),
    embedding_config: str = Form(
        None, description="Embedding configuration (JSON string)"
    ),
    vector_db_config: str = Form(
        None, description="Vector database configuration (JSON string)"
    ),
    files: List[UploadFile] = File(default=[], description="Files to upload"),
    current_user: User = Depends(create_mixed_auth_dependency("write:folders")),
    s3_service: S3Service = Depends(),
):
    """
    Create a folder and upload files to it in one atomic operation.

    Args:
        folder_name: Name of the folder to create
        organization_id: Organization ID
        folder_description: Optional folder description
        file_description: Optional files description
        chunking_config: Optional chunking configuration as JSON string
        embedding_config: Optional embedding configuration as JSON string
        vector_db_config: Optional vector database configuration as JSON string
        files: List of files to upload (optional)
        s3_service: S3 service instance

    Returns:
        Combined response with folder and file upload results
    """
    try:
        logger.info(
            f"Current user {current_user.id} accessing create_folder_and_upload_files"
        )
        user = request.state.user
        # Parse JSON configurations
        chunking_config_dict = None
        embedding_config_dict = None
        vector_db_config_dict = None

        if chunking_config:
            try:
                chunking_config_dict = json.loads(chunking_config)
            except json.JSONDecodeError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid chunking_config JSON format",
                )

        if embedding_config:
            try:
                embedding_config_dict = json.loads(embedding_config)
            except json.JSONDecodeError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid embedding_config JSON format",
                )

        if vector_db_config:
            try:
                vector_db_config_dict = json.loads(vector_db_config)
            except json.JSONDecodeError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid vector_db_config JSON format",
                )

        # Create folder and upload files
        result = await s3_service.create_folder_and_upload_files(
            folder_name=knowledgebase_name,
            organization_id=user.organization_id,
            files=files,
            folder_description=folder_description,
            chunking_config=chunking_config_dict,
            embedding_config=embedding_config_dict,
            vector_db_config=vector_db_config_dict,
        )

        # Convert uploaded files to response format
        file_responses = []
        for db_file in result["uploaded_files"]:
            file_responses.append(
                S3UploadResponse(
                    file_id=db_file.id,
                    filename=db_file.name,
                    s3_path=db_file.s3_path,
                    status=db_file.status,
                    size=db_file.size,
                    content_type=db_file.type,
                )
            )
            # Start prefect flow in background without blocking the response
            asyncio.create_task(trigger_prefect_flow_run(db_file, request))
            logger.info(f"Started prefect task for file {db_file.id} in background")

        return S3FolderCreateResponse(
            folder_id=result["folder_id"],
            folder_name=result["folder"].name,
            uploaded_files=file_responses,
            total_files=result["total_files"],
            successful_uploads=result["successful_uploads"],
            failed_uploads=result["failed_uploads"],
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create folder and upload files: {str(e)}",
        )


@router.get("/file/download/{file_id}")
async def download_file(
    file_id: UUID,
    current_user: User = Depends(create_mixed_auth_dependency("read:folders")),
    s3_service: S3Service = Depends(),
):
    """
    Download a file from S3.

    Args:
        file_id: File ID to download
        s3_service: S3 service instance

    Returns:
        File content as streaming response
    """
    try:
        logger.info(f"Current user {current_user.id} accessing download_file")
        file_data = s3_service.download_file(file_id)

        return StreamingResponse(
            io.BytesIO(file_data["content"]),
            media_type=file_data["content_type"],
            headers={
                "Content-Disposition": f"attachment; filename={file_data['filename']}"
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download file: {str(e)}",
        )


@router.get("/generate-url/{file_id}", response_model=S3PresignedUrlResponse)
async def generate_presigned_url(
    file_id: UUID = Path(..., description="ID of the file to generate URL for"),
    current_user: User = Depends(create_mixed_auth_dependency("read:folders")),
    s3_service: S3Service = Depends(),
):
    """
    Generate a presigned URL for file access.

    Args:
        request: Presigned URL request
        s3_service: S3 service instance

    Returns:
        Presigned URL response
    """
    try:
        logger.info(f"Current user {current_user.id} accessing generate_presigned_url")

        url = s3_service.generate_presigned_url(file_id, 3600, "get_object")

        return S3PresignedUrlResponse(
            presigned_url=url, expires_in=3600, file_id=file_id
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate presigned URL: {str(e)}",
        )


@router.get("/{folder_id}/files", response_model=S3FileListResponse)
async def list_files_in_folder(
    folder_id: UUID,
    current_user: User = Depends(create_mixed_auth_dependency("read:folders")),
    s3_service: S3Service = Depends(),
):
    """
    List all files in a folder.

    Args:
        folder_id: Folder ID
        s3_service: S3 service instance

    Returns:
        List of files in folder
    """
    try:
        logger.info(f"Current user {current_user.id} accessing list_files_in_folder")
        files = s3_service.list_files_in_folder(folder_id)

        file_metadata_list = []
        for file_data in files:
            file_metadata_list.append(
                S3FileMetadata(
                    file_id=file_data["file_id"],
                    filename=file_data["filename"],
                    content_type=file_data["content_type"],
                    size=file_data["size"],
                    last_modified=None,  # Not available from database
                    etag=None,  # Not available from database
                    s3_path=file_data["s3_path"],
                    status=file_data["status"],
                )
            )

        return S3FileListResponse(
            files=file_metadata_list, total_files=len(files), folder_id=folder_id
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list files: {str(e)}",
        )


@router.get("/", response_model=FolderList)
async def get_folders(
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(create_mixed_auth_dependency("read:folders")),
    db: Session = Depends(get_db),
):
    """
    Get a list of folders with filtering, sorting, and pagination.
    """
    logger.info(f"Current user {current_user.id} accessing get_folders")
    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active

    service = FolderService(db)
    folders = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = service.count_resources(filters)

    return {
        "items": folders,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=FolderResponse)
async def get_folder_by_id(
    resource_id: UUID = Path(..., description="ID of the folder to get"),
    current_user: User = Depends(create_mixed_auth_dependency("read:folders")),
    db: Session = Depends(get_db),
):
    """
    Get a specific folder by ID.
    """
    service = FolderService(db)
    folder = service.fetch_resource_by_id(resource_id)
    if not folder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return folder


@router.put("/{resource_id}", response_model=FolderResponse)
async def update_folder(
    resource_id: UUID = Path(..., description="ID of the folder to update"),
    folder_data: FolderUpdate = None,
    current_user: User = Depends(create_mixed_auth_dependency("write:folders")),
    db: Session = Depends(get_db),
):
    """
    Update a folder.
    """
    logger.info(f"Current user {current_user.id} accessing update_folder")

    # Initialize service
    service = FolderService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = folder_data.model_dump(exclude_unset=True)

    # Check if name is being updated and if it conflicts with existing resources
    if "name" in data and data["name"] != existing.name:
        name_conflicts = service.fetch_resource_by_filters({"name": data["name"]})
        if name_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Resource with this name already exists",
            )

    folder = service.update_resource_by_id(resource_id, data)
    return folder


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_folder(
    resource_id: UUID = Path(..., description="ID of the folder to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    current_user: User = Depends(create_mixed_auth_dependency("write:folders")),
    db: Session = Depends(get_db),
):
    """
    Delete a folder. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    logger.info(f"Current user {current_user.id} accessing delete_folder")

    # Initialize service
    service = FolderService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=FolderResponse)
async def restore_folder(
    resource_id: UUID = Path(..., description="ID of the folder to restore", gt=0),
    current_user: User = Depends(create_mixed_auth_dependency("write:folders")),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted folder.
    """
    logger.info(f"Current user {current_user.id} accessing restore_folder")

    # Initialize service
    service = FolderService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource


@router.post("files/update-status")
async def update_file_status(
    data: FileStatusUpdate,
    current_user: User = Depends(create_mixed_auth_dependency("write:folders")),
    db: Session = Depends(get_db),
):
    """
    Update the status of a file.
    """
    logger.info(f"Current user {current_user.id} accessing update_file_status")

    # Initialize service
    file_service = FileService(db)

    file_id = data.file_id
    # Verify resource exists
    existing = file_service.fetch_resource_by_id(file_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Update file status
    file = file_service.update_resource_by_id(file_id, {"status": status})
    if not file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="File not found"
        )

    return file
