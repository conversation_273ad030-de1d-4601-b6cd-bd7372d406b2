"""
Router for organization endpoints
"""
import logging
from typing import Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.organization import (
    OrganizationCreate,
    OrganizationList,
    OrganizationResponse,
    OrganizationUpdate,
)
from aiplanet_platform.models.user import User
from aiplanet_platform.services.organization_service import OrganizationService
from aiplanet_platform.core.enhanced_security import create_mixed_auth_dependency

router = APIRouter(
    prefix="/organizations",
    tags=["organizations"],
)
logger = logging.getLogger(__name__)


@router.get("/", response_model=OrganizationList)
async def get_organizations(
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(create_mixed_auth_dependency("read:organizations")),
    db: Session = Depends(get_db),
):
    """
    Get a list of organizations with filtering, sorting, and pagination.
    """

    logger.info(f"Current user {current_user.id} accessing get_organizations")

    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active

    service = OrganizationService(db)
    organizations = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = service.count_resources(filters)

    return {
        "items": organizations,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=OrganizationResponse)
async def get_organization_by_id(
    resource_id: UUID = Path(..., description="ID of the organization to get"),
    current_user: User = Depends(create_mixed_auth_dependency("read:organizations")),
    db: Session = Depends(get_db),
):
    """
    Get a specific organization by ID.
    """
    logger.info(f"Current user {current_user.id} accessing get_organization_by_id")

    service = OrganizationService(db)
    organization = service.fetch_resource_by_id(resource_id)
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return organization


@router.post(
    "/", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED
)
async def create_organization(
    organization_data: OrganizationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(create_mixed_auth_dependency("write:organizations")),
):
    """
    Create a new organization.
    """
    logger.info(f"Current user {current_user.id} accessing update_my_profile")

    service = OrganizationService(db)
    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters({"name": organization_data.name})
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(organization_data.model_dump())


@router.put("/{resource_id}", response_model=OrganizationResponse)
async def update_organization(
    resource_id: UUID = Path(..., description="ID of the organization to update"),
    organization_data: OrganizationUpdate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(create_mixed_auth_dependency("write:organizations")),
):
    """
    Update a organization.
    """
    logger.info(f"Current user {current_user.id} accessing update_my_profile")

    service = OrganizationService(db)
    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = organization_data.model_dump(exclude_unset=True)

    # Check if name is being updated and if it conflicts with existing resources
    if "name" in data and data["name"] != existing.name:
        name_conflicts = service.fetch_resource_by_filters({"name": data["name"]})
        if name_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Resource with this name already exists",
            )

    organization = service.update_resource_by_id(resource_id, data)
    return organization


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_organization(
    resource_id: UUID = Path(..., description="ID of the organization to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    db: Session = Depends(get_db),
    current_user: User = Depends(create_mixed_auth_dependency("write:organizations")),
):
    """
    Delete a organization. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    logger.info(f"Current user {current_user.id} accessing delete_organization")

    service = OrganizationService(db)
    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=OrganizationResponse)
async def restore_organization(
    resource_id: UUID = Path(
        ..., description="ID of the organization to restore", gt=0
    ),
    db: Session = Depends(get_db),
    current_user: User = Depends(create_mixed_auth_dependency("write:organizations")),
):
    """
    Restore a soft-deleted organization.
    """
    logger.info(f"Current user {current_user.id} accessing update_my_profile")

    service = OrganizationService(db)
    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource
