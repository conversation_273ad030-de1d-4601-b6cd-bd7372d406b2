"""
Router for validation endpoints
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.services.validation_service import (
    ValidationService,
    ValidationRequest,
    ValidationResponse,
    ValidationError,
)
from aiplanet_platform.services.component_test_service import (
    ComponentTestService,
    ComponentTestRequest,
    ComponentTestResult,
)

router = APIRouter(
    prefix="/validations",
    tags=["validations"],
)


@router.post("/")
async def validate_component(
    request: ValidationRequest, db: Session = Depends(get_db)
) -> ValidationResponse:
    """Validate a component configuration"""
    try:
        return ValidationService.validate(request.component, db)
    except Exception as e:
        return ValidationResponse(
            is_valid=False,
            errors=[ValidationError(field="validation", error=str(e))],
            warnings=[],
        )


@router.post("/test")
async def test_component(
    request: ComponentTestRequest, db: Session = Depends(get_db)
) -> ComponentTestResult:
    """Test a component functionality with appropriate inputs based on type"""
    # First validate the component configuration
    validation_result = ValidationService.validate(request.component, db)

    # Only proceed with testing if the component is valid
    if not validation_result.is_valid:
        return ComponentTestResult(
            status=False,
            message="Component validation failed",
            logs=[e.error for e in validation_result.errors],
        )

    # If validation passed, run the functional test
    return await ComponentTestService.test_component(
        component=request.component, timeout=request.timeout if request.timeout else 60
    )
