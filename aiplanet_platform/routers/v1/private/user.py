"""
Router for user endpoints
"""
import logging
from uuid import UUID
from typing import Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.services.user_service import UserService
from aiplanet_platform.schemas.user import (
    UserCreate,
    UserUpdate,
    UserProfileUpdate,
    PasswordUpdate,
    UserResponse,
    UserList,
)
from aiplanet_platform.core.enhanced_security import create_mixed_auth_dependency

# from aiplanet_platform.core.enhanced_security import get_current_user
from aiplanet_platform.models.user import User

router = APIRouter(
    prefix="/users",
    tags=["users"],
)
logger = logging.getLogger(__name__)


@router.get("/me", response_model=UserResponse)
def get_me(current_user: User = Depends(create_mixed_auth_dependency("read:users"))):
    """
    Get current user's information.
    """
    logger.info(f"Current user {current_user.id} accessing get_me")
    del current_user.password
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_my_profile(
    profile_data: UserProfileUpdate,
    current_user: User = Depends(create_mixed_auth_dependency("write:users")),
    db: Session = Depends(get_db),
):
    """
    Update current user's profile (name and bio only).
    """
    logger.info(f"Current user {current_user.id} accessing update_my_profile")

    service = UserService(db)
    # Extract only the provided fields for partial update
    update_data = profile_data.model_dump(exclude_unset=True)

    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No data provided for update",
        )

    # Check if name is being updated and if it conflicts with existing users
    if "name" in update_data and update_data["name"] != current_user.name:
        name_conflicts = service.fetch_resource_by_filters(
            {"name": update_data["name"]}
        )
        if name_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="A user with this name already exists",
            )

    # Update the current user's profile
    updated_user = service.update_resource_by_id(current_user.id, update_data)

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile",
        )

    # Remove password before returning
    del updated_user.password
    return updated_user


@router.put("/me/password", response_model=Dict[str, str])
async def change_my_password(
    password_data: PasswordUpdate,
    current_user: User = Depends(create_mixed_auth_dependency("write:users")),
    db: Session = Depends(get_db),
):
    """
    Change current user's password.
    """
    logger.info(f"Current user {current_user.id} accessing change_my_password")

    service = UserService(db)
    # Verify current password (using existing approach - direct comparison)
    if password_data.current_password != current_user.password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect",
        )

    # Update password (using existing approach - direct storage)
    update_data = {"password": password_data.new_password}
    updated_user = service.update_resource_by_id(current_user.id, update_data)

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update password",
        )

    return {"message": "Password updated successfully"}


@router.get("/", response_model=UserList)
async def get_users(
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(create_mixed_auth_dependency("read:users")),
    db: Session = Depends(get_db),
):
    """
    Get a list of users with filtering, sorting, and pagination.
    """
    logger.info(f"Current user {current_user.id} accessing get_users")
    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active

    service = UserService(db)
    users = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = service.count_resources(
        filters
    )  # This should be replaced with a proper count query in a real app

    return {
        "items": users,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=UserResponse)
async def get_user_by_id(
    resource_id: UUID = Path(..., description="ID of the user to get"),
    current_user: User = Depends(create_mixed_auth_dependency("read:users")),
    db: Session = Depends(get_db),
):
    """
    Get a specific user by ID.
    """
    logger.info(
        f"Current user {current_user.id} accessing get_user_by_id with resource_id {resource_id}"
    )

    service = UserService(db)
    user = service.fetch_resource_by_id(resource_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return user


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(create_mixed_auth_dependency("write:users")),
    db: Session = Depends(get_db),
):
    """
    Create a new user.
    """
    service = UserService(db)
    logger.info(f"Current user {current_user.id} accessing create_user")
    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters({"name": user_data.name})
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(user_data.model_dump())


@router.put("/{resource_id}", response_model=UserResponse)
async def update_user(
    resource_id: UUID = Path(..., description="ID of the user to update"),
    user_data: UserUpdate = None,
    current_user: User = Depends(create_mixed_auth_dependency("write:users")),
    db: Session = Depends(get_db),
):
    """
    Update a user.
    """
    logger.info(
        f"Current user {current_user.id} accessing update_user with resource_id {resource_id}"
    )

    service = UserService(db)
    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = user_data.model_dump(exclude_unset=True)

    # Check if name is being updated and if it conflicts with existing resources
    if "name" in data and data["name"] != existing.name:
        name_conflicts = service.fetch_resource_by_filters({"name": data["name"]})
        if name_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Resource with this name already exists",
            )

    user = service.update_resource_by_id(resource_id, data)
    return user


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_user(
    resource_id: UUID = Path(..., description="ID of the user to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    current_user: User = Depends(create_mixed_auth_dependency("write:users")),
    db: Session = Depends(get_db),
):
    """
    Delete a user. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    logger.info(
        f"Current user {current_user.id} accessing delete_user with resource_id {resource_id}"
    )

    service = UserService(db)
    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=UserResponse)
async def restore_user(
    resource_id: UUID = Path(..., description="ID of the user to restore", gt=0),
    current_user: User = Depends(create_mixed_auth_dependency("write:users")),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted user.
    """
    logger.info(
        f"Current user {current_user.id} accessing restore_user with resource_id {resource_id}"
    )

    service = UserService(db)
    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource
