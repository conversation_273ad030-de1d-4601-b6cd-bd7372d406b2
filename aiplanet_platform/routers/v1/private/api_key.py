"""
Router for api key endpoints
"""
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.security import get_current_user
from aiplanet_platform.models.user import User
from aiplanet_platform.services.api_key_service import APIKeyService
from aiplanet_platform.schemas.api_key import (
    APIKeyCreate,
    APIKeyUpdate,
    APIKeyResponse,
    APIKeyCreateResponse,
    APIKeyList,
    APIKeyScopesResponse,
    APIKeyScopeInfo,
)
from aiplanet_platform.constants.api_key import API_KEY_RATE_LIMITS

router = APIRouter(
    prefix="/api-keys",
    tags=["api keys"],
)


@router.post(
    "/", response_model=APIKeyCreateResponse, status_code=status.HTTP_201_CREATED
)
async def create_api_key(
    api_key_data: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Create a new API key for third-party access.

    **Important**: The API key will only be shown once in the response.
    Make sure to copy and store it securely.

    **Available Scopes:**
    - `read:users` - Read user information
    - `write:users` - Create and update users
    - `read:organizations` - Read organization information
    - `write:organizations` - Update organization information
    - `read:agents` - Read AI agents and configurations
    - `write:agents` - Create and update AI agents
    - `read:sessions` - Read session information
    - `write:sessions` - Create and manage sessions
    - `read:models` - Read model configurations
    - `write:models` - Create and update model configurations
    - `read:tools` - Read tool configurations
    - `write:tools` - Create and update tool configurations
    """
    service = APIKeyService(db)

    # Validate scopes
    available_scopes = [scope["scope"] for scope in service.get_available_scopes()]
    invalid_scopes = [
        scope for scope in api_key_data.scopes if scope not in available_scopes
    ]

    if "*" in api_key_data.scopes:
        api_key_data.scopes = [s for s in available_scopes if s != "*"]

    if invalid_scopes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid scopes: {', '.join(invalid_scopes)}. Use /api-keys/scopes to see available scopes.",
        )

    try:
        # Set default rate limits
        api_key_data.rate_limit_per_day = API_KEY_RATE_LIMITS.get(
            "rate_limit_per_day", 10000
        )
        api_key_data.rate_limit_per_hour = API_KEY_RATE_LIMITS.get(
            "rate_limit_per_hour", 1000
        )
        api_key_data.rate_limit_per_minute = API_KEY_RATE_LIMITS.get(
            "rate_limit_per_minute", 100
        )
        api_key_data.expires_at = None

        # Convert Pydantic model to dict for the service
        api_key_dict = api_key_data.model_dump(exclude={"expires_at": True})

        api_key, key_string = service.create_api_key(
            user_id=current_user.id,
            organization_id=current_user.organization_id,
            api_key_data=api_key_dict,
        )

        return APIKeyCreateResponse(api_key=api_key, key=key_string)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create API key: {str(e)}",
        )


@router.get("/", response_model=APIKeyList)
async def list_api_keys(
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get a list of your API keys with filtering and pagination.

    **Note**: Only shows API keys that belong to your account.
    The actual API key values are never shown for security reasons.
    """
    service = APIKeyService(db)
    api_keys = service.get_user_api_keys(current_user.id, current_user.organization_id)

    # Apply filters
    if is_active is not None:
        api_keys = [key for key in api_keys if key.is_active == is_active]

    # Apply pagination
    total_count = len(api_keys)
    api_keys = api_keys[skip : skip + limit]

    return APIKeyList(
        items=api_keys,
        total=total_count,
        page=skip // limit + 1 if limit > 0 else 1,
        pages=(total_count + limit - 1) // limit if limit > 0 else 1,
        size=limit,
    )


@router.get("/{key_id}", response_model=APIKeyResponse)
async def get_api_key(
    key_id: UUID = Path(..., description="ID of the API key to get"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get details of a specific API key.

    **Note**: The actual API key value is never shown for security reasons.
    Only the key prefix (first few characters) is displayed for identification.
    """
    service = APIKeyService(db)
    api_key = service.fetch_resource_by_filters(
        {
            "id": key_id,
            "user_id": current_user.id,
            "organization_id": current_user.organization_id,
        }
    )

    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="API key not found"
        )

    return api_key[0]


@router.put("/{key_id}", response_model=APIKeyResponse)
async def update_api_key(
    update_data: APIKeyUpdate,
    key_id: UUID = Path(..., description="ID of the API key to update"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update an API key. You can modify name, description, scopes, rate limits, and status.

    **Note**: You cannot change the actual API key value. If you need a new key value,
    create a new API key and delete the old one.
    """
    service = APIKeyService(db)

    # Validate scopes if provided
    if update_data.scopes is not None:
        available_scopes = [scope["scope"] for scope in service.get_available_scopes()]
        invalid_scopes = [
            scope for scope in update_data.scopes if scope not in available_scopes
        ]

        if invalid_scopes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid scopes: {', '.join(invalid_scopes)}. Use /api-keys/scopes to see available scopes.",
            )

    # Convert Pydantic model to dict, excluding unset fields
    update_dict = update_data.model_dump(exclude_unset=True)

    api_key = service.update_api_key(key_id, current_user.id, update_dict)

    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="API key not found"
        )

    return api_key


@router.put("/{key_id}", status_code=status.HTTP_204_NO_CONTENT)
async def revoke_api_key(
    key_id: UUID = Path(..., description="ID of the API key to revoke"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Revoke (deactivate) an API key. This action cannot be undone.

    **Warning**: Once revoked, the API key will immediately stop working.
    Any applications using this key will receive authentication errors.
    """
    service = APIKeyService(db)

    if not service.revoke_api_key(key_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="API key not found"
        )


@router.delete("/{key_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_api_key(
    key_id: UUID = Path(..., description="ID of the API key to revoke"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Delete an API key. This action cannot be undone.

    **Warning**: Once deleted, the API key will immediately stop working.
    Any applications using this key will receive authentication errors.
    """
    service = APIKeyService(db)

    if not service.remove_resource_by_id(key_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="API key not found"
        )


@router.get("/{key_id}/usage", response_model=dict)
async def get_api_key_usage(
    key_id: UUID = Path(..., description="ID of the API key"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get usage statistics for an API key.

    Returns information about:
    - Total requests made with this key
    - Last usage timestamp
    - Current rate limits
    - Whether the key is active and not expired
    """
    service = APIKeyService(db)
    usage_stats = service.get_api_key_usage_stats(key_id, current_user.id)

    if not usage_stats:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="API key not found"
        )

    return usage_stats


@router.get("/scopes/available", response_model=APIKeyScopesResponse)
async def get_available_scopes(
    current_user: User = Depends(get_current_user),
):
    """
    Get list of available API scopes that can be assigned to API keys.

    Use this endpoint to see what permissions you can grant to your API keys.
    Each scope controls access to specific parts of the API.
    """
    service = APIKeyService(None)  # No DB needed for this static method
    scopes_data = service.get_available_scopes()

    scopes = [APIKeyScopeInfo(**scope) for scope in scopes_data]

    return APIKeyScopesResponse(scopes=scopes)


@router.post("/{key_id}/regenerate", response_model=APIKeyCreateResponse)
async def regenerate_api_key(
    key_id: UUID = Path(..., description="ID of the API key to regenerate"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Regenerate an API key with a new key value while keeping the same settings.

    **Important**:
    - The old key will immediately stop working
    - The new key will be shown only once in the response
    - All other settings (scopes, rate limits, etc.) remain the same

    This is useful when you think your API key might be compromised.
    """
    service = APIKeyService(db)

    # Get the existing API key
    existing_key = service.fetch_resource_by_filters(
        {
            "id": key_id,
            "user_id": current_user.id,
            "organization_id": current_user.organization_id,
        }
    )

    if not existing_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="API key not found"
        )

    existing_key = existing_key[0]

    try:
        # Generate new key value
        from aiplanet_platform.models.api_key import APIKey

        full_key, prefix, key_hash = APIKey.generate_key()

        # Update the existing key with new values
        update_data = {
            "key_prefix": prefix,
            "key_hash": key_hash,
        }

        updated_key = service.update_resource_by_id(key_id, update_data)

        if not updated_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to regenerate API key",
            )

        return APIKeyCreateResponse(api_key=updated_key, key=full_key)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate API key: {str(e)}",
        )
