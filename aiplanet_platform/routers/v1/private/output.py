"""
Router for output endpoints
"""
from typing import Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status, Request
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.output import (
    OutputCreate,
    OutputList,
    OutputResponse,
    OutputUpdate,
)
from aiplanet_platform.services.output_service import OutputService
from aiplanet_platform.utils.update_resource import update_component_fields

router = APIRouter(
    prefix="/outputs",
    tags=["outputs"],
)


@router.get("/", response_model=OutputList)
async def get_outputs(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    organization_id: Optional[UUID] = Query(
        None, description="Filter by organization ID"
    ),
    db: Session = Depends(get_db),
):
    """
    Get a list of outputs with filtering, sorting, and pagination.
    """
    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if organization_id:
        filters["organization_id"] = organization_id

    service = OutputService(db)
    outputs = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = len(
        outputs
    )  # This should be replaced with a proper count query in a real app

    return {
        "items": outputs,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=OutputResponse)
async def get_output_by_id(
    resource_id: UUID = Path(..., description="ID of the output to get"),
    db: Session = Depends(get_db),
):
    """
    Get a specific output by ID.
    """
    service = OutputService(db)
    output = service.fetch_resource_by_id(resource_id)
    if not output:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return output


@router.post("/", response_model=OutputResponse, status_code=status.HTTP_201_CREATED)
async def create_output(
    request: Request,
    output_data: OutputCreate,
    db: Session = Depends(get_db),
):
    """
    Create a new output.
    """
    # Add organization id
    output_data.organization_id = request.state.user.organization_id

    # Initialize service
    service = OutputService(db)

    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters(
        {
            "component": output_data.component.model_dump_json(),
            "organization_id": output_data.organization_id,
        }
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(output_data.model_dump())


@router.put("/{resource_id}", response_model=OutputResponse)
async def update_output(
    request: Request,
    resource_id: UUID = Path(..., description="ID of the output to update"),
    output_data: OutputUpdate = None,
    db: Session = Depends(get_db),
):
    """
    Update a output.
    """
    service = OutputService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = output_data.component.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    existing_data = existing.component

    updated_data = update_component_fields(existing_data, data)
    output_data.component = updated_data
    output_data.organization_id = request.state.user.organization_id

    output = service.update_resource_by_id(resource_id, output_data.model_dump())
    return output


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_output(
    resource_id: UUID = Path(..., description="ID of the output to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    db: Session = Depends(get_db),
):
    """
    Delete a output. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    service = OutputService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=OutputResponse)
async def restore_output(
    resource_id: UUID = Path(..., description="ID of the output to restore", gt=0),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted output.
    """
    service = OutputService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource
