"""
S3 API routes for file operations
"""
from typing import List
from uuid import UUID

from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    UploadFile,
    status,
)

from aiplanet_platform.schemas.s3 import (
    S3BulkUploadResponse,
    S3DeleteResponse,
    S3FileMetadata,
    S3PresignedUploadRequest,
    S3PresignedUploadResponse,
    S3UploadResponse,
)
from aiplanet_platform.services.s3_service import S3Service

router = APIRouter(prefix="/s3", tags=["S3"])


@router.post("/upload", response_model=S3UploadResponse)
async def upload_file(
    folder_id: UUID = Form(..., description="Folder ID to upload file to"),
    description: str = Form(None, description="File description"),
    file: UploadFile = File(..., description="File to upload"),
    s3_service: S3Service = Depends(),
):
    """
    Upload a single file to S3.

    Args:
        folder_id: Folder ID to upload file to
        description: Optional file description
        file: File to upload
        s3_service: S3 service instance

    Returns:
        Upload response with file metadata
    """
    try:
        db_file = await s3_service.upload_file(file, folder_id, description)

        return S3UploadResponse(
            file_id=db_file.id,
            filename=db_file.name,
            s3_path=db_file.s3_path,
            status=db_file.status,
            size=db_file.size,
            content_type=db_file.type,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}",
        )


@router.post("/upload/track", response_model=S3UploadResponse)
async def upload_file_with_tracking(
    folder_id: UUID = Form(..., description="Folder ID to upload file to"),
    description: str = Form(None, description="File description"),
    file: UploadFile = File(..., description="File to upload"),
    s3_service: S3Service = Depends(),
):
    """
    Upload a single file to S3 with database tracking (creates record first).
    This endpoint creates a database record first, then uploads to S3.
    Useful when you need immediate file_id for tracking purposes.

    Args:
        folder_id: Folder ID to upload file to
        description: Optional file description
        file: File to upload
        s3_service: S3 service instance

    Returns:
        Upload response with file metadata
    """
    try:
        db_file = await s3_service.create_file_record_and_upload(
            file, folder_id, description
        )

        return S3UploadResponse(
            file_id=db_file.id,
            filename=db_file.name,
            s3_path=db_file.s3_path,
            status=db_file.status,
            size=db_file.size,
            content_type=db_file.type,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}",
        )


@router.post("/upload/bulk", response_model=S3BulkUploadResponse)
async def upload_files_bulk(
    folder_id: UUID = Form(..., description="Folder ID to upload files to"),
    description: str = Form(None, description="Files description"),
    files: List[UploadFile] = File(..., description="Files to upload"),
    s3_service: S3Service = Depends(),
):
    """
    Upload multiple files to S3 sequentially.

    Args:
        folder_id: Folder ID to upload files to
        description: Optional files description
        files: List of files to upload
        s3_service: S3 service instance

    Returns:
        Bulk upload response with file metadata
    """
    try:
        uploaded_files = await s3_service.upload_files_bulk(
            files, folder_id, description
        )

        file_responses = []
        for db_file in uploaded_files:
            file_responses.append(
                S3UploadResponse(
                    file_id=db_file.id,
                    filename=db_file.name,
                    s3_path=db_file.s3_path,
                    status=db_file.status,
                    size=db_file.size,
                    content_type=db_file.type,
                )
            )

        return S3BulkUploadResponse(
            uploaded_files=file_responses,
            total_files=len(files),
            successful_uploads=len(uploaded_files),
            failed_uploads=len(files) - len(uploaded_files),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload files: {str(e)}",
        )


@router.post("/presigned-upload-url", response_model=S3PresignedUploadResponse)
async def generate_presigned_upload_url(
    request: S3PresignedUploadRequest,
    s3_service: S3Service = Depends(),
):
    """
    Generate a presigned URL for direct upload to S3.

    Args:
        request: Presigned upload URL request
        s3_service: S3 service instance

    Returns:
        Presigned upload URL response
    """
    try:
        result = s3_service.generate_presigned_upload_url(
            request.folder_id,
            request.filename,
            request.content_type,
            request.expiration,
        )

        return S3PresignedUploadResponse(
            upload_url=result["upload_url"],
            s3_key=result["s3_key"],
            bucket=result["bucket"],
            expires_in=result["expires_in"],
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate presigned upload URL: {str(e)}",
        )


@router.delete("/{file_id}", response_model=S3DeleteResponse)
async def delete_file(
    file_id: UUID,
    s3_service: S3Service = Depends(),
):
    """
    Delete a file from S3 and database.

    Args:
        file_id: File ID to delete
        s3_service: S3 service instance

    Returns:
        Delete response
    """
    try:
        success = s3_service.delete_file(file_id)

        return S3DeleteResponse(
            file_id=file_id,
            success=success,
            message="File deleted successfully" if success else "Failed to delete file",
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete file: {str(e)}",
        )


@router.get("/metadata/{file_id}", response_model=S3FileMetadata)
async def get_file_metadata(
    file_id: UUID,
    s3_service: S3Service = Depends(),
):
    """
    Get file metadata from S3.

    Args:
        file_id: File ID
        s3_service: S3 service instance

    Returns:
        File metadata
    """
    try:
        metadata = s3_service.get_file_metadata(file_id)

        return S3FileMetadata(
            file_id=metadata["file_id"],
            filename=metadata["filename"],
            content_type=metadata["content_type"],
            size=metadata["size"],
            last_modified=metadata.get("last_modified"),
            etag=metadata.get("etag"),
            s3_path=metadata["s3_path"],
            status=metadata["status"],
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get file metadata: {str(e)}",
        )
