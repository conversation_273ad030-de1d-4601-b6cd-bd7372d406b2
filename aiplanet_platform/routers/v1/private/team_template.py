"""
Router for team template endpoints
"""

import logging
from typing import List, Optional
from uuid import UUID

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Path,
    Query,
    Request,
    status,
)
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.team_template import (
    TeamTemplateCreate,
    TeamTemplateList,
    TeamTemplateResponse,
    TeamTemplateUpdate,
)
from aiplanet_platform.services.team_template_service import TeamTemplateService
from aiplanet_platform.models.user import User
from aiplanet_platform.core.enhanced_security import create_mixed_auth_dependency

router = APIRouter(
    prefix="/team-templates",
    tags=["team-templates"],
)
logger = logging.getLogger(__name__)


@router.get("/", response_model=TeamTemplateList)
async def get_team_templates(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[List[str]] = Query([], description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    category: Optional[str] = Query(None, description="Filter by category"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    tags: Optional[List[str]] = Query([], description="Filter by tags"),
    current_user: User = Depends(create_mixed_auth_dependency("read:workflows")),
    db: Session = Depends(get_db),
):
    """
    Get a list of team templates with filtering, sorting, and pagination.
    """
    logger.info(f"Current user {current_user.id} accessing get_team_templates")
    current_user = request.state.user

    # Build filters dictionary from query parameters
    filters = {}
    if category:
        filters["category"] = category
    if name_like:
        filters["name_like"] = name_like
    if tags:
        filters["tags"] = tags

    # Include both organization templates and system templates (organization_id = None)
    filters["organization_id"] = current_user.organization_id

    service = TeamTemplateService(db)
    templates = service.fetch_templates_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    # Get total count using the count method (for pagination)
    total_count = service.count_templates(filters)

    return {
        "items": templates,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{template_id}", response_model=TeamTemplateResponse)
async def get_team_template(
    template_id: UUID = Path(..., description="ID of the template to get"),
    current_user: User = Depends(create_mixed_auth_dependency("read:workflows")),
    db: Session = Depends(get_db),
):
    """
    Get a specific team template by ID.
    """
    logger.info(f"Current user {current_user.id} accessing get_team_template")

    service = TeamTemplateService(db)
    template = service.fetch_template_by_id(template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
        )

    return template


@router.post(
    "/", response_model=TeamTemplateResponse, status_code=status.HTTP_201_CREATED
)
async def create_team_template(
    request: Request,
    template_data: TeamTemplateCreate,
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Create a new team template.
    """
    logger.info(f"Current user {current_user.id} accessing create_team_template")
    current_user = request.state.user

    # Add organization id to template data
    template_dict = template_data.model_dump()
    template_dict["organization_id"] = current_user.organization_id

    service = TeamTemplateService(db)

    # Check if a template with the same name already exists in the organization
    existing_filters = {
        "name": template_data.name,
        "organization_id": current_user.organization_id,
    }
    existing = service.fetch_templates_by_filters(existing_filters, limit=1)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Template with this name already exists in your organization",
        )

    return service.create_template(template_dict)


@router.put("/{template_id}", response_model=TeamTemplateResponse)
async def update_team_template(
    request: Request,
    template_data: TeamTemplateUpdate,
    template_id: UUID = Path(..., description="ID of the template to update"),
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Update a team template.
    """
    logger.info(f"Current user {current_user.id} accessing update_team_template")
    current_user = request.state.user

    service = TeamTemplateService(db)

    # Verify template exists
    existing = service.fetch_template_by_id(template_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
        )

    # Check if user can update this template (must be from same organization or system template)
    if (
        existing.organization_id
        and existing.organization_id != current_user.organization_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to update this template",
        )

    # Exclude None values to implement partial updates
    data = template_data.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    # Check for name conflicts if name is being updated
    if "name" in data:
        name_filters = {
            "name": data["name"],
            "organization_id": current_user.organization_id,
        }
        existing_with_name = service.fetch_templates_by_filters(name_filters, limit=1)
        if existing_with_name and existing_with_name[0].id != template_id:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Template with this name already exists in your organization",
            )

    template = service.update_template_by_id(template_id, data)
    return template


@router.delete("/{template_id}")
async def delete_team_template(
    template_id: UUID = Path(..., description="ID of the template to delete"),
    current_user: User = Depends(create_mixed_auth_dependency("write:workflows")),
    db: Session = Depends(get_db),
):
    """
    Delete a team template. This is a soft delete.
    """
    logger.info(f"Current user {current_user.id} accessing delete_team_template")

    service = TeamTemplateService(db)

    # Verify template exists
    existing = service.fetch_template_by_id(template_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
        )

    # Check if user can delete this template (must be from same organization)
    # System templates (organization_id = None) cannot be deleted by users
    if (
        not existing.organization_id
        or existing.organization_id != current_user.organization_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to delete this template",
        )

    success = service.delete_template_by_id(template_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
        )

    return {"success": True}


@router.get("/{template_id}/api-requirements")
async def get_template_api_requirements(
    template_id: UUID = Path(..., description="ID of the template to analyze"),
    current_user: User = Depends(create_mixed_auth_dependency("read:workflows")),
    db: Session = Depends(get_db),
):
    """
    Analyze a template and return required API key information.

    This endpoint helps users understand what API keys they need to provide
    when creating a team from this template.
    """
    logger.info(
        f"Current user {current_user.id} analyzing template {template_id} API requirements"
    )

    service = TeamTemplateService(db)

    # Verify template exists and user has access
    template = service.fetch_template_by_id(template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
        )

    # System templates or organization templates are accessible
    if (
        template.organization_id
        and template.organization_id != current_user.organization_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to access this template",
        )

    return service.analyze_template_api_requirements(template_id)
