"""API router for v1"""
from fastapi import APIRouter

from aiplanet_platform.routers.v1.private import router as private_router
from aiplanet_platform.routers.v1.public import router as public_router
from aiplanet_platform.routers.v1.websocket import router as ws_router
from aiplanet_platform.routers.v1.third_party import router as third_party_router

router = APIRouter(
    tags=["v1"],
)

# Include routers
router.include_router(public_router)
router.include_router(private_router)
router.include_router(ws_router)
router.include_router(third_party_router)
