"""
Router for user endpoints
"""
from typing import Annotated
from uuid import UUID

from fastapi import APIRouter, Body, Depends, Form, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from jose import JW<PERSON>rror
from sqlalchemy.orm import Session

from aiplanet_platform.constants.organization import OrganizationStatus
from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.enhanced_security import verify_human
from aiplanet_platform.core.security import (
    create_access_token,
    create_refresh_token,
    decode_token,
)
from aiplanet_platform.schemas.user import TokenResponse, UserCreate, UserResponse
from aiplanet_platform.services.organization_service import OrganizationService
from aiplanet_platform.services.user_service import UserService

router = APIRouter(
    prefix="/users",
    tags=["users"],
)


@router.post(
    "/register",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(verify_human)],
)
async def create_user(
    user_data: Annotated[UserCreate, Form()],
    db: Session = Depends(get_db),
):
    """
    Create a new user.
    """
    service = UserService(db)

    existing = service.fetch_resource_by_filters({"email": user_data.email})
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this email already exists",
        )
    organization_service = OrganizationService(db)
    organization = organization_service.fetch_resource_by_filters(
        {"email": user_data.email}
    )

    if len(organization) == 0:
        organization = organization_service.create_resource(
            {
                "email": user_data.email,
                "name": user_data.name,
                "status": OrganizationStatus.ACTIVE,
            }
        )
    else:
        organization = organization[0]

    return service.create_resource(
        user_data.model_dump(), organization.id  # type:ignore
    )


@router.post(
    "/login",
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(verify_human)],
)
async def login_user(
    form_data: OAuth2PasswordRequestForm = Depends(),  # type: ignore
    db: Session = Depends(get_db),
):
    """
    Login a user using email and password. Use email as username.
    """
    service = UserService(db)

    # Get user by email
    users = service.fetch_resource_by_filters({"email": form_data.username})
    if not users:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials"
        )

    if form_data.password != users[0].password:
        raise HTTPException(status_code=401, detail="Incorrect password")

    user = users[0]

    # Create tokens
    access_token = create_access_token(data={"sub": str(user.id), "email": user.email})
    refresh_token = create_refresh_token(
        data={"sub": str(user.id), "email": user.email}
    )

    return TokenResponse(
        access_token=access_token, refresh_token=refresh_token, token_type="bearer"
    )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_access_token(
    refresh_token: str = Body(...), db: Session = Depends(get_db)
):
    """
    Refresh the access token using a valid refresh token.
    """
    try:
        payload = decode_token(refresh_token)
        user_id = str(payload.get("sub"))

        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid refresh token")

        service = UserService(db)

        # Get user by user_id
        user = service.fetch_resource_by_id(UUID(user_id))
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token"
            )

        new_access_token = create_access_token(
            data={"sub": user_id, "email": payload.get("email")}
        )
        return TokenResponse(
            access_token=new_access_token,
            refresh_token=refresh_token,
            token_type="bearer",
        )

    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid refresh token")
