"""
Router for ws endpoints
"""
import asyncio
import logging
import json
from datetime import datetime
from uuid import UUID
from fastapi import (
    APIRouter,
    Depends,
    Query,
    Path,
    status,
    WebSocketDisconnect,
    WebSocket,
)
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.services.websocket_manager_service import WebsocketManagerService
from aiplanet_platform.services.run_service import RunService
from aiplanet_platform.services.team_service import TeamService
from aiplanet_platform.constants.run_context import RunStatus
from aiplanet_platform.core.security import get_current_user_websocket

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ws",
    tags=["ws"],
)


@router.websocket("/run/{run_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    run_id: UUID = Path(..., description="ID of the ws to connect to"),
    token: str = Query(..., description="Token for authentication"),
    db: Session = Depends(get_db),
):
    """
    Websocket endpoint for ws.
    """
    service = WebsocketManagerService(db)
    run_service = RunService(db)

    # Authenticate BEFORE accepting the WebSocket connection
    user = await get_current_user_websocket(websocket, token, db)
    if not user:
        # Reject connection without accepting it first
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return

    try:
        run = run_service.fetch_resource_by_id(run_id)
        if not run:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        if run.status not in [RunStatus.CREATED, RunStatus.ACTIVE]:
            await websocket.close(code=status.WS_1014_BAD_GATEWAY)
            return

        # Verify the user has access to this run
        if run.user_id != user.id:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Now accept the connection after all validation passes
        connected = await service.connect(websocket, run_id)
        if not connected:
            return

        logger.info(
            f"WebSocket connection established for run {run_id} by user {user.id}"
        )
        message = None
        team_config = None

        while True:
            try:
                raw_message = await websocket.receive_text()
                message = json.loads(raw_message)
                if message.get("type") == "start":
                    logger.info(f"WebSocket received start message for run {run_id}")
                    files = message.get("files", [])
                    logger.error(files)
                    task = message.get("task", "")
                    # TODO: File Processing
                    team = None
                    if team_config is None:
                        run_session = run_service.fetch_resource_by_id(run_id)
                        if run_session:
                            team = run_session.session.team
                        team_config_filepath = (
                            TeamService(db).generate_workflow(team) if team else None
                        )
                        team_config = json.load(open(team_config_filepath, "r"))

                    if task and team_config:
                        asyncio.create_task(
                            service.start_stream(run_id, task, team_config)
                        )
                    else:
                        logger.warning(f"Invalid start message format for run {run_id}")
                        await websocket.send_json(
                            {
                                "type": "error",
                                "message": "Invalid start message format",
                                "timestamp": datetime.utcnow().isoformat(),
                            }
                        )
                elif message.get("type") == "stop":
                    logger.info(f"Received stop request for run {run_id}")
                    reason = message.get("reason") or "User requested stop/cancellation"
                    await service.stop_run(run_id, reason=reason)
                    break
                elif message.get("type") == "ping":
                    await websocket.send_json(
                        {"type": "pong", "timestamp": datetime.utcnow().isoformat()}
                    )
                elif message.get("type") == "input_response":
                    # Handle input response from client
                    response = message.get("response")
                    if response is not None:
                        await service.handle_input_response(run_id, response)
                    else:
                        logger.warning(
                            f"Invalid input response format for run {run_id}"
                        )

            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received: {message}")
                await websocket.send_json(
                    {
                        "type": "error",
                        "error": "Invalid message format",
                        "timestamp": datetime.utcnow().isoformat(),
                    }
                )

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for run {run_id}")
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
    finally:
        await service.disconnect(run_id)


@router.websocket("/test")
async def test_websocket(websocket: WebSocket):
    await websocket.accept()
    await websocket.send_json({"message": "Hello WebSocket!"})
    await websocket.close()
