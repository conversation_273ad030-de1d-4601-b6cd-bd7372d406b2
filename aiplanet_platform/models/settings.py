"""
Settings model
"""
import uuid
from typing import Union

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    UUID,
)
from sqlalchemy.orm import relationship
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func

from aiplanet_platform.core.database import Base
from aiplanet_platform.constants.team_manager import SettingsConfig


class Settings(Base):
    """Settings database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Settings".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    user = relationship("User", back_populates="settings")

    # Basic fields
    config: Union[SettingsConfig, dict] = Column(
        MutableDict.as_mutable(JSONB), default=SettingsConfig, nullable=True
    )

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("settingss.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Settings", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Settings", back_populates="children", remote_side=[id])

    def __repr__(self):
        return "<Settings(id={})>".format(self.id)
