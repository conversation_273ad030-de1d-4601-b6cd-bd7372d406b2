"""
Message model
"""
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    DateTime,
    ForeignKey,
    UUID,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import Union, Optional
import uuid

from aiplanet_platform.core.database import Base
from aiplanet_platform.constants.team_manager import MessageConfig


class Message(Base):
    """Message database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Messages".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    config: Union[MessageConfig, dict] = Column(
        JSONB, default=MessageConfig, nullable=True
    )

    # Session relationship
    session_id: Optional[UUID] = Column(
        UUID, ForeignKey("sessions.id", ondelete="CASCADE"), nullable=True
    )
    session = relationship(
        "aiplanet_platform.models.session.Session",
        back_populates="messages",
    )

    # Run relationship
    run_id: Optional[UUID] = Column(
        UUID, ForeignKey("runs.id", ondelete="CASCADE"), nullable=True
    )
    run = relationship(
        "aiplanet_platform.models.run.Run",
        back_populates="messages",
    )

    user_id: Optional[UUID] = Column(
        UUID, ForeignKey("users.id", ondelete="CASCADE"), nullable=True
    )

    message_meta = Column(JSONB, nullable=True, default=dict)
    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("messages.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Message", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Message", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<Message(id={id_val})>"
        except Exception:
            return "<Message(detached)>"
