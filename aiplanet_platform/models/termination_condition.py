"""
Termination Condition model
"""

import uuid
from typing import Union

from sqlalchemy import UUID, Boolean, Column, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB

from aiplanet_platform.core.database import Base, ComponentModel


class TerminationCondition(Base):
    """TerminationCondition database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "TerminationConditions".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Termination Condition Config
    component: Union[ComponentModel, dict] = Column(JSONB, nullable=True)

    # Default termination condition flag - True for system default termination conditions
    is_default = Column(Boolean, default=False, nullable=False, index=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Organization relationship
    organization_id = Column(
        UUID, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True
    )
    organization = relationship(
        "aiplanet_platform.models.organization.Organization",
        back_populates="terminationconditions",
    )

    teams = relationship(
        "aiplanet_platform.models.team.Team",
        secondary="team_termination_conditions",  # change table name accordingly
        back_populates="team_termination_conditions",  # change relationship name accordingly
    )

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("termination_conditions.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("TerminationCondition", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("TerminationCondition", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<TerminationCondition(id={id_val})>"
        except Exception:
            return "<TerminationCondition(detached)>"
