"""
Tool model
"""

import uuid
from typing import Union

from sqlalchemy import <PERSON>UI<PERSON>, Boolean, Column, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from aiplanet_platform.core.database import Base, ComponentModel


class Tool(Base):
    """Tool database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Tools".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Tool fields
    component: Union[ComponentModel, dict] = Column(JSONB, nullable=True)

    # Default tool flag - True for system default tools
    is_default = Column(Boolean, default=False, nullable=False, index=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Organization relationship
    organization_id = Column(
        UUID, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True
    )
    organization = relationship(
        "aiplanet_platform.models.organization.Organization",
        back_populates="tools",
    )

    agents = relationship(
        "aiplanet_platform.models.agent.Agent",
        secondary="agent_tools",
        back_populates="tools",
    )

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("tools.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Tool", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Tool", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<Tool(id={id_val})>"
        except Exception:
            return "<Tool(detached)>"
