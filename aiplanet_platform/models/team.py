"""
Team model
"""

import uuid
from typing import Union

from sqlalchemy import UUID, Boolean, Column, DateTime, ForeignKey, Table
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from aiplanet_platform.core.database import Base, ComponentModel


team_agents = Table(
    "team_agents",
    Base.metadata,
    Column(
        "team_id", UUID, ForeignKey("teams.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "agent_id", UUID, ForeignKey("agents.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "created_at", DateTime(timezone=True), server_default=func.now(), nullable=False
    ),
)

team_termination_conditions = Table(
    "team_termination_conditions",
    Base.metadata,
    Column(
        "team_id", UUID, ForeignKey("teams.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "termination_condition_id",
        UUID,
        <PERSON><PERSON><PERSON>("terminationconditions.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "created_at", DateTime(timezone=True), server_default=func.now(), nullable=False
    ),
)

team_inputs = Table(
    "team_inputs",
    Base.metadata,
    Column(
        "team_id", UUID, ForeignKey("teams.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "input_id", UUID, ForeignKey("inputs.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "created_at", DateTime(timezone=True), server_default=func.now(), nullable=False
    ),
)

team_outputs = Table(
    "team_outputs",
    Base.metadata,
    Column(
        "team_id", UUID, ForeignKey("teams.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "output_id",
        UUID,
        ForeignKey("outputs.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "created_at", DateTime(timezone=True), server_default=func.now(), nullable=False
    ),
)


class Team(Base):
    """Team database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Teams".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Team Config
    component: Union[ComponentModel, dict] = Column(JSONB, nullable=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Organization relationship
    organization_id = Column(
        UUID, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True
    )
    organization = relationship(
        "aiplanet_platform.models.organization.Organization",
        back_populates="teams",
    )

    # Model relationship
    model_id = Column(UUID, ForeignKey("models.id", ondelete="SET NULL"), nullable=True)
    model = relationship("aiplanet_platform.models.model.Model")

    # Many-to-many relationships
    agents = relationship(
        "aiplanet_platform.models.agent.Agent",
        secondary=team_agents,
        back_populates="teams",
    )

    team_termination_conditions = relationship(
        "aiplanet_platform.models.termination_condition.TerminationCondition",
        secondary=team_termination_conditions,
        back_populates="teams",
    )

    team_inputs = relationship(
        "aiplanet_platform.models.input.Input",
        secondary=team_inputs,
        back_populates="teams",
    )

    team_outputs = relationship(
        "aiplanet_platform.models.output.Output",
        secondary=team_outputs,
        back_populates="teams",
    )

    sessions = relationship(
        "aiplanet_platform.models.session.Session",
        back_populates="team",
        cascade="all, delete-orphan",
    )

    is_deployed = Column(Boolean, default=False, nullable=True)

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("teams.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Team", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Team", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<Team(id={id_val})>"
        except Exception:
            return "<Team(detached)>"
