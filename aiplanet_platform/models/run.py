"""
Run model
"""
import uuid
from typing import Union
from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    ForeignKey,
    UUID,
    String,
    Text,
    Enum,
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func

from aiplanet_platform.core.database import Base
from aiplanet_platform.constants.run_context import RunStatus
from aiplanet_platform.constants.team_manager import (
    MessageConfig,
    TeamResult,
)


class Run(Base):
    """Run database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Runs".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # User Relationship
    user_id = Column(UUID, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    user = relationship("User", back_populates="runs")

    # Session relationship
    session_id = Column(
        UUID, ForeignKey("sessions.id", ondelete="CASCADE"), nullable=False
    )
    session = relationship("Session", back_populates="run")

    # Status
    status = Column(Enum(RunStatus), nullable=False, default=RunStatus.CREATED)

    # Task
    task: Union[MessageConfig, dict] = Column(
        JSONB,
        default=dict,
        nullable=True,
    )

    # Store TeamResult which contains TaskResult
    team_result: Union[TeamResult, dict] = Column(
        JSONB,
        default=dict,
        nullable=True,
    )

    error_message = Column(Text, nullable=True)
    version = Column(String(50), nullable=False, default="0.0.1")

    model_config = Column(JSONB, nullable=True, default=dict)

    messages = relationship("Message", back_populates="run")

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("runs.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Run", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Run", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<Run(id={id_val})>"
        except Exception:
            return "<Run(detached)>"
