"""
File model
"""
from sqlalchemy import (
    <PERSON>olean,
    <PERSON>umn,
    DateTime,
    ForeignKey,
    UUID,
    String,
    JSON,
    Integer,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
import uuid

from aiplanet_platform.core.database import Base


class FileStatus(str, enum.Enum):
    QUEUE = "Queue"
    FAILED = "Failed"
    PROCESSING = "Processing"
    SUCCESS = "Success"
    UPLOADED = "Uploaded"
    UPLOADING = "Uploading"


class File(Base):
    """File database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Files".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    name = Column(String(255), index=True, nullable=False)
    type = Column(String, nullable=False)
    size = Column(Integer, nullable=True)
    s3_path = Column(String)
    status = Column(String(length=50), nullable=False, default=FileStatus.QUEUE)

    # For URLs as Knowledge Base
    url = Column(String, nullable=True)
    meta_data = Column(JSON, nullable=True)

    # Folder relationship
    folder_id = Column(UUID(as_uuid=True), ForeignKey("folders.id"), nullable=False)
    folder = relationship("Folder", back_populates="files")

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("files.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("File", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("File", back_populates="children", remote_side=[id])

    def __repr__(self):
        return "<File(id={}, name={})>".format(self.id, self.name)
