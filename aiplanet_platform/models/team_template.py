"""
Team Template model
"""

import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from aiplanet_platform.core.database import Base


class TeamTemplate(Base):
    """Team Template database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "team_templates"

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(
        String(100), nullable=True, index=True
    )  # e.g., "customer_service", "coding", "research"

    # Clean template configuration (no UUIDs, just component structure)
    # Now includes nested inputs/outputs: {"team": {...}, "inputs": [...], "outputs": [...]}
    template_config = Column(JSONB, nullable=False)

    # Metadata fields (moved out of JSON)
    use_case = Column(String(255), nullable=True)
    tags = Column(JSONB, nullable=True, default=list)  # ["tag1", "tag2"]

    # Organization relationship (null for system templates)
    organization_id = Column(
        UUID, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True
    )
    organization = relationship(
        "aiplanet_platform.models.organization.Organization",
        back_populates="team_templates",
    )

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<TeamTemplate(id={id_val}, name={self.name})>"
        except Exception:
            return "<TeamTemplate(detached)>"
