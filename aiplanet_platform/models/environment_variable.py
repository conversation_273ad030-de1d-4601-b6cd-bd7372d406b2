"""
EnvironmentVariable model
"""

import uuid
import enum
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    String,
    UUID,
    Enum,
)
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from aiplanet_platform.core.database import Base


class EnvironmentVariableType(enum.Enum):
    string = "string"
    number = "number"
    boolean = "boolean"
    secret = "secret"


class EnvironmentVariable(Base):
    """Environment variable database model"""

    __tablename__ = "environment_variables"

    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False, index=True)
    user = relationship("User", back_populates="environment_variables")

    name = Column(String(255), nullable=False)
    value = Column(String, nullable=True)  # Will be encrypted if type is 'string'
    value_prefix = Column(String(255), nullable=True)
    type = Column(
        Enum(EnvironmentVariableType),
        default=EnvironmentVariableType.string,
        nullable=False,
    )
    description = Column(String(512), nullable=True)
    required = Column(Boolean, default=False, nullable=False)

    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<EnvironmentVariable(id={self.id}, name={self.name}, user_id={self.user_id})>"
