"""
User model
"""
import re
import uuid
from datetime import datetime
from sqlalchemy import (
    UUID,
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    String,
    Text,
    Integer,
    Index,
    Table,
)
from sqlalchemy.orm import relationship, validates
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy_utils.types.encrypted.encrypted_type import (
    AesGcmEngine,
    StringEncryptedType,
)

from aiplanet_platform.constants.user import UserStatus, UserType
from aiplanet_platform.core.config import get_settings
from aiplanet_platform.core.database import Base

settings = get_settings()
email_regex = re.compile(r"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$")


user_organizations = Table(
    "user_organizations",
    Base.metadata,
    Column(
        "user_id", UUID, ForeignKey("users.id", ondelete="CASCADE"), primary_key=True
    ),
    <PERSON>umn(
        "organization_id",
        UUID,
        Foreign<PERSON>ey("organizations.id", ondelete="CASCADE"),
        primary_key=True,
    ),
)


class User(Base):
    """User database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Users".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    name = Column(String(255), nullable=False)
    bio = Column(Text, nullable=True)

    # Email
    email = Column(String(255), unique=True, index=True, nullable=False)

    # Password
    password = Column(
        StringEncryptedType(String(255), settings.SECRET_KEY, AesGcmEngine),
        nullable=True,
    )

    # User role
    role = Column(Enum(UserType), nullable=False, default=UserType.NORMAL)

    # User Status
    status = Column(Enum(UserStatus), nullable=False, default=UserStatus.PENDING)

    # Add scopes support
    scopes = Column(
        JSONB,
        nullable=False,
        default=lambda: ["*"],  # Default: all permissions for regular users
        comment="List of allowed scopes for this user",
    )

    # Add rate limiting fields (similar to API keys)
    rate_limit_per_minute = Column(
        Integer,
        default=100,  # Higher default for users vs API keys
        comment="API calls per minute limit for this user",
    )
    rate_limit_per_hour = Column(
        Integer,
        default=5000,  # Higher default for users vs API keys
        comment="API calls per hour limit for this user",
    )
    rate_limit_per_day = Column(
        Integer,
        default=50000,  # Higher default for users vs API keys
        comment="API calls per day limit for this user",
    )

    # Usage tracking (similar to API keys)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    total_requests = Column(Integer, default=0)

    # OAuth fields
    oauth_provider = Column(String(255), nullable=True)
    oauth_id = Column(String(255), nullable=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Organization relationship
    organizations = relationship(
        "Organization",
        secondary=user_organizations,
        back_populates="users",
        cascade="all, delete",
    )

    settings = relationship(
        "Settings",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan",
    )

    runs = relationship(
        "Run",
        back_populates="user",
        cascade="all, delete-orphan",
    )

    api_keys = relationship(
        "APIKey", back_populates="user", cascade="all, delete-orphan"
    )

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("users.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("User", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("User", back_populates="children", remote_side=[id])

    __table_args__ = (
        Index("idx_users_oauth_provider_id", "oauth_provider", "oauth_id"),
    )

    def __repr__(self):
        return "<User(id={}, name={})>".format(self.id, self.name)

    @validates("email")
    def validate_email(self, key, address):
        if not email_regex.match(address):
            raise ValueError(f"Invalid email address: {address!r}")
        return address

    def update_usage(self):
        """Update usage statistics for the user"""
        old_count = getattr(self, "total_requests", 0)
        self.last_used_at = datetime.utcnow()
        self.total_requests = old_count + 1
