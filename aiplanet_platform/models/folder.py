"""
Folder model
"""
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    DateTime,
    ForeignKey,
    UUID,
    String,
    Text,
    UniqueConstraint,
    JSON,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from aiplanet_platform.core.database import Base


class Folder(Base):
    """Folder database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Folders".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    name = Column(String(255), index=True, nullable=False)
    description = Column(Text, nullable=True)

    # File relationship
    files = relationship("File", back_populates="folder")

    # Organization relationship
    organization_id = Column(
        UUID, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True
    )
    organization = relationship(
        "aiplanet_platform.models.organization.Organization",
        back_populates="folders",
    )

    chunking_config = Column(JSON, default=dict, nullable=True)
    embedding_config = Column(JSON, default=dict, nullable=True)
    vector_db_config = Column(JSON, default=dict, nullable=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("folders.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Folder", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Folder", back_populates="children", remote_side=[id])

    __table_args__ = (
        UniqueConstraint(
            "name",
            "organization_id",
            "is_deleted",
            name="uq_folder_name_organization_is_deleted",
        ),
    )

    def __repr__(self):
        return "<Folder(id={}, name={})>".format(self.id, self.name)
