"""
Agent model
"""

import uuid
from typing import Union

from sqlalchemy import UUID, Boolean, Column, DateTime, ForeignKey, Table
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from aiplanet_platform.core.database import Base, ComponentModel

agent_tools = Table(
    "agent_tools",
    Base.metadata,
    Column(
        "agent_id", UUID, ForeignKey("agents.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "tool_id", UUID, ForeignKey("tools.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "created_at", DateTime(timezone=True), server_default=func.now(), nullable=False
    ),
)


class Agent(Base):
    """Agent database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Agents".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Agent Config
    component: Union[ComponentModel, dict] = Column(JSONB, nullable=True)

    # Default agent flag - True for system default agents
    is_default = Column(Boolean, default=False, nullable=False, index=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Organization
    organization_id = Column(
        UUID, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True
    )
    organization = relationship(
        "aiplanet_platform.models.organization.Organization",
        back_populates="agents",
    )

    # Many-to-many relationships
    tools = relationship(
        "aiplanet_platform.models.tool.Tool",
        secondary=agent_tools,
        back_populates="agents",
    )

    teams = relationship(
        "aiplanet_platform.models.team.Team",
        secondary="team_agents",
        back_populates="agents",
    )

    model_id = Column(UUID, ForeignKey("models.id", ondelete="CASCADE"), nullable=True)
    model = relationship(
        "aiplanet_platform.models.model.Model", back_populates="agents"
    )

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("agents.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Agent", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Agent", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<Agent(id={id_val})>"
        except Exception:
            return "<Agent(detached)>"
