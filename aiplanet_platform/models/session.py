"""
Session model
"""
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    DateTime,
    ForeignKey,
    UUID,
    Text,
)
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from aiplanet_platform.core.database import Base


class Session(Base):
    """Session database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Sessions".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    name = Column(Text, nullable=True)
    description = Column(Text, nullable=True)

    team_id = Column(UUID, ForeignKey("teams.id", ondelete="CASCADE"), nullable=True)
    team = relationship("Team", back_populates="sessions")

    messages = relationship("Message", back_populates="session")

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(<PERSON><PERSON>an, default=False, nullable=False, index=True)

    run = relationship("Run", back_populates="session")

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("sessions.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Session", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Session", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<Session(id={id_val})>"
        except Exception:
            return "<Session(detached)>"
