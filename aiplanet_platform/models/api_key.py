"""
Api Key model
"""
import uuid
import secrets
from datetime import datetime
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    UUID,
    String,
    Text,
    Integer,
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func

from aiplanet_platform.core.database import Base


class APIKey(Base):
    """ApiKey database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "ApiKeys".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    name = Column(String(255), index=True, nullable=False)
    description = Column(Text, nullable=True)

    # Key Authentication
    key_prefix = Column(
        String(20),
        nullable=False,
        index=True,
        comment="First few chars for identification",
    )
    key_hash = Column(
        String(255),
        nullable=False,
        unique=True,
        comment="Hashed version of the full key",
    )

    # Ownership and organization
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    organization_id = Column(UUID, ForeignKey("organizations.id"), nullable=False)

    # Permissions and scopes
    scopes = Column(
        JSONB, nullable=False, default=list, comment="List of allowed scopes"
    )

    # Rate limiting
    rate_limit_per_minute = Column(
        Integer, default=60, comment="API calls per minute limit"
    )
    rate_limit_per_hour = Column(
        Integer, default=1000, comment="API calls per hour limit"
    )
    rate_limit_per_day = Column(
        Integer, default=10000, comment="API calls per day limit"
    )

    # Status and lifecycle
    is_active = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime, nullable=True, comment="Optional expiration date")

    # Usage tracking
    last_used_at = Column(DateTime, nullable=True)
    total_requests = Column(Integer, default=0)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(UUID, nullable=True)

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Relationships
    user = relationship("User", back_populates="api_keys")
    organization = relationship("Organization", back_populates="api_keys")

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("api_keys.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("ApiKey", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("ApiKey", back_populates="children", remote_side=[id])

    def __repr__(self):
        return "<ApiKey(id={}, name={})>".format(self.id, self.name)

    @classmethod
    def generate_key(cls) -> tuple[str, str, str]:
        """
        Generate a new API key with prefix and hash.

        Returns:
            tuple: (full_key, prefix, hash)
        """
        # Generate a secure random key
        key_body = secrets.token_urlsafe(32)
        prefix = "ak_" + key_body[:8]  # ak_ for "api key"
        full_key = prefix + "_" + key_body[8:]

        # Hash the full key for storage
        from aiplanet_platform.core.security import get_password_hash

        key_hash = get_password_hash(full_key)

        return full_key, prefix, key_hash

    def is_expired(self) -> bool:
        """Check if the API key is expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    def has_scope(self, required_scope: str) -> bool:
        """Check if the API key has a specific scope"""
        return required_scope in (self.scopes or [])

    def update_usage(self):
        """Update usage statistics"""
        self.last_used_at = datetime.utcnow()
        self.total_requests += 1
