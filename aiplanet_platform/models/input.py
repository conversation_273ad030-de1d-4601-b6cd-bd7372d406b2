"""
Input model
"""
import uuid
from typing import Union

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>olean, Column, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from aiplanet_platform.core.database import Base, ComponentModel


class Input(Base):
    """Input database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Inputs".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Input Config
    component: Union[ComponentModel, dict] = Column(JSONB, nullable=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Organization relationship
    organization_id = Column(
        UUID, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True
    )
    organization = relationship(
        "aiplanet_platform.models.organization.Organization",
        back_populates="inputs",
    )

    teams = relationship(
        "aiplanet_platform.models.team.Team",
        secondary="team_inputs",  # change table name accordingly
        back_populates="team_inputs",  # change relationship name accordingly
    )

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("inputs.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Input", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Input", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<Input(id={id_val})>"
        except Exception:
            return "<Input(detached)>"
