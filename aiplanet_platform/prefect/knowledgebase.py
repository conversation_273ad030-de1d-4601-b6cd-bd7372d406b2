import logging
from slugify import slugify
from fastapi import Request

from aiplanet_platform.prefect.prefect_client import (
    get_deployment_by_name,
    create_flow_run_by_deployment,
)
from aiplanet_platform.models.file import File
from aiplanet_platform.core.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


FLOW_NAME = settings.FLOW_NAME
DEPLOYMENT_NAME = settings.DEPLOYMENT_NAME
# DEPLOYMENT_NAME_URL = settings.DEPLOYMENT_NAME_URL


async def trigger_prefect_flow_run(
    file: File, request: Request, fast_mode: bool = True
):
    base_url = str(request.base_url).replace("http://", "https://")
    auth_header = request.headers.get("Authorization")
    base_url = str(request.base_url).replace("http://", "https://")
    folder_name = file.folder.name
    folder_chunking_config = file.folder.chunking_config
    folder_embedding_config = file.folder.embedding_config
    folder_vector_db_config = file.folder.vector_db_config
    # Chunking configuration
    chunking_config = folder_chunking_config or {
        "type": "RecursiveCharacterTextSplitter",
        "kwargs": {
            "chunk_size": settings.CHUNK_SIZE,
            "chunk_overlap": settings.CHUNK_OVERLAP,
        },
    }

    # Weaviate configuration
    weaviate_config = folder_vector_db_config or {
        "url": None,
        "api_key": settings.WEAVIATE_API_KEY,
        "headers": {
            "X-Openai-Api-Key": settings.OPENAI_API_KEY,
        },
        "grpc_host": settings.WEAVIATE_GRPC_HOST,
        "grpc_port": settings.WEAVIATE_GRPC_PORT,
        "http_host": settings.WEAVIATE_HTTP_HOST,
        "http_port": settings.WEAVIATE_HTTP_PORT,
        "grpc_secure": settings.WEAVIATE_GRPC_SECURE,
        "http_secure": settings.WEAVIATE_HTTP_SECURE,
        "is_embedded": settings.WEAVIATE_IS_EMBEDDED,
        "collection_name": folder_name.replace(" ", "_"),
        "additional_config": {"timeout": [30, 30]},
        "embedding_provider": "openai",
    }

    # Embedding configuration
    embedding_config = folder_embedding_config or {
        "type": "OpenAIEmbeddings",
        "kwargs": {"model": settings.OPENAI_EMBEDDING_MODEL},
    }

    # Webhook configuration
    webhook_config = {
        "api_key": auth_header,
        "callback_url": f"{base_url}api/v1/private/knowledgebases/files/update-status",
    }

    # Sources configuration
    sources = [
        {
            "type": "aws_object",
            "kwargs": {
                "file_id": str(file.id),
                "fast_mode": fast_mode,
                "bucket_name": settings.AWS_S3_BUCKET_NAME,
                "extraction_library": "llamaparser",
            },
            "source_name": file.s3_path,
        }
    ]

    parameters = {
        "chunking_config": chunking_config,
        "weaviate_config": weaviate_config,
        "embedding_config": embedding_config,
        "sources": sources,
        "webhook_config": webhook_config,
    }

    flow_run_name = f"{slugify(file.name)}_{str(file.id)[:8]}"
    deployment_id = get_deployment_by_name(
        flow_name=FLOW_NAME, deployment_name=DEPLOYMENT_NAME
    )["id"]
    logger.info("Deployment Id", deployment_id)
    flow_run_metadata = create_flow_run_by_deployment(
        deployment_id=deployment_id,
        name=flow_run_name,
        parameters=parameters,
    )
    logger.info("Flow run metadata", flow_run_metadata)
    logger.info(parameters)
