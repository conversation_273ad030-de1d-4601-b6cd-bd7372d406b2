import logging
import json
import requests
from typing import Dict, Optional
from aiplanet_platform.core.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

DEFAULT_EMPIRICAL_POLICY = {
    "retries": None,
    "retry_delay": None,
    "pause_keys": [],
    "resuming": None,
}

DEFAULT_STATE = {
    "type": "SCHEDULED",
    "message": "",
    "state_details": {"scheduled_time": None, "cache_expiration": None},
}

DEFAULT_WORK_QUEUE_NAME = "default"


def create_flow_run_by_deployment(
    deployment_id: str,
    name: str,
    parameters: dict,
    state: Optional[Dict] = DEFAULT_STATE,
    work_queue_name: Optional[Dict] = DEFAULT_WORK_QUEUE_NAME,
    empirical_policy: Optional[Dict] = DEFAULT_EMPIRICAL_POLICY,
):
    """Create a flow run for a specific deployment."""
    try:
        payload = {
            "name": name,
            "parameters": parameters,
            "state": state,
            "work_queue_name": work_queue_name,
            "empirical_policy": empirical_policy,
        }

        logger.info(f"Creating flow run for deployment {deployment_id}")
        logger.info("Payload:", json.dumps(payload, indent=2))

        # Fix URL construction
        base_url = settings.PREFECT_API_URL.rstrip("/")
        url = f"{base_url}/api/deployments/{deployment_id}/create_flow_run"
        logger.info(f"Request URL: {url}")

        headers = {
            "Authorization": f"Bearer {settings.PREFECT_API_KEY}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        logger.info("Making POST request...")
        response = requests.post(url, headers=headers, json=payload)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response content: {response.text}")

        response.raise_for_status()

        if not response.text:
            raise ValueError("Empty response from Prefect API")

        return response.json()

    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {str(e)}")
        raise RuntimeError(f"Failed to create flow run: {str(e)}")
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON response. Response content: {response.text}")
        raise RuntimeError(f"Invalid JSON response from Prefect API: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise RuntimeError(f"Error creating flow run: {str(e)}")


def get_deployment_by_name(flow_name: str, deployment_name: str):
    """Get deployment details by flow name and deployment name."""
    try:
        logger.info(
            f"Getting deployment: flow_name={flow_name}, deployment_name={deployment_name}"
        )

        # Fix URL construction
        base_url = settings.PREFECT_API_URL.rstrip("/")
        url = f"{base_url}/api/deployments/name/{flow_name}/{deployment_name}"
        logger.info(f"Request URL: {url}")

        headers = {
            "Authorization": f"Bearer {settings.PREFECT_API_KEY}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        logger.info("Making GET request...")
        response = requests.get(url, headers=headers)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response content: {response.text}")

        response.raise_for_status()

        if not response.text:
            raise ValueError("Empty response from Prefect API")

        return response.json()

    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {str(e)}")
        raise RuntimeError(f"Failed to get deployment: {str(e)}")
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON response. Response content: {response.text}")
        raise RuntimeError(f"Invalid JSON response from Prefect API: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise RuntimeError(f"Error getting deployment: {str(e)}")
