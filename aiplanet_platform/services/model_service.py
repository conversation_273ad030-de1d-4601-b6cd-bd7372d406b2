"""
Service for model operations
"""
import json
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.model import Model
from aiplanet_platform.services.agent_service import AgentService


class ModelService:
    """Service for model operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = Model
        self.agent_service = AgentService(db)

    def fetch_resource_by_id(self, resource_id: UUID) -> Optional[Model]:
        """
        Fetch a model by ID.

        Args:
            resource_id: ID of the model

        Returns:
            Model object if found, None otherwise
        """
        try:
            return (
                self.db.query(Model)
                .filter(Model.id == resource_id, Model.is_deleted.is_(False))
                .first()
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
    ) -> List[Model]:
        """
        Fetch models by filters.
        """
        try:
            query = self.db.query(Model).filter(Model.is_deleted.is_(False))

            # Apply filters
            for key, value in filters.items():
                if key == "component":
                    # Handle JSON structure filtering
                    value = json.loads(value) if isinstance(value, str) else value
                    for k, v in value.items():
                        # Handle nested JSON
                        if isinstance(v, dict):
                            for sub_k, sub_v in v.items():
                                query = query.filter(
                                    Model.component[k].op("->>")(sub_k) == str(sub_v)
                                )
                        else:
                            query = query.filter(Model.component.op("->>")(k) == str(v))
                    continue

                elif key == "name_like":
                    query = query.filter(
                        Model.component.op("->>")("label").ilike(f"%{value}%")
                    )
                elif hasattr(Model, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]
                        query = query.filter(
                            getattr(Model, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Model, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Model, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Model, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Model, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Model, field_name).in_(value))
                    else:
                        query = query.filter(getattr(Model, key) == value)

            # Apply sorting
            if sort_by and hasattr(Model, sort_by):
                order_field = getattr(Model, sort_by)
                if sort_order.lower() == "desc":
                    order_field = order_field.desc()
                query = query.order_by(order_field)

            return query.offset(skip).limit(limit).all()

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_resource(self, data: Dict[str, Any]) -> Model:
        """
        Create a new model.

        Args:
            data: Dictionary of model data

        Returns:
            Created Model object
        """
        try:
            resource = Model(**data)
            self.db.add(resource)
            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self, resource_id: UUID, data: Dict[str, Any]
    ) -> Optional[Model]:
        """
        Update a model by ID.

        Args:
            resource_id: ID of the model
            data: Dictionary of data to update

        Returns:
            Updated Model object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return None

            for key, value in data.items():
                if hasattr(resource, key):
                    setattr(resource, key, value)

            agents = self.agent_service.fetch_resource_by_filters(
                {"model_id": resource_id}
            )

            for agent in agents:
                if agent.teams:
                    for team in agent.teams:
                        if team.is_deployed:
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail="Cannot update model. Used in a deployed workflow.",
                            )

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[Model]:
        """
        Update models by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated Model objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a model by ID.

        Args:
            resource_id: ID of the model

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete models by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a model by ID.

        Args:
            resource_id: ID of the model

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.db.query(Model).filter(Model.id == resource_id).first()

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def count_resources(self, filters: Dict[str, Any]) -> int:
        """
        Count agents by filters (for pagination).
        """
        try:
            query = self.db.query(Model).filter(Model.is_deleted.is_(False))

            # Apply the same filters as in fetch_resource_by_filters
            for key, value in filters.items():
                if key == "component":
                    # Handle JSON structure filtering
                    value = json.loads(value) if isinstance(value, str) else value
                    for k, v in value.items():
                        # Handle nested JSON
                        if isinstance(v, dict):
                            for sub_k, sub_v in v.items():
                                query = query.filter(
                                    Model.component[k].op("->>")(sub_k) == str(sub_v)
                                )
                        else:
                            query = query.filter(Model.component.op("->>")(k) == str(v))
                    continue

                # Handle special filters for name (stored in component.label)
                if key == "name":
                    # Name is stored in component.label
                    query = query.filter(Model.component.op("->>")("label") == value)
                elif key == "name_like":
                    # Name is stored in component.label, use ilike for partial matching
                    query = query.filter(
                        Model.component.op("->>")("label").ilike(f"%{value}%")
                    )
                elif key == "is_active":
                    # For agents, is_active means not deleted (agents don't have deployment status)
                    if value:
                        query = query.filter(Model.is_deleted.is_(False))
                    else:
                        query = query.filter(Model.is_deleted.is_(True))
                elif hasattr(Model, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]
                        query = query.filter(
                            getattr(Model, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Model, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Model, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Model, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Model, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Model, field_name).in_(value))
                    else:
                        query = query.filter(getattr(Model, key) == value)

            return query.count()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e
