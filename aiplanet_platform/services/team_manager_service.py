"""
Service for team manager operations
"""
import aiofiles
import asyncio
import logging
import json
import os
import yaml
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Union, Callable, AsyncGenerator, Sequence
from sqlalchemy.orm import Session
from fastapi import Depends
from autogen_core.logging import LL<PERSON>allEvent
from autogen_core import ComponentModel, EVENT_LOGGER_NAME, CancellationToken
from autogen_agentchat.teams import BaseGroupChat
from autogen_agentchat.agents import UserProxyAgent
from autogen_agentchat.messages import BaseAgentEvent, BaseChatMessage
from autogen_agentchat.base import TaskResult

from aiplanet_platform.core.database import get_db
from aiplanet_platform.constants.team_manager import (
    LLMCallEventMessage,
    EnvironmentVariable,
    TeamResult,
    TaskResult as CustomTaskResult,
)

logger = logging.getLogger(__name__)


class RunEventLogger(logging.Handler):
    """Event logger that queues LLMCallEvents for streaming"""

    def __init__(self):
        super().__init__()
        self.events = asyncio.Queue()

    def emit(self, record: logging.LogRecord):
        if isinstance(record.msg, LLMCallEvent):
            self.events.put_nowait(LLMCallEventMessage(content=str(record.msg)))


class TeamManagerService:
    """Service for team_manager operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db

    @staticmethod
    async def load_from_file(path: Union[str, Path]) -> dict:
        """Load team configuration from JSON/YAML file"""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(f"Config file not found: {path}")

        async with aiofiles.open(path) as f:
            content = await f.read()
            if path.suffix == ".json":
                return json.loads(content)
            elif path.suffix in (".yml", ".yaml"):
                return yaml.safe_load(content)
            raise ValueError(f"Unsupported file format: {path.suffix}")

    @staticmethod
    async def load_from_directory(directory: Union[str, Path]) -> List[dict]:
        """Load all team configurations from a directory"""
        directory = Path(directory)
        configs = []
        valid_extensions = {".json", ".yaml", ".yml"}

        for path in directory.iterdir():
            if path.is_file() and path.suffix.lower() in valid_extensions:
                try:
                    config = await TeamManagerService.load_from_file(path)
                    configs.append(config)
                except Exception as e:
                    logger.error(f"Failed to load {path}: {e}")

        return configs

    async def _create_team(
        self,
        team_config: Union[str, Path, dict, ComponentModel],
        input_func: Optional[Callable] = None,
        env_vars: Optional[List[EnvironmentVariable]] = None,
    ) -> BaseGroupChat:
        """Create a new team"""
        # Validate team configuration
        # Create team in database
        # Return team details
        if isinstance(team_config, (str, Path)):
            config = await self.load_from_file(team_config)
        elif isinstance(team_config, dict):
            config = team_config
        else:
            config = team_config.model_dump()

        # Load env vars into environment if provided
        if env_vars:
            logger.info("Loading environment variables")
            for var in env_vars:
                os.environ[var.name] = var.value

        self._team = BaseGroupChat.load_component(config)

        for agent in self._team._participants:
            if (
                hasattr(agent, "input_func")
                and isinstance(agent, UserProxyAgent)
                and input_func
            ):
                agent.input_func = input_func

        return self._team

    async def run_stream(
        self,
        task: str | BaseChatMessage | Sequence[BaseChatMessage] | None,
        team_config: Union[str, Path, dict, ComponentModel],
        input_func: Optional[Callable] = None,
        cancellation_token: Optional[CancellationToken] = None,
        env_vars: Optional[List[EnvironmentVariable]] = None,
        state: Optional[dict] = None,
    ) -> AsyncGenerator[
        Union[
            BaseAgentEvent | BaseChatMessage | LLMCallEvent, BaseChatMessage, TeamResult
        ],
        None,
    ]:
        """Stream team execution results"""
        start_time = datetime.now()
        team = None

        # Setup logger correctly
        logger = logging.getLogger(EVENT_LOGGER_NAME)
        logger.setLevel(logging.INFO)
        llm_event_logger = RunEventLogger()
        logger.handlers = [llm_event_logger]  # Replace all handlers

        try:
            team = await self._create_team(team_config, input_func, env_vars)
            if state:
                await team.load_state(state)

            async for message in team.run_stream(
                task=task, cancellation_token=cancellation_token
            ):
                if cancellation_token and cancellation_token.is_cancelled():
                    break

                if isinstance(message, TaskResult):
                    yield TeamResult(
                        task_result=CustomTaskResult(
                            messages=message.messages, stop_reason=message.stop_reason
                        ),
                        usage="",
                        duration=(datetime.now() - start_time).total_seconds(),
                    )
                else:
                    yield message

                # Check for any LLM events
                while not llm_event_logger.events.empty():
                    event = await llm_event_logger.events.get()
                    yield event
        finally:
            # Cleanup - remove our handler
            if llm_event_logger in logger.handlers:
                logger.handlers.remove(llm_event_logger)

            # Ensure cleanup happens
            if team and hasattr(team, "_participants"):
                for agent in team._participants:
                    if hasattr(agent, "close"):
                        await agent.close()

    async def save_state(self):
        return await self._team.save_state()

    async def load_state(self, state: dict):
        await self._team.load_state(state)
