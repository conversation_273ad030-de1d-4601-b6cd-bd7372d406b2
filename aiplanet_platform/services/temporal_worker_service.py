"""
Temporal worker service for running workflows and activities
"""
import asyncio
import logging
from typing import Optional

from temporalio.worker import Worker

from aiplanet_platform.core.temporal_config import TemporalClient
from aiplanet_platform.jobs.temporal_streaming_workflow import (
    TeamStreamingWorkflow,
    setup_team_environment,
    execute_team_stream,
    update_run_status,
)

logger = logging.getLogger(__name__)


class TemporalWorkerService:
    """Service for managing Temporal workers"""

    def __init__(self):
        self.worker: Optional[Worker] = None
        self.is_running = False

    async def start_worker(self) -> None:
        """Start the Temporal worker"""
        if self.is_running:
            logger.warning("Worker is already running")
            return

        try:
            client = await TemporalClient.get_client()
            config = TemporalClient.get_config()

            # Create worker with workflows and activities
            self.worker = Worker(
                client,
                task_queue=config.task_queue,
                workflows=[TeamStreamingWorkflow],
                activities=[
                    setup_team_environment,
                    execute_team_stream,
                    update_run_status,
                ],
                max_concurrent_activities=10,  # Adjust based on your needs
                max_concurrent_workflow_tasks=10,
            )

            logger.info(f"Starting Temporal worker on task queue: {config.task_queue}")

            # Start worker in background task
            self.is_running = True
            await self.worker.run()

        except Exception as e:
            logger.error(f"Failed to start Temporal worker: {e}")
            self.is_running = False
            raise

    async def stop_worker(self) -> None:
        """Stop the Temporal worker"""
        if self.worker and self.is_running:
            logger.info("Stopping Temporal worker")
            await self.worker.shutdown()
            self.is_running = False
            self.worker = None

    async def health_check(self) -> bool:
        """Check if worker is healthy"""
        return self.is_running and self.worker is not None


# Global worker instance
_worker_service: Optional[TemporalWorkerService] = None


async def get_worker_service() -> TemporalWorkerService:
    """Get or create worker service instance"""
    global _worker_service
    if _worker_service is None:
        _worker_service = TemporalWorkerService()
    return _worker_service


async def start_temporal_worker():
    """Start Temporal worker (for use in startup events)"""
    try:
        worker_service = await get_worker_service()
        # Start worker in background task
        asyncio.create_task(worker_service.start_worker())
        logger.info("Temporal worker started successfully")
    except Exception as e:
        logger.error(f"Failed to start Temporal worker: {e}")
        # Don't raise exception to prevent app startup failure


async def stop_temporal_worker():
    """Stop Temporal worker (for use in shutdown events)"""
    global _worker_service
    if _worker_service:
        await _worker_service.stop_worker()
        _worker_service = None
        logger.info("Temporal worker stopped")
