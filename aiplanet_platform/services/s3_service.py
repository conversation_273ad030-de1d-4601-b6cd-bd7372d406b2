"""
S3 service for file upload/download operations with presigned URLs
"""
import logging
from typing import Any, Dict, List, Optional
from uuid import UUID

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from fastapi import Depends, HTTPException, UploadFile, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.config import get_settings
from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.file import File, FileStatus
from aiplanet_platform.services.file_service import FileService
from aiplanet_platform.services.folder_service import FolderService

logger = logging.getLogger(__name__)


class S3Service:
    """Service for S3 operations with file tracking"""

    def __init__(
        self,
        db: Session = Depends(get_db),
        file_service: FileService = Depends(),
        folder_service: FolderService = Depends(),
    ):
        """
        Initialize the S3 service.

        Args:
            db: SQLAlchemy database session
            file_service: File service for database operations
            folder_service: Folder service for database operations
        """
        self.db = db
        self.file_service = file_service
        self.folder_service = folder_service
        self.settings = get_settings()

        # S3 configuration
        self.bucket_name = self.settings.AWS_S3_BUCKET_NAME
        self.region_name = self.settings.AWS_REGION
        self.aws_access_key_id = self.settings.AWS_ACCESS_KEY_ID
        self.aws_secret_access_key = self.settings.AWS_SECRET_ACCESS_KEY

        # Initialize S3 client
        self._s3_client = None
        self._initialize_s3_client()

    def _initialize_s3_client(self):
        """Initialize S3 client with credentials"""
        try:
            if self.aws_access_key_id and self.aws_secret_access_key:
                self._s3_client = boto3.client(
                    "s3",
                    aws_access_key_id=self.aws_access_key_id,
                    aws_secret_access_key=self.aws_secret_access_key,
                    region_name=self.region_name,
                )
                logger.info("S3 client initialized with provided credentials")
            else:
                # Use default credentials (IAM role, environment, etc.)
                self._s3_client = boto3.client("s3", region_name=self.region_name)
                logger.info("S3 client initialized with default credentials")
        except NoCredentialsError:
            logger.error("Failed to initialize S3 client: AWS credentials not found")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="AWS credentials not found",
            )

    @property
    def s3_client(self):
        """Get S3 client instance"""
        if not self._s3_client:
            self._initialize_s3_client()
        return self._s3_client

    def _generate_s3_key(self, folder_id: UUID, filename: str) -> str:
        """
        Generate S3 key for file using original filename.

        Args:
            folder_id: Folder ID
            filename: Original filename

        Returns:
            S3 key path
        """
        return f"uploads/{folder_id}/files/{filename}"

    async def upload_file(
        self, file: UploadFile, folder_id: UUID, description: Optional[str] = None
    ) -> File:
        """
        Upload a single file to S3 first, then create database record.

        Args:
            file: Upload file object
            folder_id: Folder ID to associate with
            description: File description

        Returns:
            Created File object
        """
        try:
            # Generate S3 key
            s3_key = self._generate_s3_key(folder_id, file.filename)

            # Upload to S3 first
            await self._upload_to_s3(file, s3_key)

            # Only create database record after successful S3 upload
            file_data = {
                "name": file.filename,
                "type": file.content_type or "application/octet-stream",
                "size": file.size,
                "s3_path": s3_key,
                "folder_id": folder_id,
                "status": FileStatus.UPLOADED,
            }
            logger.info(f"Creating database record for file {file.filename}")
            db_file = self.file_service.create_resource(file_data)
            return db_file

        except Exception as e:
            # If S3 upload fails, no database record is created
            # If database creation fails after S3 upload, we should clean up S3
            if "s3_key" in locals():
                try:
                    # Clean up S3 file if database creation fails
                    self.s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
                    logger.info(
                        f"Cleaned up S3 file {s3_key} after failed database creation"
                    )
                except Exception as error:
                    # Log cleanup failure but don't raise
                    logger.error(f"Failed to cleanup S3 file {s3_key}: {str(e)}")
                    raise error

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload file: {str(e)}",
            )

    async def create_file_record_and_upload(
        self, file: UploadFile, folder_id: UUID, description: Optional[str] = None
    ) -> File:
        """
        Alternative workflow: Create database record first, then upload to S3.
        Useful for tracking upload progress and providing immediate file_id.

        Args:
            file: Upload file object
            folder_id: Folder ID to associate with
            description: File description

        Returns:
            Created File object
        """
        try:
            # Generate S3 key
            s3_key = self._generate_s3_key(folder_id, file.filename)

            # Create file record in database first with QUEUE status
            file_data = {
                "name": file.filename,
                "type": file.content_type or "application/octet-stream",
                "size": file.size,
                "s3_path": s3_key,
                "folder_id": folder_id,
                "status": FileStatus.QUEUE,
                "description": description,
            }

            db_file = self.file_service.create_resource(file_data)

            try:
                # Update status to processing
                self.file_service.update_resource_by_id(
                    db_file.id, {"status": FileStatus.PROCESSING}
                )

                # Upload to S3
                await self._upload_to_s3(file, s3_key)

                # Update file status to success
                self.file_service.update_resource_by_id(
                    db_file.id, {"status": FileStatus.SUCCESS}
                )

            except Exception as e:
                # Update file status to failed
                self.file_service.update_resource_by_id(
                    db_file.id, {"status": FileStatus.FAILED}
                )
                raise e

            return db_file

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload file: {str(e)}",
            )

    async def _upload_to_s3(self, file: UploadFile, s3_key: str):
        """
        Upload file to S3.

        Args:
            file: Upload file object
            s3_key: S3 key path
        """
        try:
            # Read file content
            content = await file.read()

            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=content,
                ContentType=file.content_type or "application/octet-stream",
            )
            logger.info(f"Uploaded file {file.filename} to S3 with key {s3_key}")

            # Reset file pointer
            await file.seek(0)

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload to S3: {str(e)}",
            )

    async def upload_files_bulk(
        self,
        files: List[UploadFile],
        folder_id: UUID,
        description: Optional[str] = None,
    ) -> List[File]:
        """
        Upload multiple files in parallel for better performance.

        Args:
            files: List of upload file objects
            folder_id: Folder ID to associate with
            description: File description

        Returns:
            List of created File objects
        """
        import asyncio

        async def upload_single_file(file: UploadFile) -> Optional[File]:
            try:
                return await self.upload_file(file, folder_id, description)
            except Exception as e:
                # Log the error but continue with other files
                print(f"Failed to upload file {file.filename}: {str(e)}")
                return None

        # Upload all files in parallel
        tasks = [upload_single_file(file) for file in files]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out None results and exceptions
        uploaded_files = []
        for result in results:
            if result is not None and not isinstance(result, Exception):
                uploaded_files.append(result)

        return uploaded_files

    async def create_folder_and_upload_files(
        self,
        folder_name: str,
        organization_id: UUID,
        files: List[UploadFile],
        folder_description: Optional[str] = None,
        chunking_config: Optional[Dict[str, Any]] = None,
        embedding_config: Optional[Dict[str, Any]] = None,
        vector_db_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Create a folder and upload files to it in one atomic operation.

        Args:
            folder_name: Name of the folder to create
            organization_id: Organization ID
            files: List of files to upload
            folder_description: Optional folder description
            chunking_config: Optional chunking configuration
            embedding_config: Optional embedding configuration
            vector_db_config: Optional vector database configuration

        Returns:
            Dict containing created folder and uploaded files
        """
        try:
            # Create folder first
            folder_data = {
                "name": folder_name,
                "description": folder_description,
                "organization_id": organization_id,
                "chunking_config": chunking_config or {},
                "embedding_config": embedding_config or {},
                "vector_db_config": vector_db_config or {},
            }

            folder = self.folder_service.create_resource(folder_data)

            # Upload files to the created folder
            uploaded_files = []
            if files:
                uploaded_files = await self.upload_files_bulk(files, folder.id)

            return {
                "folder": folder,
                "uploaded_files": uploaded_files,
                "folder_id": folder.id,
                "total_files": len(files),
                "successful_uploads": len(uploaded_files),
                "failed_uploads": len(files) - len(uploaded_files),
            }

        except Exception as e:
            # If folder creation fails, nothing to clean up
            # If file uploads fail, folder remains but files may be partially uploaded
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create folder and upload files: {str(e)}",
            )

    def download_file(self, file_id: UUID) -> Dict[str, Any]:
        """
        Download file from S3.

        Args:
            file_id: File ID

        Returns:
            Dict with file content and metadata
        """
        try:
            # Get file record from database
            db_file = self.file_service.fetch_resource_by_id(file_id)
            if not db_file:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="File not found"
                )

            if not db_file.s3_path:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="File not uploaded to S3",
                )

            # Download from S3
            response = self.s3_client.get_object(
                Bucket=self.bucket_name, Key=db_file.s3_path
            )

            return {
                "content": response["Body"].read(),
                "content_type": db_file.type,
                "filename": db_file.name,
                "size": db_file.size,
            }

        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="File not found in S3"
                )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to download file: {str(e)}",
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to download file: {str(e)}",
            )

    def generate_presigned_url(
        self, file_id: UUID, expiration: int = 3600, operation: str = "get_object"
    ) -> str:
        """
        Generate presigned URL for file access.

        Args:
            file_id: File ID
            expiration: URL expiration time in seconds (default 1 hour)
            operation: S3 operation (get_object, put_object)

        Returns:
            Presigned URL string
        """
        try:
            # Get file record from database
            db_file = self.file_service.fetch_resource_by_id(file_id)
            if not db_file:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="File not found"
                )

            if not db_file.s3_path:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="File not uploaded to S3",
                )

            # Generate presigned URL
            url = self.s3_client.generate_presigned_url(
                operation,
                Params={"Bucket": self.bucket_name, "Key": db_file.s3_path},
                ExpiresIn=expiration,
            )

            return url

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate presigned URL: {str(e)}",
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate presigned URL: {str(e)}",
            )

    def generate_presigned_upload_url(
        self, folder_id: UUID, filename: str, content_type: str, expiration: int = 3600
    ) -> Dict[str, Any]:
        """
        Generate presigned URL for direct upload to S3.

        Args:
            folder_id: Folder ID
            filename: Original filename
            content_type: File content type
            expiration: URL expiration time in seconds

        Returns:
            Dict with presigned URL and file metadata
        """
        try:
            # Generate S3 key
            s3_key = self._generate_s3_key(folder_id, filename)

            # Generate presigned URL for upload
            url = self.s3_client.generate_presigned_url(
                "put_object",
                Params={
                    "Bucket": self.bucket_name,
                    "Key": s3_key,
                    "ContentType": content_type,
                },
                ExpiresIn=expiration,
            )

            return {
                "upload_url": url,
                "s3_key": s3_key,
                "bucket": self.bucket_name,
                "expires_in": expiration,
            }

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate presigned upload URL: {str(e)}",
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate presigned upload URL: {str(e)}",
            )

    def delete_file(self, file_id: UUID) -> bool:
        """
        Delete file from S3 and update database record.

        Args:
            file_id: File ID

        Returns:
            True if successful
        """
        try:
            # Get file record from database
            db_file = self.file_service.fetch_resource_by_id(file_id)
            if not db_file:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="File not found"
                )

            # Delete from S3 if exists
            if db_file.s3_path:
                try:
                    self.s3_client.delete_object(
                        Bucket=self.bucket_name, Key=db_file.s3_path
                    )
                except ClientError as e:
                    if e.response["Error"]["Code"] != "NoSuchKey":
                        raise e

            # Soft delete in database
            self.file_service.remove_resource_by_id(file_id)

            return True

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete file: {str(e)}",
            )

    def get_file_metadata(self, file_id: UUID) -> Dict[str, Any]:
        """
        Get file metadata from S3.

        Args:
            file_id: File ID

        Returns:
            File metadata dict
        """
        try:
            # Get file record from database
            db_file = self.file_service.fetch_resource_by_id(file_id)
            if not db_file:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="File not found"
                )

            if not db_file.s3_path:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="File not uploaded to S3",
                )

            # Get metadata from S3
            response = self.s3_client.head_object(
                Bucket=self.bucket_name, Key=db_file.s3_path
            )

            return {
                "file_id": db_file.id,
                "filename": db_file.name,
                "content_type": db_file.type,
                "size": db_file.size,
                "last_modified": response.get("LastModified"),
                "etag": response.get("ETag"),
                "s3_path": db_file.s3_path,
                "status": db_file.status,
            }

        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="File not found in S3"
                )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get file metadata: {str(e)}",
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get file metadata: {str(e)}",
            )

    def list_files_in_folder(self, folder_id: UUID) -> List[Dict[str, Any]]:
        """
        List all files in a folder with their metadata.

        Args:
            folder_id: Folder ID

        Returns:
            List of file metadata dicts
        """
        try:
            # Get files from database
            files = self.file_service.fetch_resource_by_filters(
                {"folder_id": folder_id}
            )

            file_list = []
            for file in files:
                file_data = {
                    "file_id": file.id,
                    "filename": file.name,
                    "content_type": file.type,
                    "size": file.size,
                    "status": file.status,
                    "created_at": file.created_at,
                    "updated_at": file.updated_at,
                    "s3_path": file.s3_path,
                }
                file_list.append(file_data)

            return file_list

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list files: {str(e)}",
            )
