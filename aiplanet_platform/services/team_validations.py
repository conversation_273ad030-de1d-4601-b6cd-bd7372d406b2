"""
Team validation service for team operations
"""

from typing import Optional, List
from uuid import UUID
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.services.input_service import InputService


class TeamValidationService:
    """Service for team validation operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the validation service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.input_service = InputService(db)

    def validate_chat_input_count(self, team_input_ids: Optional[List[UUID]]) -> None:
        """
        Validate that only 1 ChatInput component is present in the inputs.

        Args:
            team_input_ids: List of input IDs for the team

        Raises:
            HTTPException: If more than 1 ChatInput component is found or if invalid input IDs are provided
        """
        if not team_input_ids:
            return

        chat_input_count = 0
        invalid_input_ids = []

        for input_id in team_input_ids:
            try:
                input_obj = self.input_service.fetch_resource_by_id(input_id)
                if not input_obj:
                    invalid_input_ids.append(input_id)
                    continue

                # Handle both dict and object representations of component
                provider = self._get_component_provider(input_obj.component)

                if provider == "autogen_core.io.ChatInput":
                    chat_input_count += 1

            except Exception:
                # Log the error but continue validation for other inputs
                invalid_input_ids.append(input_id)

        # Check for invalid input IDs
        if invalid_input_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid input IDs: {invalid_input_ids}",
            )

        # Check for multiple ChatInput components
        if chat_input_count > 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only one ChatInput component is allowed per team",
            )

    def _get_component_provider(self, component) -> Optional[str]:
        """
        Helper method to extract provider from component.

        Args:
            component: Component object or dict

        Returns:
            Provider string if found, None otherwise
        """
        if isinstance(component, dict):
            return component.get("provider")
        elif hasattr(component, "provider"):
            return component.provider
        return None
