"""
OAuth service for handling Google and GitHub authentication
"""
import httpx
import secrets
from enum import Enum
from authlib.integrations.starlette_client import OAuth
from authlib.integrations.base_client import OAuthError
from typing import Any, Dict
from sqlalchemy.orm import Session
from fastapi import Depends, HTTPException, status
from urllib.parse import urlencode

from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.config import get_settings

settings = get_settings()


class Provider(Enum):
    GOOGLE = "google"
    GITHUB = "github"


class OAuthService:
    """Service for oauth operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.oauth = OAuth()

        # Config Google
        self.oauth.register(
            name=Provider.GOOGLE.value,
            client_id=settings.GOOGLE_CLIENT_ID,
            client_secret=settings.GOOGLE_CLIENT_SECRET,
            server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
            client_kwargs={"scope": "openid email profile"},
        )

        # Config GitHub
        self.oauth.register(
            name=Provider.GITHUB.value,
            api_base_url="https://api.github.com/",
            access_token_url="https://github.com/login/oauth/access_token",
            authorize_url="https://github.com/login/oauth/authorize",
            client_kwargs={"scope": "user:email"},
        )

    def generate_state(self) -> str:
        """
        Generate a random state for OAuth authentication.

        Returns:
            str: Random state
        """
        return secrets.token_urlsafe(32)

    def get_authorization_url(self, provider: str, state: str) -> str:
        """
        Get the authorization URL for the given provider.

        Args:
            provider: Provider name (google or github)
            state: Random state

        Returns:
            str: Authorization URL
        """
        import logging

        logger = logging.getLogger(__name__)

        if provider == Provider.GOOGLE.value:
            redirect_uri = (
                f"{settings.FRONTEND_URL}{settings.OAUTH_REDIRECT_GOOGLE_URI}"
            )
            logger.info(f"Redirect URI: {redirect_uri}")

            params = {
                "client_id": settings.GOOGLE_CLIENT_ID,
                "redirect_uri": redirect_uri,
                "scope": "openid email profile",
                "response_type": "code",
                "state": state,
            }
            return f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"
        elif provider == Provider.GITHUB.value:
            redirect_uri = (
                f"{settings.FRONTEND_URL}{settings.OAUTH_REDIRECT_GITHUB_URI}"
            )
            logger.info(f"Redirect URI: {redirect_uri}")

            params = {
                "client_id": settings.GITHUB_CLIENT_ID,
                "redirect_uri": redirect_uri,
                "scope": "user:email",
                "state": state,
            }
            return f"https://github.com/login/oauth/authorize?{urlencode(params)}"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid provider"
            )

    async def exchange_code_for_token(self, provider: str, code: str) -> Dict[str, Any]:
        """
        Exchange the code for a token.

        Args:
            provider: Provider name (google or github)
            code: Code from the provider

        Returns:
            Dict[str, Any]: Token
        """
        if provider == Provider.GOOGLE.value:
            redirect_uri = (
                f"{settings.FRONTEND_URL}{settings.OAUTH_REDIRECT_GOOGLE_URI}"
            )
            token = await self._exchange_google_token(code, redirect_uri)
        elif provider == Provider.GITHUB.value:
            redirect_uri = (
                f"{settings.FRONTEND_URL}{settings.OAUTH_REDIRECT_GITHUB_URI}"
            )
            token = await self._exchange_github_token(code, redirect_uri)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid provider"
            )
        return token

    async def _exchange_google_token(
        self, code: str, redirect_uri: str
    ) -> Dict[str, Any]:
        """
        Exchange the code for a Google token.

        Args:
            code: Code from Google

        Returns:
            Dict[str, Any]: Google token
        """
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://oauth2.googleapis.com/token",
                data={
                    "client_id": settings.GOOGLE_CLIENT_ID,
                    "client_secret": settings.GOOGLE_CLIENT_SECRET,
                    "code": code,
                    "grant_type": "authorization_code",
                    "redirect_uri": redirect_uri,
                },
            )

            if response.status_code != 200:
                raise OAuthError(f"Token exchange failed: {response.text}")

            return response.json()

    async def _exchange_github_token(
        self, code: str, redirect_uri: str
    ) -> Dict[str, Any]:
        """Exchange GitHub authorization code for token"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://github.com/login/oauth/access_token",
                data={
                    "client_id": settings.GITHUB_CLIENT_ID,
                    "client_secret": settings.GITHUB_CLIENT_SECRET,
                    "code": code,
                    "redirect_uri": redirect_uri,
                },
                headers={"Accept": "application/json"},
            )

            if response.status_code != 200:
                raise OAuthError(f"Token exchange failed: {response.text}")

            return response.json()

    async def get_user_info(self, provider: str, access_token: str) -> Dict[str, Any]:
        """
        Get user information from OAuth provider

        Args:
            provider: 'google' or 'github'
            access_token: Access token from OAuth exchange

        Returns:
            User information from provider
        """
        if provider == Provider.GOOGLE.value:
            return await self._get_google_user_info(access_token)
        elif provider == Provider.GITHUB.value:
            return await self._get_github_user_info(access_token)
        else:
            raise ValueError(f"Unsupported OAuth provider: {provider}")

    async def _get_google_user_info(self, access_token: str) -> Dict[str, Any]:
        """Get user info from Google"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://www.googleapis.com/oauth2/v2/userinfo",
                headers={"Authorization": f"Bearer {access_token}"},
            )

            if response.status_code != 200:
                raise OAuthError(f"Failed to get user info: {response.text}")

            return response.json()

    async def _get_github_user_info(self, access_token: str) -> Dict[str, Any]:
        """Get user info from GitHub"""
        async with httpx.AsyncClient() as client:
            # Get basic user info
            user_response = await client.get(
                "https://api.github.com/user",
                headers={"Authorization": f"token {access_token}"},
            )

            if user_response.status_code != 200:
                raise OAuthError(f"Failed to get user info: {user_response.text}")

            user_data = user_response.json()

            # Get user emails (GitHub may not return email in basic info)
            emails_response = await client.get(
                "https://api.github.com/user/emails",
                headers={"Authorization": f"token {access_token}"},
            )

            if emails_response.status_code == 200:
                emails = emails_response.json()
                # Find primary email
                primary_email = next(
                    (email["email"] for email in emails if email["primary"]), None
                )
                if primary_email:
                    user_data["email"] = primary_email

            return user_data
