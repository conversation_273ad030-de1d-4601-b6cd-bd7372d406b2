"""
Service for agent operations
"""
import json
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.agent import Agent
from aiplanet_platform.models.tool import Tool


class AgentService:
    """Service for agent operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = Agent

    def fetch_resource_by_id(self, resource_id: UUID) -> Optional[Agent]:
        """
        Fetch a agent by ID.

        Args:
            resource_id: ID of the agent

        Returns:
            Agent object if found, None otherwise
        """
        try:
            agent = (
                self.db.query(Agent)
                .filter(Agent.id == resource_id, Agent.is_deleted.is_(False))
                .options(joinedload(Agent.tools))
                .first()
            )

            agent.tool_ids = [tool.id for tool in agent.tools if not tool.is_deleted]
            return agent
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
    ) -> List[Agent]:
        """
        Fetch agents by filters.
        """
        try:
            query = (
                self.db.query(Agent)
                .filter(Agent.is_deleted.is_(False))
                .options(joinedload(Agent.tools))
            )

            # Apply filters
            for key, value in filters.items():
                if key == "component":
                    # Handle JSON structure filtering
                    value = json.loads(value) if isinstance(value, str) else value
                    for k, v in value.items():
                        # Handle nested JSON
                        if isinstance(v, dict):
                            for sub_k, sub_v in v.items():
                                query = query.filter(
                                    Agent.component[k].op("->>")(sub_k) == str(sub_v)
                                )
                        else:
                            query = query.filter(Agent.component.op("->>")(k) == str(v))
                    continue

                # Handle special filters for name (stored in component.label)
                if key == "name":
                    # Name is stored in component.label
                    query = query.filter(Agent.component.op("->>")("label") == value)
                elif key == "name_like":
                    # Name is stored in component.label, use ilike for partial matching
                    query = query.filter(
                        Agent.component.op("->>")("label").ilike(f"%{value}%")
                    )
                elif key == "is_active":
                    # For agents, is_active means not deleted (agents don't have deployment status)
                    if value:
                        query = query.filter(Agent.is_deleted.is_(False))
                    else:
                        query = query.filter(Agent.is_deleted.is_(True))
                elif hasattr(Agent, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]
                        query = query.filter(
                            getattr(Agent, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Agent, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Agent, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Agent, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Agent, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Agent, field_name).in_(value))
                    else:
                        query = query.filter(getattr(Agent, key) == value)

            # Apply sorting
            if sort_by and hasattr(Agent, sort_by):
                order_field = getattr(Agent, sort_by)
                if sort_order.lower() == "desc":
                    order_field = order_field.desc()
                query = query.order_by(order_field)

            agents = query.offset(skip).limit(limit).all()
            for agent in agents:
                agent.tool_ids = [
                    tool.id for tool in agent.tools if not tool.is_deleted
                ]
            return agents

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def count_resources(self, filters: Dict[str, Any]) -> int:
        """
        Count agents by filters (for pagination).
        """
        try:
            query = self.db.query(Agent).filter(Agent.is_deleted.is_(False))

            # Apply the same filters as in fetch_resource_by_filters
            for key, value in filters.items():
                if key == "component":
                    # Handle JSON structure filtering
                    value = json.loads(value) if isinstance(value, str) else value
                    for k, v in value.items():
                        # Handle nested JSON
                        if isinstance(v, dict):
                            for sub_k, sub_v in v.items():
                                query = query.filter(
                                    Agent.component[k].op("->>")(sub_k) == str(sub_v)
                                )
                        else:
                            query = query.filter(Agent.component.op("->>")(k) == str(v))
                    continue

                # Handle special filters for name (stored in component.label)
                if key == "name":
                    # Name is stored in component.label
                    query = query.filter(Agent.component.op("->>")("label") == value)
                elif key == "name_like":
                    # Name is stored in component.label, use ilike for partial matching
                    query = query.filter(
                        Agent.component.op("->>")("label").ilike(f"%{value}%")
                    )
                elif key == "is_active":
                    # For agents, is_active means not deleted (agents don't have deployment status)
                    if value:
                        query = query.filter(Agent.is_deleted.is_(False))
                    else:
                        query = query.filter(Agent.is_deleted.is_(True))
                elif hasattr(Agent, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]
                        query = query.filter(
                            getattr(Agent, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Agent, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Agent, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Agent, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Agent, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Agent, field_name).in_(value))
                    else:
                        query = query.filter(getattr(Agent, key) == value)

            return query.count()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_resource(self, data: Dict[str, Any], tool_ids: List[UUID]) -> Agent:
        """
        Create a new agent.

        Args:
            data: Dictionary of agent data

        Returns:
            Created Agent object
        """
        try:
            resource = Agent(**data)
            self.db.add(resource)
            self.db.flush()

            # Associate tools if provided
            if tool_ids:
                tools = (
                    self.db.query(Tool)
                    .filter(
                        Tool.id.in_(tool_ids),
                        Tool.organization_id == data.get("organization_id"),
                    )
                    .all()
                )

                if len(tools) != len(tool_ids):
                    raise ValueError("Some tools not found or not accessible")

                resource.tools.extend(tools)

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self,
        resource_id: UUID,
        data: Dict[str, Any],
        tool_ids: Optional[List[UUID]] = None,
    ) -> Optional[Agent]:
        """
        Update a agent by ID.

        Args:
            resource_id: ID of the agent
            data: Dictionary of data to update

        Returns:
            Updated Agent object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return None

            for key, value in data.items():
                if hasattr(resource, key):
                    setattr(resource, key, value)

            # Update tool associations if provided
            if tool_ids is not None:
                # Clear existing tools
                resource.tools.clear()

                # Add new tools
                if tool_ids:
                    tools = (
                        self.db.query(Tool)
                        .filter(
                            Tool.id.in_(tool_ids),
                            Tool.organization_id == resource.organization_id,
                        )
                        .all()
                    )

                    resource.tools.extend(tools)

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[Agent]:
        """
        Update agents by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated Agent objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a agent by ID.

        Args:
            resource_id: ID of the agent

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete agents by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a agent by ID.

        Args:
            resource_id: ID of the agent

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.db.query(Agent).filter(Agent.id == resource_id).first()

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def get_agents_by_tool_ids(self, tool_ids: List[UUID]) -> List[Agent]:
        """
        Get agents by tool IDs.

        Args:
            tool_ids: List of tool IDs

        Returns:
            List of agents
        """
        try:
            agents = (
                self.db.query(Agent)
                .filter(Agent.is_deleted.is_(False))
                .filter(Agent.tools.any(Tool.id.in_(tool_ids)))
                .all()
            )
            return agents
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e
