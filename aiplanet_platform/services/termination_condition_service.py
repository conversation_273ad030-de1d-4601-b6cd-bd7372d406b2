"""
Service for termination condition operations
"""
import json
import uuid
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.termination_condition import TerminationCondition
from aiplanet_platform.services.team_service import TeamService

# Default termination conditions for new organizations
DEFAULT_TERMINATION_CONDITIONS = [
    {
        "provider": "autogen_agentchat.conditions.MaxMessageTermination",
        "component_type": "termination",
        "version": 1,
        "component_version": 1,
        "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
        "label": "MaxMessageTermination",
        "config": {"max_messages": 10, "include_agent_event": False},
    },
    {
        "provider": "autogen_agentchat.conditions.TextMentionTermination",
        "component_type": "termination",
        "version": 1,
        "component_version": 1,
        "description": "Terminate the conversation when a specific text is mentioned.",
        "label": "TextMentionTermination",
        "config": {"text": "TERMINATE", "case_sensitive": False},
    },
    {
        "provider": "autogen_agentchat.conditions.TokenUsageTermination",
        "component_type": "termination",
        "version": 1,
        "component_version": 1,
        "description": "Terminate the conversation when token usage exceeds a threshold.",
        "label": "TokenUsageTermination",
        "config": {
            "max_total_token": 1000,
            "max_prompt_token": 500,
            "max_completion_token": 500,
        },
    },
    {
        "provider": "autogen_agentchat.conditions.TimeoutTermination",
        "component_type": "termination",
        "version": 1,
        "component_version": 1,
        "description": "Terminate the conversation after a timeout period.",
        "label": "TimeoutTermination",
        "config": {"timeout": 300},
    },
]


class TerminationConditionService:
    """Service for termination_condition operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = TerminationCondition
        self.team_service = TeamService(db)

    def fetch_resource_by_id(self, resource_id: UUID) -> Optional[TerminationCondition]:
        """
        Fetch a termination_condition by ID.

        Args:
            resource_id: ID of the termination_condition

        Returns:
            TerminationCondition object if found, None otherwise
        """
        try:
            return (
                self.db.query(TerminationCondition)
                .filter(
                    TerminationCondition.id == resource_id,
                    TerminationCondition.is_deleted.is_(False),
                )
                .first()
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
    ) -> List[TerminationCondition]:
        """
        Fetch termination_conditions by filters.

        Args:
            filters: Dictionary of filters to apply
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)

        Returns:
            List of TerminationCondition objects
        """
        try:
            query = self.db.query(TerminationCondition).filter(
                TerminationCondition.is_deleted.is_(False)
            )

            # Apply filters
            for key, value in filters.items():
                if key == "name_like":
                    query = query.filter(
                        TerminationCondition.component.op("->>")("label").ilike(
                            f"%{value}%"
                        )
                    )
                elif hasattr(TerminationCondition, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]  # Remove '_like' suffix
                        query = query.filter(
                            getattr(TerminationCondition, field_name).ilike(
                                f"%{value}%"
                            )
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]  # Remove '_gt' suffix
                        query = query.filter(
                            getattr(TerminationCondition, field_name) > value
                        )
                    elif key.endswith("_lt"):
                        field_name = key[:-3]  # Remove '_lt' suffix
                        query = query.filter(
                            getattr(TerminationCondition, field_name) < value
                        )
                    elif key.endswith("_gte"):
                        field_name = key[:-4]  # Remove '_gte' suffix
                        query = query.filter(
                            getattr(TerminationCondition, field_name) >= value
                        )
                    elif key.endswith("_lte"):
                        field_name = key[:-4]  # Remove '_lte' suffix
                        query = query.filter(
                            getattr(TerminationCondition, field_name) <= value
                        )
                    elif key.endswith("_in"):
                        field_name = key[:-3]  # Remove '_in' suffix
                        query = query.filter(
                            getattr(TerminationCondition, field_name).in_(value)
                        )
                    else:
                        query = query.filter(
                            getattr(TerminationCondition, key) == value
                        )

            # Apply sorting
            if sort_by and hasattr(TerminationCondition, sort_by):
                order_field = getattr(TerminationCondition, sort_by)
                if sort_order.lower() == "desc":
                    order_field = order_field.desc()
                query = query.order_by(order_field)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            return query.all()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_resource(self, data: Dict[str, Any]) -> TerminationCondition:
        """
        Create a new termination_condition.

        Args:
            data: Dictionary of termination_condition data

        Returns:
            Created TerminationCondition object
        """
        try:
            resource = TerminationCondition(**data)
            self.db.add(resource)
            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self, resource_id: UUID, data: Dict[str, Any]
    ) -> Optional[TerminationCondition]:
        """
        Update a termination_condition by ID.

        Args:
            resource_id: ID of the termination_condition
            data: Dictionary of data to update

        Returns:
            Updated TerminationCondition object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return None

            for key, value in data.items():
                if hasattr(resource, key):
                    setattr(resource, key, value)

            teams = self.team_service.fetch_resource_by_filters(
                {"team_termination_conditions": {"id": resource_id}}
            )
            for team in teams:
                if team.is_deployed:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Cannot update termination_condition. Used in deployed workflow.",
                    )

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            if isinstance(e, HTTPException):
                raise e

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[TerminationCondition]:
        """
        Update termination_conditions by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated TerminationCondition objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a termination_condition by ID.

        Args:
            resource_id: ID of the termination_condition

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete termination_conditions by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a termination_condition by ID.

        Args:
            resource_id: ID of the termination_condition

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = (
                self.db.query(TerminationCondition)
                .filter(TerminationCondition.id == resource_id)
                .first()
            )

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_default_termination_conditions_for_organization(
        self, organization_id: UUID
    ) -> List[TerminationCondition]:
        """
        Create default termination conditions for a new organization.

        Args:
            organization_id: ID of the organization to create termination conditions for

        Returns:
            List of created TerminationCondition objects
        """
        try:
            created_conditions = []

            for component_data in DEFAULT_TERMINATION_CONDITIONS:
                # Check if this termination condition already exists for this organization
                existing = (
                    self.db.query(TerminationCondition)
                    .filter(
                        TerminationCondition.component["provider"].astext
                        == component_data["provider"],
                        TerminationCondition.component["component_type"].astext
                        == component_data["component_type"],
                        TerminationCondition.component["label"].astext
                        == component_data["label"],
                        TerminationCondition.organization_id == organization_id,
                        TerminationCondition.is_deleted.is_(False),
                    )
                    .first()
                )

                if existing:
                    print(
                        f"Termination condition already exists for organization {organization_id}: {component_data['label']}"
                    )
                    continue

                # Create new termination condition
                termination_condition = TerminationCondition(
                    id=uuid.uuid4(),
                    component=component_data,
                    organization_id=organization_id,
                    is_deleted=False,
                )

                self.db.add(termination_condition)
                created_conditions.append(termination_condition)
                print(
                    f"Created default termination condition for organization {organization_id}: {component_data['label']}"
                )

            self.db.commit()

            # Refresh all created conditions
            for condition in created_conditions:
                self.db.refresh(condition)

            return created_conditions

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating default termination conditions: {str(e)}",
            ) from e

    def count_resources(self, filters: Dict[str, Any]) -> int:
        """
        Count agents by filters (for pagination).
        """
        try:
            query = self.db.query(TerminationCondition).filter(
                TerminationCondition.is_deleted.is_(False)
            )

            # Apply the same filters as in fetch_resource_by_filters
            for key, value in filters.items():
                if key == "component":
                    # Handle JSON structure filtering
                    value = json.loads(value) if isinstance(value, str) else value
                    for k, v in value.items():
                        # Handle nested JSON
                        if isinstance(v, dict):
                            for sub_k, sub_v in v.items():
                                query = query.filter(
                                    TerminationCondition.component[k].op("->>")(sub_k)
                                    == str(sub_v)
                                )
                        else:
                            query = query.filter(
                                TerminationCondition.component.op("->>")(k) == str(v)
                            )
                    continue

                # Handle special filters for name (stored in component.label)
                if key == "name":
                    # Name is stored in component.label
                    query = query.filter(
                        TerminationCondition.component.op("->>")("label") == value
                    )
                elif key == "name_like":
                    # Name is stored in component.label, use ilike for partial matching
                    query = query.filter(
                        TerminationCondition.component.op("->>")("label").ilike(
                            f"%{value}%"
                        )
                    )
                elif key == "is_active":
                    # For agents, is_active means not deleted (agents don't have deployment status)
                    if value:
                        query = query.filter(TerminationCondition.is_deleted.is_(False))
                    else:
                        query = query.filter(TerminationCondition.is_deleted.is_(True))
                elif hasattr(TerminationCondition, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]
                        query = query.filter(
                            getattr(TerminationCondition, field_name).ilike(
                                f"%{value}%"
                            )
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]
                        query = query.filter(
                            getattr(TerminationCondition, field_name) > value
                        )
                    elif key.endswith("_lt"):
                        field_name = key[:-3]
                        query = query.filter(
                            getattr(TerminationCondition, field_name) < value
                        )
                    elif key.endswith("_gte"):
                        field_name = key[:-4]
                        query = query.filter(
                            getattr(TerminationCondition, field_name) >= value
                        )
                    elif key.endswith("_lte"):
                        field_name = key[:-4]
                        query = query.filter(
                            getattr(TerminationCondition, field_name) <= value
                        )
                    elif key.endswith("_in"):
                        field_name = key[:-3]
                        query = query.filter(
                            getattr(TerminationCondition, field_name).in_(value)
                        )
                    else:
                        query = query.filter(
                            getattr(TerminationCondition, key) == value
                        )

            return query.count()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e
