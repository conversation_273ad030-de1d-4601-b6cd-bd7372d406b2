"""
Service for settings operations
"""
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from sqlalchemy.orm import Session
from fastapi import Depends, HTTPException, status
from typing import List, Dict, Any, Optional
from uuid import UUID

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.settings import Settings
from aiplanet_platform.core.config import get_settings

settings = get_settings()


class SettingsService:
    """Service for settings operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = Settings
        # Initialize encryption key from application secret key
        self.fernet = self._get_fernet_instance()

    def _get_fernet_instance(self) -> Fernet:
        """
        Create a Fernet instance for encryption/decryption using the app's secret key.

        Returns:
            Fernet instance
        """
        # Use PBKDF2 to derive a key from the application secret key
        salt = b"aiplanet_settings_salt"  # Fixed salt for consistency
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(settings.SECRET_KEY.encode()))
        return Fernet(key)

    def encrypt_value(self, value: str) -> str:
        """
        Encrypt a value using Fernet symmetric encryption.

        Args:
            value: Plain text value to encrypt

        Returns:
            Encrypted value as a string
        """
        if not value:
            return value

        # Prefix to identify encrypted values
        prefix = "enc:"

        # Check if already encrypted
        if value.startswith(prefix):
            return value

        # Encrypt the value
        encrypted_bytes = self.fernet.encrypt(value.encode())
        encrypted_str = base64.urlsafe_b64encode(encrypted_bytes).decode()

        # Return with prefix
        return f"{prefix}{encrypted_str}"

    def decrypt_value(self, value: str) -> str:
        """
        Decrypt a value that was encrypted with Fernet.

        Args:
            value: Encrypted value

        Returns:
            Decrypted value as a string
        """
        if not value or not isinstance(value, str):
            return value

        # Check for encryption prefix
        prefix = "enc:"
        if not value.startswith(prefix):
            return value

        try:
            # Remove prefix and decode
            encrypted_str = value[len(prefix) :]
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_str)

            # Decrypt
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception:
            # If decryption fails, return masked value
            return "********"

    def encrypt_secret_values(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Encrypt environment variable values with type 'secret'.

        Args:
            config: Settings configuration dictionary

        Returns:
            Settings configuration with encrypted secret values
        """
        if not config or "environment" not in config:
            return config

        for env_var in config["environment"]:
            if (
                isinstance(env_var, dict)
                and env_var.get("type") == "secret"
                and "value" in env_var
            ):
                env_var["value"] = self.encrypt_value(env_var["value"])

        return config

    def decrypt_secret_values(
        self, config: Dict[str, Any], mask_secrets: bool = True
    ) -> Dict[str, Any]:
        """
        Decrypt secret values in the configuration.
        If mask_secrets is True, replace with asterisks for API responses.

        Args:
            config: Settings configuration dictionary
            mask_secrets: Whether to mask secret values with asterisks

        Returns:
            Settings configuration with decrypted or masked secret values
        """
        if not config or "environment" not in config:
            return config

        for env_var in config["environment"]:
            if (
                isinstance(env_var, dict)
                and env_var.get("type") == "secret"
                and "value" in env_var
            ):
                if mask_secrets:
                    # For API responses, mask the value
                    env_var["value"] = "********"
                else:
                    # For internal use, decrypt the value
                    env_var["value"] = self.decrypt_value(env_var["value"])

        return config

    def get_decrypted_environment_variables(
        self, user_id: UUID
    ) -> List[Dict[str, Any]]:
        """
        Get decrypted environment variables for a user.
        This is for internal use by other services that need the actual values.

        Args:
            user_id: User ID

        Returns:
            List of environment variables with decrypted values
        """
        settings_list = self.fetch_resource_by_filters(filters={"user_id": user_id})

        if not settings_list:
            return []

        settings_obj = settings_list[0]

        if not settings_obj.config or "environment" not in settings_obj.config:
            return []

        # Make a deep copy to avoid modifying the cached object
        import copy

        config_copy = copy.deepcopy(settings_obj.config)

        # Decrypt without masking
        decrypted_config = self.decrypt_secret_values(config_copy, mask_secrets=False)

        return decrypted_config.get("environment", [])

    def create_resource(self, data: Dict[str, Any]) -> Settings:
        """
        Create a new settings resource.

        Args:
            data: Dictionary of data to create the resource with

        Returns:
            Created Settings object
        """
        try:
            # Encrypt secret values before storing
            if "config" in data:
                data["config"] = self.encrypt_secret_values(data["config"])

            resource = self.model(**data)
            self.db.add(resource)
            self.db.commit()
            self.db.refresh(resource)

            # Mask secret values in the response
            if resource.config:
                resource.config = self.decrypt_secret_values(
                    resource.config, mask_secrets=True
                )

            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self, resource_id: UUID, data: Dict[str, Any]
    ) -> Optional[Settings]:
        """
        Update a settings by ID.

        Args:
            resource_id: ID of the settings
            data: Dictionary of data to update

        Returns:
            Updated Settings object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id, mask_secrets=False)
            if not resource:
                return None

            # Encrypt secret values before storing
            if "config" in data:
                data["config"] = self.encrypt_secret_values(data["config"])

            for key, value in data.items():
                if hasattr(resource, key):
                    # Special handling for config field to ensure proper merging
                    if key == "config" and isinstance(value, dict):
                        current_config = resource.config or {}
                        # Deep merge the configs
                        for config_key, config_value in value.items():
                            # Handle nested dictionaries properly
                            if (
                                config_key in current_config
                                and isinstance(current_config[config_key], dict)
                                and isinstance(config_value, dict)
                            ):
                                # Merge nested dictionaries
                                for nested_key, nested_value in config_value.items():
                                    current_config[config_key][
                                        nested_key
                                    ] = nested_value
                            else:
                                # Direct assignment for non-dict values or new keys
                                current_config[config_key] = config_value
                        setattr(resource, key, current_config)
                    else:
                        setattr(resource, key, value)

            self.db.commit()
            self.db.refresh(resource)

            # Mask secret values in the response
            if resource.config:
                resource.config = self.decrypt_secret_values(
                    resource.config, mask_secrets=True
                )

            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_id(
        self, resource_id: UUID, mask_secrets: bool = True
    ) -> Optional[Settings]:
        """
        Fetch a settings by ID.

        Args:
            resource_id: ID of the settings
            mask_secrets: Whether to mask secret values with asterisks

        Returns:
            Settings object if found, None otherwise
        """
        try:
            resource = (
                self.db.query(Settings).filter(Settings.id == resource_id).first()
            )

            # Mask secret values in the response
            if resource and resource.config:
                resource.config = self.decrypt_secret_values(
                    resource.config, mask_secrets=mask_secrets
                )

            return resource
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        mask_secrets: bool = True,
    ) -> List[Settings]:
        """
        Fetch settings by filters.

        Args:
            filters: Dictionary of filters to apply
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order ("asc" or "desc")
            mask_secrets: Whether to mask secret values with asterisks

        Returns:
            List of Settings objects
        """
        try:
            query = self.db.query(Settings)

            # Apply filters
            for key, value in filters.items():
                if hasattr(Settings, key):
                    query = query.filter(getattr(Settings, key) == value)

            # Apply sorting
            if hasattr(Settings, sort_by):
                if sort_order.lower() == "desc":
                    query = query.order_by(getattr(Settings, sort_by).desc())
                else:
                    query = query.order_by(getattr(Settings, sort_by).asc())

            # Apply pagination
            query = query.offset(skip).limit(limit)

            resources = query.all()

            # Mask secret values in the response
            for resource in resources:
                if resource.config:
                    resource.config = self.decrypt_secret_values(
                        resource.config, mask_secrets=mask_secrets
                    )

            return resources
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[Settings]:
        """
        Update settingss by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated Settings objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a settings by ID.

        Args:
            resource_id: ID of the settings

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete settingss by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a settings by ID.

        Args:
            resource_id: ID of the settings

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = (
                self.db.query(Settings).filter(Settings.id == resource_id).first()
            )

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e
