"""
Service for team template operations
"""

import copy
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.team_template import TeamTemplate
from aiplanet_platform.models.team import Team

from aiplanet_platform.services.team_service import TeamService
from aiplanet_platform.services.agent_service import AgentService
from aiplanet_platform.services.model_service import ModelService
from aiplanet_platform.services.tool_service import ToolService
from aiplanet_platform.services.input_service import InputService
from aiplanet_platform.services.termination_condition_service import (
    TerminationConditionService,
)


class TeamTemplateService:
    """Service for team template operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = TeamTemplate

    def fetch_template_by_id(self, template_id: UUID) -> Optional[TeamTemplate]:
        """
        Fetch a team template by ID.

        Args:
            template_id: ID of the template

        Returns:
            TeamTemplate object if found, None otherwise
        """
        try:
            return (
                self.db.query(TeamTemplate)
                .filter(
                    TeamTemplate.id == template_id, TeamTemplate.is_deleted.is_(False)
                )
                .first()
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_templates_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[List[str]] = None,
        sort_order: str = "asc",
    ) -> List[TeamTemplate]:
        """
        Fetch team templates by filters.

        Args:
            filters: Dictionary of filters to apply
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)

        Returns:
            List of TeamTemplate objects
        """
        try:
            query = self.db.query(TeamTemplate).filter(
                TeamTemplate.is_deleted.is_(False)
            )

            # Apply filters
            for key, value in filters.items():
                if key == "organization_id":
                    if value is None:
                        # For system templates (organization_id is null)
                        query = query.filter(TeamTemplate.organization_id.is_(None))
                    else:
                        # For organization-specific templates or both (None means show all)
                        query = query.filter(
                            (TeamTemplate.organization_id == value)
                            | (TeamTemplate.organization_id.is_(None))
                        )
                elif key == "category":
                    query = query.filter(TeamTemplate.category == value)
                elif key == "name_like":
                    query = query.filter(TeamTemplate.name.ilike(f"%{value}%"))
                elif key == "tags":
                    # Filter templates that have any of the specified tags
                    if isinstance(value, list):
                        for tag in value:
                            query = query.filter(TeamTemplate.tags.contains([tag]))
                    else:
                        query = query.filter(TeamTemplate.tags.contains([value]))
                elif hasattr(TeamTemplate, key):
                    query = query.filter(getattr(TeamTemplate, key) == value)

            # Apply sorting
            if sort_by:
                order_fields = []
                for field in sort_by:
                    if hasattr(TeamTemplate, field):
                        order_field = getattr(TeamTemplate, field)
                        if sort_order.lower() == "desc":
                            order_field = order_field.desc()
                        order_fields.append(order_field)

                if order_fields:
                    query = query.order_by(*order_fields)

            return query.offset(skip).limit(limit).all()

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def count_templates(self, filters: Dict[str, Any] = None) -> int:
        """
        Count team templates matching the given filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Total count of templates matching the filters
        """
        try:
            query = self.db.query(TeamTemplate).filter(
                TeamTemplate.is_deleted.is_(False)
            )

            if filters:
                # Apply the same filters as in fetch_templates_by_filters
                for key, value in filters.items():
                    if key == "organization_id":
                        if value is None:
                            query = query.filter(TeamTemplate.organization_id.is_(None))
                        else:
                            query = query.filter(
                                (TeamTemplate.organization_id == value)
                                | (TeamTemplate.organization_id.is_(None))
                            )
                    elif key == "category":
                        query = query.filter(TeamTemplate.category == value)
                    elif key == "name_like":
                        query = query.filter(TeamTemplate.name.ilike(f"%{value}%"))
                    elif key == "tags":
                        if isinstance(value, list):
                            for tag in value:
                                query = query.filter(TeamTemplate.tags.contains([tag]))
                        else:
                            query = query.filter(TeamTemplate.tags.contains([value]))
                    elif hasattr(TeamTemplate, key):
                        query = query.filter(getattr(TeamTemplate, key) == value)

            return query.count()

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_template(self, data: Dict[str, Any]) -> TeamTemplate:
        """
        Create a new team template.

        Args:
            data: Dictionary of template data

        Returns:
            Created TeamTemplate object
        """
        try:
            template = TeamTemplate(**data)
            self.db.add(template)
            self.db.commit()
            self.db.refresh(template)
            return template
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_template_by_id(
        self, template_id: UUID, data: Dict[str, Any]
    ) -> Optional[TeamTemplate]:
        """
        Update a team template by ID.

        Args:
            template_id: ID of the template
            data: Dictionary of data to update

        Returns:
            Updated TeamTemplate object if found, None otherwise
        """
        try:
            template = self.fetch_template_by_id(template_id)
            if not template:
                return None

            # Update basic fields
            for key, value in data.items():
                if hasattr(template, key) and value is not None:
                    setattr(template, key, value)

            self.db.commit()
            self.db.refresh(template)
            return template
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def delete_template_by_id(self, template_id: UUID) -> bool:
        """
        Soft delete a team template by ID.

        Args:
            template_id: ID of the template

        Returns:
            True if deleted, False otherwise
        """
        try:
            template = self.fetch_template_by_id(template_id)
            if not template:
                return False

            template.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_team_from_template(
        self,
        template_id: UUID,
        organization_id: UUID,
        name_override: str = None,
        customizations: dict = None,
    ) -> Team:
        """
        Create a new team from a template.

        Process:
        1. Fetch template
        2. Apply customizations to template_config
        3. Create all components (models, tools, agents, termination conditions)
        4. Build team with proper relationships

        Args:
            template_id: ID of the template to use
            organization_id: Organization ID for the new team
            name_override: Optional name override for the team
            customizations: Optional customizations to apply to the template

        Returns:
            Created Team object
        """
        try:
            # 1. Get template
            template = self.fetch_template_by_id(template_id)
            if not template:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
                )

            # 2. Start with template config (extract team from nested structure)
            team_config = copy.deepcopy(template.template_config.get("team", {}))

            # 3. Apply customizations if provided
            if customizations:
                team_config = self._apply_customizations(team_config, customizations)

            # 4. Override name if provided
            if name_override:
                team_config["label"] = name_override

            # 5. Create all the database entities
            created_entities = self._process_template_and_create_entities(
                team_config, organization_id, template
            )

            # 6. Create the team with relationships
            team_data = {
                "component": team_config,
                "organization_id": organization_id,
                "model_id": created_entities.get("model_id"),
            }

            team_service = TeamService(self.db)
            return team_service.create_resource(
                team_data,
                team_agent_ids=created_entities["agent_ids"],
                team_input_ids=created_entities.get("input_ids", []),
                team_output_ids=created_entities.get("output_ids", []),
                team_termination_condition_ids=created_entities[
                    "termination_condition_ids"
                ],
            )

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating team from template: {str(e)}",
            ) from e

    def _apply_customizations(self, config: dict, customizations: dict) -> dict:
        """
        Apply customizations to the template configuration.

        Args:
            config: Base configuration from template
            customizations: Customizations to apply

        Returns:
            Updated configuration
        """

        def deep_merge(base_dict, override_dict):
            """Recursively merge two dictionaries"""
            result = base_dict.copy()
            for key, value in override_dict.items():
                if (
                    key in result
                    and isinstance(result[key], dict)
                    and isinstance(value, dict)
                ):
                    result[key] = deep_merge(result[key], value)
                elif key == "participants" and isinstance(value, list):
                    # Special handling for participants list to preserve base participant structure
                    if "participants" not in result:
                        result["participants"] = []

                    # Ensure base participants list is long enough
                    while len(result["participants"]) < len(value):
                        result["participants"].append({})

                    # Merge each participant while preserving original structure
                    for i, participant_override in enumerate(value):
                        if i < len(result["participants"]) and isinstance(
                            participant_override, dict
                        ):
                            result["participants"][i] = deep_merge(
                                result["participants"][i], participant_override
                            )
                        else:
                            result["participants"].append(participant_override)
                else:
                    result[key] = value
            return result

        return deep_merge(config, customizations)

    def _process_template_and_create_entities(
        self, team_config: dict, organization_id: UUID, template=None
    ) -> dict:
        """
        Create all necessary database entities from the template config.
        Returns dict with created entity IDs.

        Args:
            team_config: Team configuration from template
            organization_id: Organization ID
            template: Template object containing input_config and output_config

        Returns:
            Dictionary containing lists of created entity IDs
        """
        created_entities = {
            "agent_ids": [],
            "input_ids": [],
            "output_ids": [],
            "termination_condition_ids": [],
            "model_id": None,
        }

        # Create termination condition
        if "termination_condition" in team_config["config"]:
            tc_service = TerminationConditionService(self.db)
            tc_data = {
                "component": team_config["config"]["termination_condition"],
                "organization_id": organization_id,
            }
            tc = tc_service.create_resource(tc_data)
            created_entities["termination_condition_ids"].append(tc.id)

        # Create team-level model if exists (for teams like SelectorGroupChat)
        if "model_client" in team_config["config"]:
            model_service = ModelService(self.db)
            model_data = {
                "component": team_config["config"]["model_client"],
                "organization_id": organization_id,
            }
            model = model_service.create_resource(model_data)
            if not created_entities["model_id"]:
                created_entities["model_id"] = model.id

        # Create agents and their dependencies
        for participant in team_config["config"]["participants"]:
            # Create model if exists
            model_id = None
            if "model_client" in participant["config"]:
                model_service = ModelService(self.db)
                model_data = {
                    "component": participant["config"]["model_client"],
                    "organization_id": organization_id,
                }
                model = model_service.create_resource(model_data)
                model_id = model.id

            # Create tools
            tool_ids = []
            if "tools" in participant["config"]:
                tool_service = ToolService(self.db)
                for tool_config in participant["config"]["tools"]:
                    tool_data = {
                        "component": tool_config,
                        "organization_id": organization_id,
                    }
                    tool = tool_service.create_resource(tool_data)
                    tool_ids.append(tool.id)

            # Create agent
            agent_service = AgentService(self.db)
            agent_data = {
                "component": participant,
                "organization_id": organization_id,
                "model_id": model_id,
            }

            # Debug logging to see what's being stored
            print(f"DEBUG: Creating agent with participant: {participant}")
            print(
                f"DEBUG: Participant label: {participant.get('label', 'NO LABEL FOUND')}"
            )
            print(f"DEBUG: Agent data component: {agent_data['component']}")

            agent = agent_service.create_resource(agent_data, tool_ids)
            created_entities["agent_ids"].append(agent.id)

            # Debug: Check what was actually stored
            print(f"DEBUG: Created agent component: {agent.component}")
            print(
                f"DEBUG: Created agent component label: {agent.component.get('label', 'NO LABEL IN STORED AGENT') if agent.component else 'NO COMPONENT'}"
            )

        # Create input components from template_config.inputs
        if template and template.template_config.get("inputs"):
            input_service = InputService(self.db)
            for input_config in template.template_config["inputs"]:
                input_data = {
                    "component": input_config,
                    "organization_id": organization_id,
                }
                input_obj = input_service.create_resource(input_data)
                created_entities["input_ids"].append(input_obj.id)

        # Create output components from template_config.outputs
        if template and template.template_config.get("output"):
            output_id = template.template_config.get("output")
            if isinstance(output_id, str):
                from uuid import UUID

                output_id = UUID(output_id)
            created_entities["output_ids"].append(output_id)

        return created_entities

    def analyze_template_api_requirements(self, template_id: UUID) -> Dict[str, Any]:
        """
        Analyze a template to identify required API keys and configurations.

        Args:
            template_id: ID of the template to analyze

        Returns:
            Dictionary containing required API key information
        """
        try:
            template = self.fetch_template_by_id(template_id)
            if not template:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
                )

            team_config = template.template_config.get("team", {})
            api_requirements = {
                "required_providers": [],
                "agent_requirements": [],
                "team_requirements": None,
            }

            # Analyze team-level model client
            if "model_client" in team_config.get("config", {}):
                team_model = team_config["config"]["model_client"]
                team_req = self._extract_api_key_requirements(
                    team_model, "team", "Team Model"
                )
                if team_req:
                    api_requirements["team_requirements"] = team_req
                    api_requirements["required_providers"].append(
                        team_req["provider_type"]
                    )

            # Analyze agent-level model clients
            for i, participant in enumerate(
                team_config.get("config", {}).get("participants", [])
            ):
                if "model_client" in participant.get("config", {}):
                    agent_model = participant["config"]["model_client"]
                    agent_name = participant.get("label", f"Agent {i+1}")
                    agent_req = self._extract_api_key_requirements(
                        agent_model, "agent", agent_name
                    )
                    if agent_req:
                        api_requirements["agent_requirements"].append(agent_req)
                        if (
                            agent_req["provider_type"]
                            not in api_requirements["required_providers"]
                        ):
                            api_requirements["required_providers"].append(
                                agent_req["provider_type"]
                            )

            return api_requirements

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error analyzing template: {str(e)}",
            ) from e

    def _extract_api_key_requirements(
        self, model_config: dict, context_type: str, context_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Extract API key requirements from a model configuration.

        Args:
            model_config: Model client configuration
            context_type: Type of context ('team' or 'agent')
            context_name: Name/label for display purposes

        Returns:
            Dictionary with API key requirements or None if no keys required
        """
        provider = model_config.get("provider", "")
        config = model_config.get("config", {})

        # Define exact provider mappings based on builder service response
        provider_mappings = {
            "autogen_ext.models.anthropic.AnthropicChatCompletionClient": {
                "provider_type": "anthropic",
                "display_name": "Anthropic",
                "required_fields": ["api_key", "model"],
                "optional_fields": ["max_tokens", "temperature"],
            },
            "autogen_ext.models.openai.OpenAIChatCompletionClient": {
                "provider_type": "openai",
                "display_name": "OpenAI",
                "required_fields": ["api_key", "model"],
                "optional_fields": ["max_tokens", "temperature"],
            },
            "autogen_ext.models.openai.AzureOpenAIChatCompletionClient": {
                "provider_type": "azure_openai",
                "display_name": "Azure OpenAI",
                "required_fields": [
                    "api_key",
                    "model",
                    "azure_endpoint",
                    "azure_deployment",
                    "api_version",
                ],
                "optional_fields": ["max_tokens", "temperature"],
            },
        }

        # Look up exact provider match
        provider_info = provider_mappings.get(provider)
        if not provider_info:
            return None

        # Check which fields are present and need user input
        missing_fields = []
        present_fields = []

        for field in provider_info["required_fields"]:
            if field in config:
                present_fields.append(field)
            else:
                missing_fields.append(field)

        for field in provider_info.get("optional_fields", []):
            if field in config:
                present_fields.append(field)

        return {
            "provider_type": provider_info["provider_type"],
            "provider_name": provider_info["display_name"],
            "context_type": context_type,
            "context_name": context_name,
            "required_fields": provider_info["required_fields"],
            "optional_fields": provider_info.get("optional_fields", []),
            "missing_fields": missing_fields,
            "present_fields": present_fields,
            "model": config.get("model", "Unknown Model"),
        }

    def generate_api_key_customizations(
        self, template_id: UUID, api_keys: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate customizations object with API key substitutions for a template.

        This method now supports two formats for api_keys:

        Format 1 - Simple (backward compatible): Apply same config to all models of same provider
        {
            "azure_openai": {"api_key": "...", "azure_endpoint": "..."},
            "anthropic": {"api_key": "...", "model": "..."}
        }

        Format 2 - Individual model customizations: Specify config for each model by label/path
        {
            "team_model_client": {"api_key": "...", "model": "gpt-4"},
            "participant_0_model_client": {"api_key": "...", "azure_endpoint": "..."},
            "participant_1_model_client": {"api_key": "...", "model": "claude-3"}
        }

        Args:
            template_id: ID of the template
            api_keys: Dictionary with API key configurations (supports both formats above)

        Returns:
            Customizations object that can be passed to create_team_from_template()
        """
        try:
            template = self.fetch_template_by_id(template_id)
            if not template:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Template not found"
                )

            team_config = template.template_config.get("team", {})
            customizations = {"config": {"participants": []}}

            # Detect if using new individual model format vs old provider-type format
            is_individual_format = any(
                key in ["team_model_client"] or key.startswith("participant_")
                for key in api_keys.keys()
            )

            # Handle team-level model client
            if "model_client" in team_config.get("config", {}):
                team_model = team_config["config"]["model_client"]

                if is_individual_format and "team_model_client" in api_keys:
                    # Individual model customization
                    team_customization = self._create_model_customization(
                        team_model, api_keys["team_model_client"]
                    )
                    if team_customization:
                        customizations["config"]["model_client"] = team_customization
                else:
                    # Legacy provider-type customization
                    provider_type = self._get_provider_type(
                        team_model.get("provider", "")
                    )
                    if provider_type and provider_type in api_keys:
                        team_customization = self._create_model_customization(
                            team_model, api_keys[provider_type]
                        )
                        if team_customization:
                            customizations["config"][
                                "model_client"
                            ] = team_customization

            # Handle agent-level model clients
            for i, participant in enumerate(
                team_config.get("config", {}).get("participants", [])
            ):
                if "model_client" in participant.get("config", {}):
                    agent_model = participant["config"]["model_client"]
                    participant_key = f"participant_{i}_model_client"

                    participant_customization = None

                    if is_individual_format and participant_key in api_keys:
                        # Individual model customization
                        participant_customization = self._create_model_customization(
                            agent_model, api_keys[participant_key]
                        )
                    else:
                        # Legacy provider-type customization
                        provider_type = self._get_provider_type(
                            agent_model.get("provider", "")
                        )
                        if provider_type and provider_type in api_keys:
                            participant_customization = (
                                self._create_model_customization(
                                    agent_model, api_keys[provider_type]
                                )
                            )

                    if participant_customization:
                        # Ensure we have participant entry
                        while len(customizations["config"]["participants"]) <= i:
                            customizations["config"]["participants"].append({})

                        customizations["config"]["participants"][i] = {
                            "config": {"model_client": participant_customization}
                        }

            return customizations

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error generating API key customizations: {str(e)}",
            ) from e

    def _create_model_customization(
        self, original_model_config: dict, new_api_config: dict
    ) -> dict:
        """
        Create a model customization that preserves the original structure while updating API configs.

        Args:
            original_model_config: Original model client configuration from template
            new_api_config: New API configuration to apply

        Returns:
            Complete model customization preserving structure
        """
        # Start with a copy of the original model config to preserve structure
        customization = copy.deepcopy(original_model_config)

        # Update the config section with new API configuration
        # Merge new API config with existing config, giving preference to new values
        if "config" not in customization:
            customization["config"] = {}

        for key, value in new_api_config.items():
            customization["config"][key] = value

        return customization

    def _get_provider_type(self, provider: str) -> Optional[str]:
        """
        Determine the provider type from the provider string.

        Args:
            provider: Provider class name/string

        Returns:
            Provider type identifier or None
        """
        # Define exact provider mappings
        provider_mappings = {
            "autogen_ext.models.anthropic.AnthropicChatCompletionClient": "anthropic",
            "autogen_ext.models.openai.OpenAIChatCompletionClient": "openai",
            "autogen_ext.models.openai.AzureOpenAIChatCompletionClient": "azure_openai",
        }

        return provider_mappings.get(provider)
