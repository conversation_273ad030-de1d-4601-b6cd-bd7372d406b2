"""
Service for tool operations
"""

import json
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.tool import Tool
from aiplanet_platform.services.agent_service import AgentService

# from autogen_ext.tools.http import HttpTool

# from autogen_ext.code_executors.docker import DockerCommandLineCodeExecutor
from aiplanet_platform.constants.default_tools import DEFAULT_TOOLS
import logging

logger = logging.getLogger(__name__)
# import pandas as pd


class ToolService:
    """Service for tool operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = Tool
        self.agent_service = AgentService(db)

    def bulk_add_tools(self, user_id, organization_id):
        """
        Add default tools to an organization.

        Args:
            user_id: ID of the user who owns the tools
            organization_id: ID of the organization to add tools to
        """
        # Import here to avoid circular imports

        # Process regular tools
        for tool_data in DEFAULT_TOOLS:
            # Create a deep copy to avoid modifying the original
            new_data = tool_data.copy()

            # Set the organization ID
            new_data["organization_id"] = organization_id

            # Get the component to check for existing tools
            component = new_data.get("component", {})

            # Try to get the name from the component's config
            tool_name = ""
            if (
                isinstance(component, dict)
                and "config" in component
                and "name" in component["config"]
            ):
                tool_name = component["config"]["name"]

            # Check if a similar tool exists
            existing = self.fetch_resource_by_filters(
                {"organization_id": organization_id}
            )

            # Skip if a similar tool exists with the same name
            tool_exists = False
            for existing_tool in existing:
                if (
                    hasattr(existing_tool, "component")
                    and hasattr(existing_tool.component, "config")
                    and hasattr(existing_tool.component.config, "name")
                    and existing_tool.component.config.name == tool_name
                ):
                    tool_exists = True
                    logger.info(f"Tool '{tool_name}' already exists, skipping")
                    break

            if not tool_exists:
                logger.info(f"Creating new default tool '{tool_name}'")
                self.create_resource(new_data)

    def fetch_resource_by_id(self, resource_id: UUID) -> Optional[Tool]:
        """
        Fetch a tool by ID.

        Args:
            resource_id: ID of the tool

        Returns:
            Tool object if found, None otherwise
        """
        try:
            return (
                self.db.query(Tool)
                .filter(Tool.id == resource_id, Tool.is_deleted.is_(False))
                .first()
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
    ) -> List[Tool]:
        """
        Fetch models by filters.
        """
        try:
            query = self.db.query(Tool).filter(Tool.is_deleted.is_(False))

            # Apply filters
            for key, value in filters.items():
                if key == "component":
                    # Handle JSON structure filtering
                    value = json.loads(value) if isinstance(value, str) else value
                    for k, v in value.items():
                        # Handle nested JSON
                        if isinstance(v, dict):
                            for sub_k, sub_v in v.items():
                                query = query.filter(
                                    Tool.component[k].op("->>")(sub_k) == str(sub_v)
                                )
                        else:
                            query = query.filter(Tool.component.op("->>")(k) == str(v))
                    continue

                if key == "name_like":
                    query = query.filter(
                        Tool.component.op("->>")("label").ilike(f"%{value}%")
                    )
                elif hasattr(Tool, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]
                        query = query.filter(
                            getattr(Tool, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Tool, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Tool, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Tool, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Tool, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Tool, field_name).in_(value))
                    else:
                        query = query.filter(getattr(Tool, key) == value)

            # Apply sorting
            if sort_by and hasattr(Tool, sort_by):
                order_field = getattr(Tool, sort_by)
                if sort_order.lower() == "desc":
                    order_field = order_field.desc()
                query = query.order_by(order_field)

            return query.offset(skip).limit(limit).all()

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_resource(self, data: Dict[str, Any]) -> Tool:
        """
        Create a new tool.

        Args:
            data: Dictionary of tool data

        Returns:
            Created Tool object
        """
        try:
            resource = Tool(**data)
            self.db.add(resource)
            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self, resource_id: UUID, data: Dict[str, Any]
    ) -> Optional[Tool]:
        """
        Update a tool by ID.

        Args:
            resource_id: ID of the tool
            data: Dictionary of data to update

        Returns:
            Updated Tool object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return None

            for key, value in data.items():
                if hasattr(resource, key):
                    setattr(resource, key, value)

            agents = self.agent_service.get_agents_by_tool_ids([resource_id])
            for agent in agents:
                if agent.teams:
                    for team in agent.teams:
                        if team.is_deployed:
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail="Cannot update tool. Used in deployed workflow.",
                            )

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()

            if isinstance(e, HTTPException):
                raise e

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[Tool]:
        """
        Update tools by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated Tool objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a tool by ID.

        Args:
            resource_id: ID of the tool

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete tools by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a tool by ID.

        Args:
            resource_id: ID of the tool

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.db.query(Tool).filter(Tool.id == resource_id).first()

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e
