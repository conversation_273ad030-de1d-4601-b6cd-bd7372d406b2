"""
Service for api key operations
"""
from datetime import datetime
from uuid import UUID
from typing import Any, Dict, List, Optional, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_
from fastapi import Depends, HTTPException, status

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.api_key import APIKey
from aiplanet_platform.constants.api_key import API_PERMISSIONS
from aiplanet_platform.core.security import verify_password


class APIKeyService:
    """Service for api_key operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = APIKey

    def fetch_resource_by_id(self, resource_id: UUID) -> Optional[APIKey]:
        """
        Fetch a api_key by ID.

        Args:
            resource_id: ID of the api_key

        Returns:
            APIKey object if found, None otherwise
        """
        try:
            return (
                self.db.query(APIKey)
                .filter(APIKey.id == resource_id, APIKey.is_deleted.is_(False))
                .options(joinedload(APIKey.user))
                .first()
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[list[str]] = None,
        sort_order: str = "asc",
    ) -> List[APIKey]:
        """
        Fetch api_keys by filters.

        Args:
            filters: Dictionary of filters to apply
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)

        Returns:
            List of APIKey objects
        """
        try:
            query = self.db.query(APIKey).filter(APIKey.is_deleted.is_(False))

            # Apply filters
            for key, value in filters.items():
                if hasattr(APIKey, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]  # Remove '_like' suffix
                        query = query.filter(
                            getattr(APIKey, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]  # Remove '_gt' suffix
                        query = query.filter(getattr(APIKey, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]  # Remove '_lt' suffix
                        query = query.filter(getattr(APIKey, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]  # Remove '_gte' suffix
                        query = query.filter(getattr(APIKey, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]  # Remove '_lte' suffix
                        query = query.filter(getattr(APIKey, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]  # Remove '_in' suffix
                        query = query.filter(getattr(APIKey, field_name).in_(value))
                    else:
                        query = query.filter(getattr(APIKey, key) == value)

            # Apply sorting
            if sort_by:
                order_fields = []
                for field in sort_by:
                    if hasattr(APIKey, field):
                        order_field = getattr(APIKey, field)
                        if sort_order.lower() == "desc":
                            order_field = order_field.desc()
                        order_fields.append(order_field)

                if order_fields:
                    query = query.order_by(*order_fields)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            return query.all()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_resource(self, data: Dict[str, Any]) -> APIKey:
        """
        Create a new api_key.

        Args:
            data: Dictionary of api_key data

        Returns:
            Created APIKey object
        """
        try:
            resource = APIKey(**data)
            self.db.add(resource)
            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self, resource_id: UUID, data: Dict[str, Any]
    ) -> Optional[APIKey]:
        """
        Update a api_key by ID.

        Args:
            resource_id: ID of the api_key
            data: Dictionary of data to update

        Returns:
            Updated APIKey object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return None

            for key, value in data.items():
                if hasattr(resource, key):
                    setattr(resource, key, value)

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[APIKey]:
        """
        Update api_keys by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated APIKey objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a api_key by ID.

        Args:
            resource_id: ID of the api_key

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete api_keys by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a api_key by ID.

        Args:
            resource_id: ID of the api_key

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.db.query(APIKey).filter(APIKey.id == resource_id).first()

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_api_key(
        self, user_id: UUID, organization_id: UUID, api_key_data: Dict[str, Any]
    ) -> Tuple[APIKey, str]:
        """
        Create a new API key for a user.

        Args:
            user_id: ID of the user creating the key
            organization_id: ID of the user's organization
            api_key_data: API key creation data

        Returns:
            Tuple of (APIKey object, actual key string)
        """
        try:
            # Generate the API key
            full_key, prefix, key_hash = APIKey.generate_key()

            # Prepare API key data
            api_key_data_complete = {
                **api_key_data,
                "key_prefix": prefix,
                "key_hash": key_hash,
                "user_id": user_id,
                "organization_id": organization_id,
                "created_by": user_id,
            }

            # Create the API key record
            api_key = self.create_resource(api_key_data_complete)

            return api_key, full_key
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create API key: {str(e)}",
            ) from e

    def authenticate_api_key(
        self, key: str, update_usage: bool = True
    ) -> Optional[APIKey]:
        """
        Authenticate an API key and return the associated APIKey object.

        Args:
            key: The API key string
            update_usage: Whether to update usage statistics (default: True)
                        Set to False for validation-only checks (like middleware)

        Returns:
            APIKey object if valid, None otherwise
        """
        try:
            if not key or not key.startswith("ak_"):
                return None

            # Extract prefix for quick lookup
            try:
                prefix = key[:11]  # ak_ + 8 chars
            except IndexError:
                return None

            # Find API key by prefix first (faster than checking all hashes)
            api_key = (
                self.db.query(APIKey)
                .filter(and_(APIKey.key_prefix == prefix, APIKey.is_active))
                .first()
            )

            if not api_key:
                return None

            # Verify the full key hash
            if not verify_password(key, api_key.key_hash):
                return None

            # Check if expired
            if api_key.is_expired():
                return None

            # Update usage statistics ONLY if requested
            if update_usage:
                api_key.update_usage()
                self.db.commit()

            return api_key
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Authentication error: {str(e)}",
            ) from e

    def validate_api_key(self, key: str) -> Optional[APIKey]:
        """
        Validate an API key WITHOUT updating usage statistics.
        Use this for validation-only checks (like middleware rate limiting).

        Args:
            key: The API key string

        Returns:
            APIKey object if valid, None otherwise
        """
        return self.authenticate_api_key(key, update_usage=False)

    def get_user_api_keys(self, user_id: UUID, organization_id: UUID) -> List[APIKey]:
        """
        Get all API keys for a user within their organization.

        Args:
            user_id: User ID
            organization_id: Organization ID

        Returns:
            List of APIKey objects
        """
        try:
            return self.fetch_resource_by_filters(
                {"user_id": user_id, "organization_id": organization_id},
                sort_by="created_at",
                sort_order="desc",
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def revoke_api_key(self, key_id: UUID, user_id: UUID) -> bool:
        """
        Revoke (deactivate) an API key.

        Args:
            key_id: API key ID
            user_id: User ID (for authorization)

        Returns:
            True if revoked successfully, False if not found
        """
        try:
            api_key = (
                self.db.query(APIKey)
                .filter(and_(APIKey.id == key_id, APIKey.user_id == user_id))
                .first()
            )

            if not api_key:
                return False

            api_key.is_active = False
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_api_key(
        self, key_id: UUID, user_id: UUID, update_data: Dict[str, Any]
    ) -> Optional[APIKey]:
        """
        Update an API key.

        Args:
            key_id: API key ID
            user_id: User ID (for authorization)
            update_data: Update data

        Returns:
            Updated APIKey object or None if not found
        """
        try:
            api_key = (
                self.db.query(APIKey)
                .filter(and_(APIKey.id == key_id, APIKey.user_id == user_id))
                .first()
            )

            if not api_key:
                return None

            # Update fields
            for field, value in update_data.items():
                if hasattr(api_key, field) and value is not None:
                    setattr(api_key, field, value)

            self.db.commit()
            self.db.refresh(api_key)
            return api_key
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def get_api_key_usage_stats(self, key_id: UUID, user_id: UUID) -> Optional[Dict]:
        """
        Get usage statistics for an API key.

        Args:
            key_id: API key ID
            user_id: User ID (for authorization)

        Returns:
            Dictionary with usage stats or None if not found
        """
        try:
            api_key = (
                self.db.query(APIKey)
                .filter(and_(APIKey.id == key_id, APIKey.user_id == user_id))
                .first()
            )

            if not api_key:
                return None

            return {
                "total_requests": api_key.total_requests,
                "last_used_at": api_key.last_used_at,
                "created_at": api_key.created_at,
                "is_active": api_key.is_active,
                "is_expired": api_key.is_expired(),
                "rate_limits": {
                    "per_minute": api_key.rate_limit_per_minute,
                    "per_hour": api_key.rate_limit_per_hour,
                    "per_day": api_key.rate_limit_per_day,
                },
            }
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def cleanup_expired_keys(self) -> int:
        """
        Cleanup expired API keys by marking them as inactive.

        Returns:
            Number of keys cleaned up
        """
        try:
            expired_keys = (
                self.db.query(APIKey)
                .filter(and_(APIKey.expires_at < datetime.utcnow(), APIKey.is_active))
                .all()
            )

            count = 0
            for key in expired_keys:
                key.is_active = False
                count += 1

            self.db.commit()
            return count
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    @staticmethod
    def get_available_scopes() -> List[Dict[str, str]]:
        """
        Get list of available API scopes.

        Returns:
            List of scope information
        """
        return API_PERMISSIONS

    def count_resources(self, filters: Dict[str, Any] = None) -> int:
        """Count users matching the given filters."""
        try:
            query = self.db.query(self.model)

            if filters:
                for key, value in filters.items():
                    if hasattr(self.model, key):
                        if key.endswith("_like"):
                            field_name = key[:-5]
                            if hasattr(self.model, field_name):
                                field = getattr(self.model, field_name)
                                query = query.filter(field.ilike(f"%{value}%"))
                        elif key.endswith("_gt"):
                            field_name = key[:-3]
                            if hasattr(self.model, field_name):
                                field = getattr(self.model, field_name)
                                query = query.filter(field > value)
                        elif key.endswith("_lt"):
                            field_name = key[:-3]
                            if hasattr(self.model, field_name):
                                field = getattr(self.model, field_name)
                                query = query.filter(field < value)
                        else:
                            field = getattr(self.model, key)
                            query = query.filter(field == value)

            # Add soft delete filter if applicable
            if hasattr(self.model, "is_deleted"):
                query = query.filter(self.model.is_deleted.is_(False))

            return query.count()

        except Exception as e:
            raise Exception(f"Failed to count users: {str(e)}")
