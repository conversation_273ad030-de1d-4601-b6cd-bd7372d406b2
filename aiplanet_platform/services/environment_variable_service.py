"""
Service for environment variable operations
"""

import base64
from typing import Any, Dict, List, Optional
from uuid import UUID

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.config import get_settings
from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.environment_variable import EnvironmentVariable

settings = get_settings()


class EnvironmentVariableService:
    """Service for environment variable operations"""

    def __init__(self, db: Session = Depends(get_db)):
        self.db = db
        self.model = EnvironmentVariable
        self.fernet = self._get_fernet_instance()

    def _get_fernet_instance(self) -> Fernet:
        salt = b"aiplanet_env_var_salt"
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(settings.SECRET_KEY.encode()))
        return Fernet(key)

    def encrypt_value(self, value: str) -> str:
        if not value:
            return value
        prefix = "enc:"
        if value.startswith(prefix):
            return value
        encrypted_bytes = self.fernet.encrypt(value.encode())
        encrypted_str = base64.urlsafe_b64encode(encrypted_bytes).decode()
        return f"{prefix}{encrypted_str}"

    def decrypt_value(self, value: str) -> str:
        if not value or not isinstance(value, str):
            return value
        prefix = "enc:"
        if not value.startswith(prefix):
            return value
        try:
            encrypted_str = value[len(prefix) :]
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_str)
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception:
            return "********"

    def _generate_value_prefix(self, value: str) -> str:
        """
        Generate a value prefix for secret keys, e.g., 'sk-****' or first 4 chars + '****'.
        """
        if not value:
            return "****"
        return value[:4] + "****"

    def create_resource(self, data: Dict[str, Any]) -> EnvironmentVariable:
        """
        Create a new environment variable resource.
        """
        try:
            if data.get("type") == "secret" and data.get("value"):
                data["value_prefix"] = self._generate_value_prefix(data["value"])
                data["value"] = self.encrypt_value(data["value"])
            variable = self.model(**data)
            self.db.add(variable)
            self.db.commit()
            self.db.refresh(variable)
            return variable
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self, resource_id: UUID, data: Dict[str, Any]
    ) -> Optional[EnvironmentVariable]:
        """
        Update an environment variable by ID.
        """
        try:
            variable = self.fetch_resource_by_id(resource_id)
            if not variable:
                return None
            for key, value in data.items():
                if key == "value" and (
                    data.get("type") == "secret"
                    or getattr(variable, "type", None) == "secret"
                ):
                    value = self.encrypt_value(value)
                setattr(variable, key, value)
            # If type is secret and value is present, update value_prefix
            if (
                data.get("type") == "secret"
                or getattr(variable, "type", None) == "secret"
            ) and data.get("value"):
                variable.value_prefix = self._generate_value_prefix(data["value"])
            self.db.commit()
            self.db.refresh(variable)
            return variable
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_id(
        self, resource_id: UUID, decrypt: bool = False
    ) -> Optional[EnvironmentVariable]:
        """
        Fetch an environment variable by ID.

        Args:
            resource_id: ID of the environment variable
            decrypt: Whether to decrypt the value if type is 'string'

        Returns:
            EnvironmentVariable object if found, None otherwise
        """
        try:
            variable = (
                self.db.query(self.model).filter(self.model.id == resource_id).first()
            )
            if variable and decrypt and variable.type == "string":
                variable.value = self.decrypt_value(variable.value)
            return variable
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        decrypt: bool = False,
    ) -> List[EnvironmentVariable]:
        """
        Fetch environment variables by filters.

        Args:
            filters: Dictionary of filters to apply
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order ("asc" or "desc")
            decrypt: Whether to decrypt the value if type is 'string'

        Returns:
            List of EnvironmentVariable objects
        """
        try:
            query = self.db.query(self.model)
            for key, value in filters.items():
                if key == "name_like":
                    query = query.filter(self.model.name.ilike(f"%{value}%"))
                elif hasattr(self.model, key):
                    query = query.filter(getattr(self.model, key) == value)
            if hasattr(self.model, sort_by):
                if sort_order.lower() == "desc":
                    query = query.order_by(getattr(self.model, sort_by).desc())
                else:
                    query = query.order_by(getattr(self.model, sort_by).asc())
            query = query.offset(skip).limit(limit)
            variables = query.all()
            if decrypt:
                for var in variables:
                    if var.type == "string":
                        var.value = self.decrypt_value(var.value)
            return variables
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[EnvironmentVariable]:
        """
        Update environment variables by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated EnvironmentVariable objects
        """
        try:
            variables = self.fetch_resource_by_filters(filters)
            for variable in variables:
                for key, value in data.items():
                    if key == "value" and variable.type == "string":
                        value = self.encrypt_value(value)
                    setattr(variable, key, value)
            self.db.commit()
            for variable in variables:
                self.db.refresh(variable)
            return variables
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete an environment variable by ID.

        Args:
            resource_id: ID of the environment variable

        Returns:
            True if deleted, False otherwise
        """
        try:
            variable = (
                self.db.query(self.model).filter(self.model.id == resource_id).first()
            )
            if not variable:
                return False
            self.db.delete(variable)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e
