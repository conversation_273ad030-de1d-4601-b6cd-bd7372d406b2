"""
Service for websocket manager operations
"""
import asyncio
import logging
import traceback
from datetime import date, datetime, time, timezone, timedelta
from uuid import UUID
from typing import Any, Dict, Optional, Union, Sequence, Callable
from sqlalchemy.orm import Session
from fastapi import Depends, WebSocket, WebSocketDisconnect
from autogen_core import CancellationToken, Image as AGImage
from autogen_agentchat.messages import (
    BaseAgentEvent,
    BaseChatMessage,
    ChatMessage,
    HandoffMessage,
    ModelClientStreamingChunkEvent,
    MultiModalMessage,
    StopMessage,
    TextMessage,
    ToolCallExecutionEvent,
    ToolCallRequestEvent,
)
from autogen_agentchat.base import TaskResult as RawTaskResult

# Temporal imports
from temporalio.client import WorkflowHandle
from temporalio.common import WorkflowIDReusePolicy


from aiplanet_platform.core.database import get_db
from aiplanet_platform.constants.team_manager import (
    TeamResult,
    TaskResult,
    SettingsConfig,
    MessageConfig,
    LLMCallEventMessage,
)
from aiplanet_platform.constants.run_context import <PERSON><PERSON><PERSON><PERSON><PERSON>, RunStatus
from aiplanet_platform.services.team_manager_service import TeamManagerService
from aiplanet_platform.services.user_service import UserService
from aiplanet_platform.services.run_service import RunService
from aiplanet_platform.services.message_service import MessageService
from aiplanet_platform.services.settings_service import SettingsService
from aiplanet_platform.models.settings import Settings
from aiplanet_platform.jobs.temporal_streaming_workflow import (
    TeamStreamingWorkflow,
    TeamStreamingWorkflowInput,
    TeamStreamingWorkflowResult,
)
from aiplanet_platform.core.temporal_config import TemporalClient
from aiplanet_platform.core.config import get_settings

settings = get_settings()

logger = logging.getLogger(__name__)


class WebsocketManagerService:
    """Service for websocket_manager operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self._connections: Dict[int, WebSocket] = {}
        self._cancellation_tokens: Dict[int, CancellationToken] = {}

        # Track explicitly closed connections
        self._closed_connections: set[int] = set()
        self._input_responses: Dict[int, asyncio.Queue] = {}
        self._cancel_message = TeamResult(
            task_result=TaskResult(
                messages=[TextMessage(source="user", content="Run cancelled by user")],
                stop_reason="cancelled by user",
            ),
            usage="",
            duration=0,
        ).model_dump()
        self.user_service = UserService(self.db)
        self.team_service = TeamManagerService(self.db)
        self.run_service = RunService(self.db)
        self.message_service = MessageService(self.db)
        self.settings_service = SettingsService(self.db)

        # Temporal configuration
        self.use_temporal = self._should_use_temporal()
        self._temporal_workflows: Dict[UUID, WorkflowHandle] = {}

    def _should_use_temporal(self) -> bool:
        """Determine if Temporal should be used based on configuration"""
        # Check if Temporal is enabled via environment variable
        temporal_enabled = settings.TEMPORAL_ENABLED

        # Additional checks can be added here
        # For example, check if Temporal server is accessible

        return temporal_enabled

    def _get_stop_message(self, reason: str) -> Dict[str, Any]:
        return TeamResult(
            task_result=TaskResult(
                messages=[TextMessage(source="user", content=f"Run stopped: {reason}")],
                stop_reason=reason,
            ),
            usage="",
            duration=0,
        ).model_dump()

    async def connect(self, websocket: WebSocket, run_id: UUID) -> bool:
        try:
            await websocket.accept()
            self._connections[run_id] = websocket
            self._closed_connections.discard(run_id)
            self._input_responses[run_id] = asyncio.Queue()

            return True
        except Exception as e:
            logger.error(f"Connection error for run {run_id}: {e}")
            return False

    async def start_stream(
        self,
        run_id: UUID,
        task: str | ChatMessage | Sequence[ChatMessage] | None,
        team_config: Dict,
    ) -> None:
        """
        Start streaming the task to the websocket with Temporal integration.

        Args:
            run_id: Run ID
            task: Task to stream
            team_config: Team configuration
        """
        if run_id not in self._connections or run_id in self._closed_connections:
            raise ValueError(f"No active connection for run {run_id}")

        with RunContext.populate_context(run_id=run_id):
            if self.use_temporal:
                await self._start_stream_with_temporal(run_id, task, team_config)
            else:
                await self._start_stream_legacy(run_id, task, team_config)

    async def _start_stream_with_temporal(
        self,
        run_id: UUID,
        task: str | ChatMessage | Sequence[ChatMessage] | None,
        team_config: Dict,
    ) -> None:
        """Start streaming using Temporal workflow"""
        try:
            logger.info(f"Starting Temporal workflow for run {run_id}")

            # Get run details
            run = self.run_service.fetch_resource_by_id(run_id)
            env_vars = None
            user_id = None

            if run and run.user_id:
                user_id = str(run.user_id)
                user = self.user_service.fetch_resource_by_id(run.user_id)
                env_vars = (
                    SettingsConfig(**user.settings.config).environment
                    if user.settings and user.settings.config
                    else None
                )

                # Update run task
                run.task = self._convert_images_in_dict(
                    MessageConfig(content=task, source="user").model_dump()
                )
                run.status = RunStatus.ACTIVE
                self.run_service.update_resource_by_id(run_id, run.__dict__)

            # Prepare workflow input
            workflow_input = TeamStreamingWorkflowInput(
                run_id=str(run_id),
                task=task if isinstance(task, (str, dict)) else str(task),
                team_config=team_config,
                env_vars=[
                    var.model_dump() if hasattr(var, "model_dump") else var
                    for var in env_vars
                ]
                if env_vars
                else None,
                user_id=user_id,
            )

            # Get Temporal client
            client = await TemporalClient.get_client()
            config = TemporalClient.get_config()

            # Start workflow
            workflow_id = f"team-streaming-{run_id}"
            workflow_handle = await client.start_workflow(
                TeamStreamingWorkflow.run,
                workflow_input,
                id=workflow_id,
                task_queue=config.task_queue,
                execution_timeout=timedelta(hours=2),
                id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE_FAILED_ONLY,
            )

            # Store workflow handle for potential cancellation
            self._temporal_workflows[run_id] = workflow_handle

            logger.info(f"Started Temporal workflow {workflow_id} for run {run_id}")

            # Start streaming workflow progress to websocket
            await self._stream_temporal_workflow_progress(run_id, workflow_handle)

        except Exception as e:
            logger.error(f"Failed to start Temporal workflow for run {run_id}: {e}")
            # Fallback to legacy mode
            logger.info(f"Falling back to legacy mode for run {run_id}")
            # await self._start_stream_legacy(run_id, task, team_config)
            # raise e

    async def _stream_temporal_workflow_progress(
        self, run_id: UUID, workflow_handle: WorkflowHandle
    ) -> None:
        """Stream Temporal workflow progress to websocket"""
        try:
            # Send initial status
            await self._send_message(
                run_id,
                {
                    "type": "workflow_started",
                    "workflow_id": workflow_handle.id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
            )

            # For now, we'll poll the workflow status
            # In a production setup, you might want to use Temporal's query or signal features
            # for more real-time updates

            polling_interval = 2.0  # seconds
            last_status = None

            while True:
                try:
                    # Check if workflow is still running
                    description = await workflow_handle.describe()
                    current_status = description.status.name

                    # Send status update if changed
                    if current_status != last_status:
                        await self._send_message(
                            run_id,
                            {
                                "type": "workflow_status",
                                "status": current_status,
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                            },
                        )
                        last_status = current_status

                    # Check if workflow completed
                    if description.status.name in ["COMPLETED", "FAILED", "CANCELED"]:
                        break

                    # Wait before next poll
                    await asyncio.sleep(polling_interval)

                except Exception as poll_error:
                    logger.error(f"Error polling workflow status: {poll_error}")
                    break

            # Get final result
            try:
                result: TeamStreamingWorkflowResult = await workflow_handle.result()

                logger.info(
                    f"Workflow completed for run {run_id} and final result is {result.final_result}"
                )
                # Convert result to legacy format for compatibility
                final_message = TeamResult(
                    task_result=TaskResult(
                        messages=[
                            TextMessage(
                                source="system",
                                content=result.final_result.get("task_result", {}).get(
                                    "messages"
                                )[-1]["content"],
                            )
                        ],
                        stop_reason="completed",
                    ),
                    usage="",
                    duration=result.duration,
                ).model_dump()

                if result.final_result:
                    final_message = result.final_result

                await self._send_message(
                    run_id,
                    {
                        "type": "completion",
                        "status": result.status,
                        "data": final_message,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                )

            except Exception as result_error:
                logger.error(f"Error getting workflow result: {result_error}")
                await self._handle_stream_error(run_id, result_error)

        except Exception as e:
            logger.error(f"Error streaming workflow progress for run {run_id}: {e}")
            await self._handle_stream_error(run_id, e)
        finally:
            # Clean up workflow handle
            self._temporal_workflows.pop(run_id, None)

    async def _start_stream_legacy(
        self,
        run_id: UUID,
        task: str | ChatMessage | Sequence[ChatMessage] | None,
        team_config: Dict,
    ) -> None:
        """
        Start streaming the task to the websocket.

        Args:
            run_id: Run ID
            task: Task to stream
            team_config: Team configuration
        """
        if run_id not in self._connections or run_id in self._closed_connections:
            raise ValueError(f"No active connection for run {run_id}")

        with RunContext.populate_context(run_id=run_id):
            team_manager = self.team_service
            cancellation_token = CancellationToken()
            self._cancellation_tokens[run_id] = cancellation_token
            final_result = None
            env_vars = None

            try:
                run = self.run_service.fetch_resource_by_id(run_id)

                if run is not None and run.user_id:
                    user_settings = self.user_service.fetch_resource_by_id(run.user_id)
                    env_vars = (
                        SettingsConfig(**user_settings.config).environment
                        if user_settings.settings
                        else None
                    )
                    run.task = self._convert_images_in_dict(
                        MessageConfig(content=task, source="user").model_dump()
                    )
                    run.status = RunStatus.ACTIVE

                    self.run_service.update_resource_by_id(run_id, run.__dict__)

                input_func = self.create_input_func(run_id)

                async for message in team_manager.run_stream(
                    task=task,
                    team_config=team_config,
                    input_func=input_func,
                    cancellation_token=cancellation_token,
                    env_vars=env_vars,
                ):
                    if (
                        cancellation_token.is_cancelled()
                        or run_id in self._closed_connections
                    ):
                        logger.info(
                            f"Stream cancelled or connection closed for run {run_id}"
                        )
                        break

                    formatted_message = self._format_message(message)
                    if formatted_message:
                        await self._send_message(run_id, formatted_message)

                        # Save messages by concrete type
                        if isinstance(
                            message,
                            (
                                TextMessage,
                                MultiModalMessage,
                                StopMessage,
                                HandoffMessage,
                                ToolCallRequestEvent,
                                ToolCallExecutionEvent,
                                LLMCallEventMessage,
                            ),
                        ):
                            await self._save_message(run_id, message)

                        # Capture final result if it's a TeamResult
                        elif isinstance(message, TeamResult):
                            final_result = message.model_dump()
                        elif isinstance(message, RawTaskResult):
                            task_result = TaskResult(
                                messages=message.messages,
                                stop_reason=message.stop_reason,
                            )
                            final_result = task_result.model_dump()

                if (
                    not cancellation_token.is_cancelled()
                    and run_id not in self._closed_connections
                ):
                    if final_result:
                        await self._update_run(
                            run_id, RunStatus.COMPLETE, team_result=final_result
                        )
                    else:
                        logger.warning(
                            f"No final result captured for completed run {run_id}"
                        )
                        await self._update_run_status(run_id, RunStatus.COMPLETE)
                else:
                    await self._send_message(
                        run_id,
                        {
                            "type": "completion",
                            "status": "cancelled",
                            "data": self._cancel_message,
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                    )
                    # Update run with cancellation result
                    await self._update_run(
                        run_id, RunStatus.STOPPED, team_result=self._cancel_message
                    )

            except Exception as e:
                logger.error(f"Error streaming task for run {run_id}: {e}")
                traceback.print_exc()
                await self._handle_stream_error(run_id, e)

            finally:
                self._cancellation_tokens.pop(run_id, None)

    async def _save_message(
        self,
        run_id: UUID,
        message: Union[BaseAgentEvent | BaseChatMessage, BaseChatMessage],
    ) -> None:
        run = self.run_service.fetch_resource_by_id(run_id)
        if run:
            new_message = dict(
                session_id=run.session_id,
                run_id=run_id,
                config=self._convert_images_in_dict(message.model_dump()),
                user_id=run.user_id,
            )
            self.message_service.create_resource(new_message)

    async def _update_run(
        self,
        run_id: UUID,
        status: RunStatus,
        team_result: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
    ) -> None:
        run = self.run_service.fetch_resource_by_id(run_id)
        if run:
            run.status = status
            if team_result:
                run.team_result = self._convert_images_in_dict(team_result)
            if error:
                run.error = error
            self.run_service.update_resource_by_id(run_id, run.__dict__)

    def create_input_func(self, run_id: UUID) -> Callable:
        """
        Create an input function for the run.

        Args:
            run_id: Run ID
        """

        async def input_func(prompt: str) -> str:
            try:
                await self._send_message(
                    run_id,
                    {
                        "type": "input_request",
                        "prompt": prompt,
                        "data": {"source": "system", "content": prompt},
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                )

                # Wait for response
                if run_id in self._input_responses:
                    response = await self._input_responses[run_id].get()
                    return response
                else:
                    raise ValueError(f"No input response queue for run {run_id}")
            except Exception as e:
                logger.error(f"Error in input function for run {run_id}: {e}")
                raise

        return input_func

    async def handle_input_response(self, run_id: UUID, response: str) -> None:
        if run_id in self._input_responses:
            await self._input_responses[run_id].put(response)
        else:
            logger.warning(f"No input response queue for run {run_id}")

    async def stop_run(self, run_id: UUID, reason: str) -> None:
        if run_id in self._cancellation_tokens:
            logger.info(f"Stopping run {run_id}")

            stop_message = self._get_stop_message(reason)

            try:
                await self._update_run(
                    run_id, status=RunStatus.STOPPED, team_result=stop_message
                )

                # Then handle websocket communication if connection is active
                if (
                    run_id in self._connections
                    and run_id not in self._closed_connections
                ):
                    await self._send_message(
                        run_id,
                        {
                            "type": "completion",
                            "status": "cancelled",
                            "data": stop_message,
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                    )

                self._cancellation_tokens[run_id].cancel()
            except Exception as e:
                logger.error(f"Error stopping run {run_id}: {e}")

    async def disconnect(self, run_id: UUID) -> None:
        """
        Disconnect from the websocket.

        Args:
            run_id: Run ID
        """
        logger.info(f"Disconnecting from run {run_id}")

        # Mark as closed before cleanup to prevent any new messages
        self._closed_connections.add(run_id)

        # Cancel any running tasks
        await self.stop_run(run_id, "Connection closed")

        # Clean up resources
        self._connections.pop(run_id, None)
        self._cancellation_tokens.pop(run_id, None)
        self._input_responses.pop(run_id, None)

    def _convert_images_in_dict(self, obj: Any) -> Any:
        """Recursively find and convert Image and datetime objects in dictionaries and lists"""
        if isinstance(obj, dict):
            return {k: self._convert_images_in_dict(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_images_in_dict(item) for item in obj]
        elif isinstance(obj, AGImage):
            return {
                "type": "image",
                "url": f"data:image/png;base64,{obj.to_base64()}",
                "alt": "Image",
            }
        elif isinstance(obj, (datetime, date, time)):
            return obj.isoformat()
        else:
            return obj

    async def _send_message(self, run_id: UUID, message: Dict[str, Any]) -> None:
        """Send a message to the websocket."""
        if run_id in self._closed_connections:
            logger.warning(
                f"Attempted to send message to closed connection for run {run_id}"
            )
            return

        try:
            if run_id in self._connections:
                websocket = self._connections[run_id]
                await websocket.send_json(self._convert_images_in_dict(message))
        except WebSocketDisconnect as e:
            logger.error(f"Error sending message to run {run_id}: {e}")
            await self.disconnect(run_id)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Error sending message to run {run_id}: {e}, {message}")
            # Don't try to send error message here to avoid potential recursive loop
            await self._update_run_status(run_id, RunStatus.ERROR, str(e))
            await self.disconnect(run_id)

    async def _handle_stream_error(self, run_id: UUID, error: Exception) -> None:
        """Handle stream errors with proper run updates"""
        if run_id not in self._closed_connections:
            error_result = TeamResult(
                task_result=TaskResult(
                    messages=[TextMessage(source="system", content=str(error))],
                    stop_reason="An error occurred while processing this run",
                ),
                usage="",
                duration=0,
            ).model_dump()

            await self._send_message(
                run_id,
                {
                    "type": "completion",
                    "status": "error",
                    "data": error_result,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
            )

            await self._update_run(
                run_id, RunStatus.ERROR, team_result=error_result, error=str(error)
            )

    def _format_message(self, message: Any) -> Optional[dict]:
        """Format message for WebSocket transmission

        Args:
            message: Message to format

        Returns:
            Optional[dict]: Formatted message or None if formatting fails
        """

        try:
            if isinstance(message, MultiModalMessage):
                message_dump = message.model_dump()

                message_content = []
                for row in message_dump["content"]:
                    if isinstance(row, dict) and "data" in row:
                        message_content.append(
                            {
                                "url": f"data:image/png;base64,{row['data']}",
                                "alt": "WebSurfer Screenshot",
                            }
                        )
                    else:
                        message_content.append(row)
                message_dump["content"] = message_content

                return {"type": "message", "data": message_dump}

            elif isinstance(message, TeamResult):
                return {
                    "type": "result",
                    "data": message.model_dump(),
                    "status": "complete",
                }
            elif isinstance(message, RawTaskResult):
                task_result = TaskResult(
                    messages=message.messages,
                    stop_reason=message.stop_reason,
                )
                return {
                    "type": "result",
                    "data": task_result.model_dump(),
                    "status": "complete",
                }
            elif isinstance(message, ModelClientStreamingChunkEvent):
                return {"type": "message_chunk", "data": message.model_dump()}

            elif isinstance(
                message,
                (
                    TextMessage,
                    StopMessage,
                    HandoffMessage,
                    ToolCallRequestEvent,
                    ToolCallExecutionEvent,
                    LLMCallEventMessage,
                ),
            ):
                return {"type": "message", "data": message.model_dump()}

            return None

        except Exception as e:
            logger.error(f"Message formatting error: {e}")
            traceback.print_exc()
            return None

    async def _get_settings(self, user_id: UUID) -> Optional[Settings]:
        """Get user settings from database
        Args:
            user_id: User ID to retrieve settings for
        Returns:
            Optional[dict]: User settings if found, None otherwise
        """
        # response = self.db_manager.get(filters={"user_id": user_id}, model_class=Settings, return_json=False)
        # return response.data[0] if response.status and response.data else None
        settings = self.settings_service.fetch_resource_by_filters({"user_id": user_id})
        return settings[0] if settings else None

    async def _update_run_status(
        self, run_id: int, status: RunStatus, error: Optional[str] = None
    ) -> None:
        """Update run status in database

        Args:
            run_id: id of the run to update
            status: New status to set
            error: Optional error message
        """
        run = await self.run_service.fetch_resource_by_id(run_id)
        if run:
            run.status = status
            run.error_message = error
            await self.run_service.update_resource_by_id(run_id, run._dict__)
        else:
            logger.warning(f"Run {run_id} not found for status update")

    async def cleanup(self) -> None:
        """Clean up all active connections and resources when server is shutting down"""
        logger.info(f"Cleaning up {len(self.active_connections)} active connections")

        try:
            # First cancel all running tasks
            for run_id in self.active_runs.copy():
                if run_id in self._cancellation_tokens:
                    self._cancellation_tokens[run_id].cancel()
                run = await self._get_run(run_id)
                if run and run.status == RunStatus.ACTIVE:
                    interrupted_result = TeamResult(
                        task_result=TaskResult(
                            messages=[
                                TextMessage(
                                    source="system",
                                    content="Run interrupted by server shutdown",
                                )
                            ],
                            stop_reason="server_shutdown",
                        ),
                        usage="",
                        duration=0,
                    ).model_dump()

                    run.status = RunStatus.STOPPED
                    run.team_result = interrupted_result
                    self.db_manager.upsert(run)

            # Then disconnect all websockets with timeout
            # 10 second timeout for entire cleanup
            async with asyncio.timeout(10):
                for run_id in self.active_connections.copy():
                    try:
                        # Give each disconnect operation 2 seconds
                        async with asyncio.timeout(2):
                            await self.disconnect(run_id)
                    except asyncio.TimeoutError:
                        logger.warning(f"Timeout disconnecting run {run_id}")
                    except Exception as e:
                        logger.error(f"Error disconnecting run {run_id}: {e}")

        except asyncio.TimeoutError:
            logger.warning("WebSocketManager cleanup timed out")
        except Exception as e:
            logger.error(f"Error during WebSocketManager cleanup: {e}")
        finally:
            # Always clear internal state, even if cleanup had errors
            self._connections.clear()
            self._cancellation_tokens.clear()
            self._closed_connections.clear()
            self._input_responses.clear()

    @property
    def active_connections(self) -> set[int]:
        """Get set of active run IDs"""
        return set(self._connections.keys()) - self._closed_connections

    @property
    def active_runs(self) -> set[int]:
        """Get set of runs with active cancellation tokens"""
        return set(self._cancellation_tokens.keys())
