"""
Service for team operations
"""

import json
import os
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload
from autogen_agentchat.teams import BaseGroupChat

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.team import Team
from aiplanet_platform.models.output import Output
from aiplanet_platform.models.input import Input
from aiplanet_platform.models.termination_condition import TerminationCondition
from aiplanet_platform.models.agent import Agent


class TeamService:
    """Service for team operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = Team
        # Map filterable relationship keys to Team model attributes
        self.relationship_filters = {
            "team_outputs": Team.team_outputs,
            "team_inputs": Team.team_inputs,
            "team_termination_conditions": Team.team_termination_conditions,
            "agents": Team.agents,
        }

    def fetch_resource_by_id(self, resource_id: UUID) -> Optional[Team]:
        """
        Fetch a team by ID.

        Args:
            resource_id: ID of the team

        Returns:
            Team object if found, None otherwise
        """
        try:
            team = (
                self.db.query(Team)
                .filter(Team.id == resource_id, Team.is_deleted.is_(False))
                .options(
                    joinedload(Team.team_inputs),
                    joinedload(Team.team_outputs),
                    joinedload(Team.team_termination_conditions),
                    joinedload(Team.agents),
                )
                .first()
            )

            if team:
                team.team_agent_ids = [
                    agent.id for agent in team.agents if not agent.is_deleted
                ]
                team.team_input_ids = [
                    team_input.id
                    for team_input in team.team_inputs
                    if not team_input.is_deleted
                ]
                team.team_output_ids = [
                    team_output.id
                    for team_output in team.team_outputs
                    if not team_output.is_deleted
                ]
                team.team_termination_condition_ids = [
                    team_termination_condition.id
                    for team_termination_condition in team.team_termination_conditions
                    if not team_termination_condition.is_deleted
                ]
            return team

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[list[str]] = None,
        sort_order: str = "asc",
    ) -> List[Team]:
        """
        Fetch agents by filters.
        """
        try:
            query = (
                self.db.query(Team)
                .filter(Team.is_deleted.is_(False))
                .options(
                    joinedload(Team.team_inputs),
                    joinedload(Team.team_outputs),
                    joinedload(Team.team_termination_conditions),
                    joinedload(Team.agents),
                )
            )

            # Apply filters
            for key, value in filters.items():
                if key == "component":
                    # Handle JSON structure filtering
                    value = json.loads(value) if isinstance(value, str) else value
                    for k, v in value.items():
                        # Handle nested JSON
                        if isinstance(v, dict):
                            for sub_k, sub_v in v.items():
                                query = query.filter(
                                    Team.component[k].op("->>")(sub_k) == str(sub_v)
                                )
                        else:
                            query = query.filter(Team.component.op("->>")(k) == str(v))
                    continue

                # Generic handler for relationship filters like {"agents": {"id": "..."}}
                if key in self.relationship_filters and isinstance(value, dict):
                    if "id" in value:
                        query = query.filter(
                            self.relationship_filters[key].any(id=value["id"])
                        )
                    continue

                # Handle special filters for name (stored in component.label)
                if key == "name":
                    # Name is stored in component.label
                    query = query.filter(Team.component.op("->>")("label") == value)
                elif key == "name_like":
                    # Name is stored in component.label, use ilike for partial matching
                    query = query.filter(
                        Team.component.op("->>")("label").ilike(f"%{value}%")
                    )
                elif key == "is_active":
                    # is_active means not deleted and deployed
                    if value:
                        query = query.filter(
                            Team.is_deleted.is_(False), Team.is_deployed.is_(True)
                        )
                    else:
                        query = query.filter(
                            (Team.is_deleted.is_(True)) | (Team.is_deployed.is_(False))
                        )
                elif hasattr(Team, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]
                        query = query.filter(
                            getattr(Team, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Team, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Team, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Team, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]
                        query = query.filter(getattr(Team, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]
                        query = query.filter(getattr(Team, field_name).in_(value))
                    else:
                        query = query.filter(getattr(Team, key) == value)

            # Apply sorting
            if sort_by:
                order_fields = []
                for field in sort_by:
                    if field == "name":
                        # Sort by component.label for name field
                        order_field = Team.component.op("->>")("label")
                        if sort_order.lower() == "desc":
                            order_field = order_field.desc()
                        order_fields.append(order_field)
                    elif hasattr(Team, field):
                        order_field = getattr(Team, field)
                        if sort_order.lower() == "desc":
                            order_field = order_field.desc()
                        order_fields.append(order_field)

                if order_fields:
                    query = query.order_by(*order_fields)

            teams = query.offset(skip).limit(limit).all()
            for team in teams:
                team.team_agent_ids = [
                    agent.id for agent in team.agents if not agent.is_deleted
                ]
                team.team_input_ids = [
                    team_input.id
                    for team_input in team.team_inputs
                    if not team_input.is_deleted
                ]
                team.team_output_ids = [
                    team_output.id
                    for team_output in team.team_outputs
                    if not team_output.is_deleted
                ]
                team.team_termination_condition_ids = [
                    team_termination_condition.id
                    for team_termination_condition in team.team_termination_conditions
                    if not team_termination_condition.is_deleted
                ]
            return teams

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def count_resources(self, filters: Dict[str, Any] = None) -> int:
        """
        Count teams matching the given filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Total count of teams matching the filters
        """
        try:
            query = self.db.query(Team).filter(Team.is_deleted.is_(False))

            if filters:
                # Apply the same filters as in fetch_resource_by_filters
                for key, value in filters.items():
                    if key == "component":
                        # Handle JSON structure filtering
                        value = json.loads(value) if isinstance(value, str) else value
                        for k, v in value.items():
                            # Handle nested JSON
                            if isinstance(v, dict):
                                for sub_k, sub_v in v.items():
                                    query = query.filter(
                                        Team.component[k].op("->>")(sub_k) == str(sub_v)
                                    )
                            else:
                                query = query.filter(
                                    Team.component.op("->>")(k) == str(v)
                                )
                        continue

                    # Generic handler for relationship filters like {"agents": {"id": "..."}}
                    if key in self.relationship_filters and isinstance(value, dict):
                        if "id" in value:
                            query = query.filter(
                                self.relationship_filters[key].any(id=value["id"])
                            )
                        continue

                    # Handle special filters for name (stored in component.label)
                    if key == "name":
                        # Name is stored in component.label
                        query = query.filter(Team.component.op("->>")("label") == value)
                    elif key == "name_like":
                        # Name is stored in component.label, use ilike for partial matching
                        query = query.filter(
                            Team.component.op("->>")("label").ilike(f"%{value}%")
                        )
                    elif key == "is_active":
                        # is_active means not deleted and deployed
                        if value:
                            query = query.filter(
                                Team.is_deleted.is_(False), Team.is_deployed.is_(True)
                            )
                        else:
                            query = query.filter(
                                (Team.is_deleted.is_(True))
                                | (Team.is_deployed.is_(False))
                            )
                    elif key.endswith("_like"):
                        field_name = key[:-5]
                        if hasattr(Team, field_name):
                            query = query.filter(
                                getattr(Team, field_name).ilike(f"%{value}%")
                            )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]
                        if hasattr(Team, field_name):
                            query = query.filter(getattr(Team, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]
                        if hasattr(Team, field_name):
                            query = query.filter(getattr(Team, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]
                        if hasattr(Team, field_name):
                            query = query.filter(getattr(Team, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]
                        if hasattr(Team, field_name):
                            query = query.filter(getattr(Team, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]
                        if hasattr(Team, field_name):
                            query = query.filter(getattr(Team, field_name).in_(value))
                    elif hasattr(Team, key):
                        query = query.filter(getattr(Team, key) == value)

            return query.count()

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_resource(
        self,
        data: Dict[str, Any],
        team_agent_ids: List[UUID] = None,
        team_input_ids: List[UUID] = None,
        team_output_ids: List[UUID] = None,
        team_termination_condition_ids: List[UUID] = None,
    ) -> Team:
        """
        Create a new team.

        Args:
            data: Dictionary of team data

        Returns:
            Created Team object
        """
        try:
            resource = Team(**data)
            self.db.add(resource)
            self.db.flush()

            # Associate tools if provided
            if team_input_ids:
                inputs = self.db.query(Input).filter(Input.id.in_(team_input_ids)).all()
                add_inputs = []
                for id in team_input_ids:
                    for input in inputs:
                        if input.id == id:
                            add_inputs.append(input)
                            break

                if len(add_inputs) != len(team_input_ids):
                    raise ValueError("Some inputs not found or not accessible")

                resource.team_inputs.extend(add_inputs)
                resource.team_input_ids = team_input_ids

            if team_output_ids:
                outputs = (
                    self.db.query(Output).filter(Output.id.in_(team_output_ids)).all()
                )

                add_outputs = []
                for id in team_output_ids:
                    for output in outputs:
                        if output.id == id:
                            add_outputs.append(output)
                            break
                if len(add_outputs) != len(team_output_ids):
                    raise ValueError("Some outputs not found or not accessible")

                resource.team_outputs.extend(add_outputs)
                resource.team_output_ids = team_output_ids

            if team_termination_condition_ids:
                termination_conditions = (
                    self.db.query(TerminationCondition)
                    .filter(TerminationCondition.id.in_(team_termination_condition_ids))
                    .all()
                )

                if len(termination_conditions) != len(team_termination_condition_ids):
                    raise ValueError(
                        "Some termination conditions not found or not accessible"
                    )

                resource.team_termination_conditions.extend(termination_conditions)
                resource.team_termination_condition_ids = team_termination_condition_ids

            if team_agent_ids:
                agents = self.db.query(Agent).filter(Agent.id.in_(team_agent_ids)).all()
                if len(agents) != len(team_agent_ids):
                    raise ValueError("Some tools not found or not accessible")

                for agent in agents:
                    agent.tool_ids = [
                        tool.id for tool in agent.tools if not tool.is_deleted
                    ]

                resource.agents.extend(agents)
                resource.team_agent_ids = team_agent_ids

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self,
        resource_id: UUID,
        data: Dict[str, Any],
        team_agent_ids: List[UUID] = None,
        team_input_ids: List[UUID] = None,
        team_output_ids: List[UUID] = None,
        team_termination_condition_ids: List[UUID] = None,
    ) -> Optional[Team]:
        """
        Update a team by ID including relationships.

        Args:
            resource_id: ID of the team
            data: Dictionary of data to update
            team_agent_ids: List of agent IDs to associate
            team_input_ids: List of input IDs to associate
            team_output_ids: List of output IDs to associate
            team_termination_condition_ids: List of termination condition IDs to associate

        Returns:
            Updated Team object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return None

            # Check if team is deployed
            if resource.is_deployed:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot update team. Team is currently deployed. Please undeploy first.",
                )

            # Update basic fields
            for key, value in data.items():
                if hasattr(resource, key):
                    setattr(resource, key, value)

            # Update relationships if provided
            if team_input_ids is not None:
                # Clear existing relationships
                resource.team_inputs.clear()

                if team_input_ids:
                    inputs = (
                        self.db.query(Input).filter(Input.id.in_(team_input_ids)).all()
                    )
                    if len(inputs) != len(team_input_ids):
                        raise ValueError("Some inputs not found or not accessible")
                    resource.team_inputs.extend(inputs)

                resource.team_input_ids = team_input_ids

            if team_output_ids is not None:
                # Clear existing relationships
                resource.team_outputs.clear()

                if team_output_ids:
                    outputs = (
                        self.db.query(Output)
                        .filter(Output.id.in_(team_output_ids))
                        .all()
                    )
                    if len(outputs) != len(team_output_ids):
                        raise ValueError("Some outputs not found or not accessible")
                    resource.team_outputs.extend(outputs)

                resource.team_output_ids = team_output_ids

            if team_termination_condition_ids is not None:
                # Clear existing relationships
                resource.team_termination_conditions.clear()

                if team_termination_condition_ids:
                    termination_conditions = (
                        self.db.query(TerminationCondition)
                        .filter(
                            TerminationCondition.id.in_(team_termination_condition_ids)
                        )
                        .all()
                    )
                    if len(termination_conditions) != len(
                        team_termination_condition_ids
                    ):
                        raise ValueError(
                            "Some termination conditions not found or not accessible"
                        )
                    resource.team_termination_conditions.extend(termination_conditions)

                resource.team_termination_condition_ids = team_termination_condition_ids

            if team_agent_ids is not None:
                # Clear existing relationships
                resource.agents.clear()

                if team_agent_ids:
                    agents = (
                        self.db.query(Agent).filter(Agent.id.in_(team_agent_ids)).all()
                    )
                    if len(agents) != len(team_agent_ids):
                        raise ValueError("Some agents not found or not accessible")

                    for agent in agents:
                        agent.tool_ids = [
                            tool.id for tool in agent.tools if not tool.is_deleted
                        ]

                    resource.agents.extend(agents)

                resource.team_agent_ids = team_agent_ids

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[Team]:
        """
        Update teams by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated Team objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a team by ID.

        Args:
            resource_id: ID of the team

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            # Check if team is deployed
            if resource.is_deployed:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot delete team. Team is currently deployed. Please undeploy first.",
                )

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete teams by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            # Check if any team is deployed before proceeding
            deployed_teams = [
                resource for resource in resources if resource.is_deployed
            ]
            if deployed_teams:
                deployed_ids = [str(team.id) for team in deployed_teams]
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot delete teams. The following teams are currently deployed: {', '.join(deployed_ids)}. Please undeploy them first.",
                )

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a team by ID.

        Args:
            resource_id: ID of the team

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.db.query(Team).filter(Team.id == resource_id).first()

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def deploy_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Deploy a team by ID.

        Args:
            resource_id: ID of the team

        Returns:
            True if deployed, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            if resource.is_deployed:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Resource already deployed",
                )

            resource.is_deployed = True
            self.generate_workflow(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def undeploy_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Undeploy a team by ID.

        Args:
            resource_id: ID of the team

        Returns:
            True if undeployed, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            if not resource.is_deployed:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Resource not deployed",
                )

            resource.is_deployed = False
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()

            if isinstance(e, HTTPException):
                raise e

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def generate_workflow(self, team: Team) -> None:
        """
        Generate a workflow for a team.

        Args:
            team: Team object
        """
        try:
            component = team.component
            agents = team.agents
            # TODO
            _inputs = team.team_inputs  # type: ignore
            _outputs = team.team_outputs  # type: ignore
            termination_conditions = team.team_termination_conditions
            component["config"]["participants"] = [agent.component for agent in agents]
            component["config"]["termination_condition"] = termination_conditions[
                0
            ].component
            # TODO Add input and output configs

            if team.model_id:
                component["config"]["model_client"] = team.model.component

            # Determine project root directory
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))

            # Create folder at root level
            workflows_dir = os.path.join(project_root, "workflows")
            os.makedirs(workflows_dir, exist_ok=True)

            # Write file
            file_path = os.path.join(workflows_dir, f"team_{team.id}.json")
            with open(file_path, "w") as f:
                json.dump(component, f, indent=2)

            return file_path

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            )

    async def test_resource_by_id(
        self, resource_id: UUID, test_data: Dict[str, Any]
    ) -> bool:
        """
        Test a team by ID.

        Args:
            resource_id: ID of the team

        Returns:
            True if tested, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            if not resource.is_deployed:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Resource not deployed",
                )

            file_path = self.generate_workflow(resource)
            team_json = json.load(open(file_path, "r"))

            team = BaseGroupChat.load_component(team_json)
            result = await team.run(task=test_data["task"])
            return (
                result.messages[-1].content
                if result and len(result.messages) > 0
                else ""
            )
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e
