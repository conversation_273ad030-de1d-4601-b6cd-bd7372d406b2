"""
Service for generating presigned URLs for cloud storage (AWS S3 or Azure Blob Storage)
"""
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict

import aioboto3
from azure.storage.blob.aio import BlobServiceClient
from azure.storage.blob import generate_blob_sas, BlobSasPermissions

from aiplanet_platform.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class PresignedURLService:
    """Service for generating presigned URLs for cloud storage"""

    def __init__(self):
        """Initialize the service based on the configured storage provider"""
        # Clean the provider string (remove comments and whitespace)
        self.provider = settings.STORAGE_PROVIDER.split("#")[0].strip().lower()
        self.s3_client = None
        self.blob_service_client = None
        logger.info(f"Storage provider: {self.provider}")

        if self.provider == "azure":
            if not settings.AZURE_CONNECTION_STRING:
                raise ValueError(
                    "Azure connection string is required when using Azure provider"
                )
            self.blob_service_client = BlobServiceClient.from_connection_string(
                settings.AZURE_CONNECTION_STRING
            )
        elif self.provider == "aws":
            if not all([settings.AWS_ACCESS_KEY_ID, settings.AWS_SECRET_ACCESS_KEY]):
                raise ValueError("AWS credentials are required when using AWS provider")
        else:
            raise ValueError(
                f"Unsupported storage provider: '{self.provider}'. Choose 'aws' or 'azure'"
            )

    async def _initialize_aws_client(self) -> None:
        """Initialize AWS S3 client if not already initialized"""
        if self.provider == "aws" and not self.s3_client:
            try:
                session = aioboto3.Session()
                self.s3_client = await session.client(
                    "s3",
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                    region_name=settings.AWS_REGION,
                ).__aenter__()
                logger.info("AWS S3 client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize AWS S3 client: {str(e)}")
                raise

    async def generate_presigned_url(
        self,
        object_name: str,
        expiration_minutes: int = 15,
        content_type: str = "application/pdf",
    ) -> Dict[str, str]:
        """
        Generate a presigned URL for uploading files to cloud storage

        Args:
            object_name: Name of the object/file
            expiration_minutes: URL expiration time in minutes (default: 15)
            content_type: Content type of the file (default: application/pdf)

        Returns:
            Dictionary containing the presigned URL and provider information
        """
        try:
            if self.provider == "aws":
                url = await self._generate_aws_presigned_url(
                    object_name, expiration_minutes, content_type
                )
                return {
                    "presigned_url": url,
                    "provider": "aws",
                    "bucket_name": settings.AWS_S3_BUCKET,
                    "object_name": object_name,
                    "expires_in_minutes": expiration_minutes,
                }
            elif self.provider == "azure":
                url = await self._generate_azure_sas_url(
                    object_name, expiration_minutes
                )
                return {
                    "presigned_url": url,
                    "provider": "azure",
                    "container_name": settings.AZURE_CONTAINER_NAME,
                    "blob_name": object_name,
                    "expires_in_minutes": expiration_minutes,
                }
            else:
                raise ValueError(
                    f"Unsupported storage provider: '{self.provider}'. Choose 'aws' or 'azure'"
                )
        except Exception as e:
            logger.error(f"Failed to generate presigned URL: {str(e)}")
            raise

    async def _generate_aws_presigned_url(
        self, object_name: str, expiration_minutes: int, content_type: str
    ) -> str:
        """Generate AWS S3 presigned URL"""
        try:
            await self._initialize_aws_client()

            if not settings.AWS_S3_BUCKET:
                raise ValueError("AWS S3 bucket name is required")

            url = await self.s3_client.generate_presigned_url(
                "put_object",
                Params={
                    "Bucket": settings.AWS_S3_BUCKET,
                    "Key": object_name,
                    "ContentType": content_type,
                },
                ExpiresIn=expiration_minutes * 60,
            )

            logger.info(f"Generated AWS S3 presigned URL for object: {object_name}")
            return url

        except Exception as e:
            logger.error(f"Error generating AWS presigned URL: {str(e)}")
            raise Exception(f"Error generating AWS presigned URL: {str(e)}") from e

    async def _generate_azure_sas_url(
        self, blob_name: str, expiration_minutes: int
    ) -> str:
        """Generate Azure Blob Storage SAS URL"""
        try:
            if not settings.AZURE_CONTAINER_NAME:
                raise ValueError("Azure container name is required")

            blob_client = self.blob_service_client.get_blob_client(
                container=settings.AZURE_CONTAINER_NAME, blob=blob_name
            )

            sas_token = generate_blob_sas(
                account_name=self.blob_service_client.account_name,
                container_name=settings.AZURE_CONTAINER_NAME,
                blob_name=blob_name,
                account_key=self.blob_service_client.credential.account_key,
                permission=BlobSasPermissions(write=True),
                expiry=datetime.now(timezone.utc)
                + timedelta(minutes=expiration_minutes),
            )

            url = f"{blob_client.url}?{sas_token}"
            logger.info(f"Generated Azure SAS URL for blob: {blob_name}")
            return url

        except Exception as e:
            logger.error(f"Error generating Azure SAS URL: {str(e)}")
            raise Exception(f"Error generating Azure SAS URL: {str(e)}") from e

    async def close(self) -> None:
        """Close any open connections"""
        try:
            if self.s3_client:
                await self.s3_client.__aexit__(None, None, None)
                logger.info("AWS S3 client connection closed")
        except Exception as e:
            logger.error(f"Error closing connections: {str(e)}")
