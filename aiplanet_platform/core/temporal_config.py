"""
Temporal configuration and client setup
"""
import logging
from typing import Optional
from temporalio.client import Client, TLSConfig
from aiplanet_platform.core.config import get_settings


logger = logging.getLogger(__name__)
settings = get_settings()


class TemporalConfig:
    """Temporal configuration settings"""

    def __init__(self):
        self.host = settings.TEMPORAL_HOST
        self.namespace = settings.TEMPORAL_NAMESPACE
        self.task_queue = settings.TEMPORAL_TASK_QUEUE
        self.tls_enabled = settings.TEMPORAL_TLS_ENABLED
        self.cert_path = settings.TEMPORAL_TLS_CERT_PATH
        self.key_path = settings.TEMPORAL_TLS_KEY_PATH
        self.ca_path = settings.TEMPORAL_TLS_CA_PATH


class TemporalClient:
    """Temporal client singleton"""

    _instance: Optional[Client] = None
    _config: Optional[TemporalConfig] = None

    @classmethod
    async def get_client(cls) -> Client:
        """Get or create Temporal client"""
        if cls._instance is None:
            await cls._initialize()
        return cls._instance

    @classmethod
    async def _initialize(cls):
        """Initialize Temporal client"""
        cls._config = TemporalConfig()

        # Setup TLS if enabled
        tls_config = None
        if cls._config.tls_enabled:
            if cls._config.cert_path and cls._config.key_path:
                with open(cls._config.cert_path, "rb") as cert_file:
                    cert_data = cert_file.read()
                with open(cls._config.key_path, "rb") as key_file:
                    key_data = key_file.read()

                ca_data = None
                if cls._config.ca_path:
                    with open(cls._config.ca_path, "rb") as ca_file:
                        ca_data = ca_file.read()

                tls_config = TLSConfig(
                    client_cert=cert_data,
                    client_private_key=key_data,
                    server_root_ca_cert=ca_data,
                )

        try:
            cls._instance = await Client.connect(
                cls._config.host, namespace=cls._config.namespace, tls=tls_config
            )
            logger.info(f"Connected to Temporal at {cls._config.host}")
        except Exception as e:
            logger.error(f"Failed to connect to Temporal: {e}")
            # For development, we'll create a mock client or handle gracefully
            raise

    @classmethod
    def get_config(cls) -> TemporalConfig:
        """Get Temporal configuration"""
        if cls._config is None:
            cls._config = TemporalConfig()
        return cls._config

    @classmethod
    async def close(cls):
        """Close Temporal client"""
        if cls._instance:
            await cls._instance.aclose()
            cls._instance = None
