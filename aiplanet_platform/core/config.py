"""
Application configuration
"""
import secrets
from functools import lru_cache
from typing import List, Literal, Optional, Union

from pydantic import AnyHttpUrl, Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""

    ENVIRONMENT: Literal["development", "production"] = "development"

    # API settings
    API_V1_PREFIX: str = "/api/v1"
    PROJECT_NAME: str = "aiplanet_platform"
    DEBUG: bool = False

    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    ALLOWED_HOSTS: List[str] = ["*"]

    # Security settings
    SECRET_KEY: str = Field(default_factory=lambda: secrets.token_hex(32))
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    ALGORITHM: str = "HS256"

    # Database settings
    DATABASE_URL: str = (
        "postgresql://postgres:postgres@localhost:5432/aiplanet_platform"
    )
    SQL_ECHO: bool = False
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 1800

    # Cache settings
    REDIS_URL: Optional[str] = None
    CACHE_TTL: int = 300  # 5 minutes

    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = 100

    # Azure Document Intelligence Config
    DOCUMENT_INTEL_ENDPOINT: str = ""
    DOCUMENT_INTEL_KEY: str = ""

    # OAuth Settings
    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GITHUB_CLIENT_ID: str = ""
    GITHUB_CLIENT_SECRET: str = ""

    # OAuth redirect URIs
    FRONTEND_URL: str = "http://localhost:3000"  # Your frontend URL
    OAUTH_REDIRECT_GOOGLE_URI: str = "/google/auth/callback"
    OAUTH_REDIRECT_GITHUB_URI: str = "/github/auth/callback"

    # Cloud Storage Settings
    STORAGE_PROVIDER: str = "aws"  # "aws" or "azure"

    # AWS S3 Settings
    AWS_ACCESS_KEY_ID: str = ""
    AWS_SECRET_ACCESS_KEY: str = ""
    AWS_REGION: str = "us-east-1"
    AWS_S3_BUCKET: str = ""

    # Azure Blob Storage Settings
    AZURE_CONNECTION_STRING: str = ""
    AZURE_CONTAINER_NAME: str = ""

    # Temporal Worker
    TEMPORAL_START_WORKER: bool = True

    # Temporal Configuration
    TEMPORAL_ENABLED: bool = True
    TEMPORAL_HOST: str = (
        "localhost:7233"  # Use "temporal.stage.aiplanet.com:443" for staging
    )
    TEMPORAL_NAMESPACE: str = "default"
    TEMPORAL_TASK_QUEUE: str = "team-manager-tasks"

    # Additional connection settings
    TEMPORAL_CONNECTION_TIMEOUT: float = 10.0
    TEMPORAL_KEEP_ALIVE_TIME: float = 30.0
    TEMPORAL_KEEP_ALIVE_TIMEOUT: float = 5.0

    # Optional TLS configuration for production
    TEMPORAL_TLS_ENABLED: bool = False  # Set to True for staging/production
    TEMPORAL_TLS_CERT_PATH: str = ""
    TEMPORAL_TLS_KEY_PATH: str = ""
    TEMPORAL_TLS_CA_PATH: str = ""

    # Prefect Config
    PREFECT_API_URL: str = "http://localhost:4200/api"
    PREFECT_API_KEY: str = ""
    FLOW_NAME: str = "knowledgebase"
    DEPLOYMENT_NAME: str = "knowledgebase-deployment"
    DEPLOYMENT_NAME_URL: str = "knowledgebase-deployment-url"

    # OpenAI API Key
    OPENAI_API_KEY: str = ""
    OPENAI_EMBEDDING_MODEL: str = "text-embedding-3-small"

    # Azure OpenAI Config
    AZURE_OPENAI_ENDPOINT: str = "https://{your-custom-endpoint}.openai.azure.com/"
    AZURE_OPENAI_API_VERSION: str = "2024-06-01"
    AZURE_OPENAI_API_KEY: str = ""
    AZURE_OPENAI_EMBEDDINGS_API_KEY: str = ""
    AZURE_OPENAI_DEPLOYMENT: str = "{your-azure-deployment}"
    AZURE_OPENAI_RESOURCE_NAME: str = "{your-azure-resource-name}"

    # Chunking Config
    CHUNK_SIZE: int = 800
    CHUNK_OVERLAP: int = 400

    # Weaviate Config
    WEAVIATE_URL: str = "http://localhost:8080"
    WEAVIATE_API_KEY: str = ""
    WEAVIATE_HTTP_PORT: int = 8080
    WEAVIATE_GRPC_PORT: int = 50051
    WEAVIATE_HTTP_SECURE: bool = False
    WEAVIATE_GRPC_SECURE: bool = False
    WEAVIATE_IS_EMBEDDED: bool = False
    WEAVIATE_HTTP_HOST: str = "localhost"
    WEAVIATE_GRPC_HOST: str = "localhost"

    # AWS Config
    AWS_ACCESS_KEY_ID: str = ""
    AWS_SECRET_ACCESS_KEY: str = ""
    AWS_REGION: str = "us-east-1"
    AWS_S3_BUCKET_NAME: str = "aiplanet-platform"

    # Cloudflare turnstile
    TURNSTILE_SECRET_KEY: str = ""

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Pydantic v2 uses model_config instead of Config class
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="ignore"
    )


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings.

    Returns:
        Settings instance
    """
    return Settings()
