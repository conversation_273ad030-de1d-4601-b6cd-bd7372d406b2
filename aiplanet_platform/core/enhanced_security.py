"""
Enhanced security utilities with API key support
"""
import ssl
from typing import Union, Optional, List

import aiohttp
import certifi
from pydantic import BaseModel, Field, ValidationError
from aiplanet_platform.core.config import get_settings
from fastapi import Header, HTTPException, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.api_key import APIKey
from aiplanet_platform.models.user import User
from aiplanet_platform.services.api_key_service import APIKeyService
from aiplanet_platform.core.rate_limiter import rate_limiter
from aiplanet_platform.core.security import get_current_user, oauth2_scheme

# Add this new security scheme for API keys
api_key_scheme = HTTPBearer(scheme_name="API Key", auto_error=False)


class APIKeyAuth:
    """API Key authentication class"""

    def __init__(self, required_scopes: Optional[List[str]] = None):
        self.required_scopes = required_scopes or []

    async def __call__(
        self,
        authorization: Optional[HTTPAuthorizationCredentials] = Depends(api_key_scheme),
        x_api_key: Optional[str] = Header(None, description="API Key"),
        db: Session = Depends(get_db),
    ) -> APIKey:
        """
        Authenticate using API key from Authorization header or X-API-Key header.

        Args:
            authorization: Bearer token from Authorization header
            x_api_key: API key from X-API-Key header
            db: Database session

        Returns:
            APIKey object if authentication successful

        Raises:
            HTTPException: If authentication fails
        """
        # Try to get API key from either header
        api_key_value = None

        if authorization and authorization.scheme.lower() == "bearer":
            api_key_value = authorization.credentials
        elif x_api_key:
            api_key_value = x_api_key

        if not api_key_value:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key required. Provide via Authorization: Bearer <key> or X-API-Key header",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Authenticate the API key
        api_key_service = APIKeyService(db)
        api_key = api_key_service.authenticate_api_key(api_key_value)

        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired API key",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Check rate limits
        if not rate_limiter.is_allowed(api_key):
            rate_info = rate_limiter.get_rate_limit_info(api_key)
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded",
                headers={
                    "X-RateLimit-Limit-Minute": str(api_key.rate_limit_per_minute),
                    "X-RateLimit-Remaining-Minute": str(
                        max(
                            0,
                            api_key.rate_limit_per_minute - rate_info["minute"]["used"],
                        )
                    ),
                    "X-RateLimit-Reset-Minute": str(
                        int(rate_info["minute"]["reset_at"].timestamp())
                    ),
                },
            )

        # Check required scopes
        if self.required_scopes:
            for scope in self.required_scopes:
                if not api_key.has_scope(scope):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Insufficient permissions. Required scope: {scope}",
                    )

        return api_key


def create_api_key_auth(*required_scopes: str):
    """
    Create an API key authentication dependency with required scopes.

    Args:
        *required_scopes: Required scopes for the endpoint

    Returns:
        APIKeyAuth dependency
    """
    return APIKeyAuth(required_scopes=list(required_scopes))


async def get_current_user_or_api_key(
    # Try JWT first
    request: Request,
    token: Optional[str] = Depends(oauth2_scheme),
    # Try API key second
    authorization: Optional[HTTPAuthorizationCredentials] = Depends(api_key_scheme),
    x_api_key: Optional[str] = Header(None),
    db: Session = Depends(get_db),
) -> Union[User, APIKey]:
    """
    Get current user from JWT or API key.
    Supports both user authentication and third-party API access.
    Now includes rate limiting for both authentication methods.

    Returns:
        User object (for JWT auth) or APIKey object (for API key auth)
    """
    # Try JWT authentication first
    if token:
        try:
            user = await get_current_user(request=request, token=token, db=db)

            # Add rate limiting methods if they don't exist (for users without migration)
            if not hasattr(user, "get_user_identifier"):
                user.get_user_identifier = lambda: f"user_{user.id}"
            if not hasattr(user, "has_scope"):
                user.has_scope = (
                    lambda scope: True
                )  # Default: all permissions for backward compatibility
            if not hasattr(user, "rate_limit_per_minute"):
                user.rate_limit_per_minute = 100  # Default rate limit
            if not hasattr(user, "rate_limit_per_hour"):
                user.rate_limit_per_hour = 1000
            if not hasattr(user, "rate_limit_per_day"):
                user.rate_limit_per_day = 10000
            if not hasattr(user, "total_requests"):
                user.total_requests = 0

            # Apply rate limiting to JWT users (only if they have rate limit fields)
            if (
                hasattr(user, "rate_limit_per_minute")
                and user.rate_limit_per_minute is not None
            ):
                if not rate_limiter.is_allowed(user):
                    rate_info = rate_limiter.get_rate_limit_info(user)
                    raise HTTPException(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        detail="Rate limit exceeded",
                        headers={
                            "X-RateLimit-Limit-Minute": str(user.rate_limit_per_minute),
                            "X-RateLimit-Remaining-Minute": str(
                                max(
                                    0,
                                    user.rate_limit_per_minute
                                    - rate_info["minute"]["used"],
                                )
                            ),
                            "X-RateLimit-Reset-Minute": str(
                                int(rate_info["minute"]["reset_at"].timestamp())
                                if rate_info["minute"]["reset_at"]
                                else 0
                            ),
                            "X-Auth-Type": "jwt",
                        },
                    )

            # Store user in request state for middleware
            request.state.user = user
            request.state.auth_type = "jwt"
            return user

        except HTTPException:
            pass  # Fall through to API key auth

    # Try API key authentication
    api_key_value = None
    if authorization and authorization.scheme.lower() == "bearer":
        api_key_value = authorization.credentials
    elif x_api_key:
        api_key_value = x_api_key

    if api_key_value:
        api_key_service = APIKeyService(db)
        api_key = api_key_service.authenticate_api_key(api_key_value)

        if api_key:
            # Check rate limits for API keys
            if not rate_limiter.is_allowed(api_key):
                rate_info = rate_limiter.get_rate_limit_info(api_key)
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Rate limit exceeded",
                    headers={
                        "X-RateLimit-Limit-Minute": str(api_key.rate_limit_per_minute),
                        "X-RateLimit-Remaining-Minute": str(
                            max(
                                0,
                                api_key.rate_limit_per_minute
                                - rate_info["minute"]["used"],
                            )
                        ),
                        "X-RateLimit-Reset-Minute": str(
                            int(rate_info["minute"]["reset_at"].timestamp())
                            if rate_info["minute"]["reset_at"]
                            else 0
                        ),
                        "X-Auth-Type": "api_key",
                    },
                )

            # Store API key in request state for middleware
            request.state.api_key = api_key
            request.state.auth_type = "api_key"
            return api_key

    # Neither authentication method worked
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Valid authentication required (JWT token or API key)",
        headers={"WWW-Authenticate": "Bearer"},
    )


def create_mixed_auth_dependency(*required_scopes: str):
    """
    Create a dependency that supports both JWT and API key auth with required scopes.
    Now includes rate limiting for both authentication methods.

    Args:
        *required_scopes: Required scopes

    Returns:
        Dependency function
    """

    async def mixed_auth_dependency(
        auth: Union[User, APIKey] = Depends(get_current_user_or_api_key)
    ) -> Union[User, APIKey]:
        return require_scopes_for_auth(list(required_scopes), auth)

    return mixed_auth_dependency


def get_auth_context(auth: Union[User, APIKey]) -> dict:
    """
    Get authentication context information.
    Updated to reflect actual user scopes instead of wildcard.

    Args:
        auth: Authenticated User or APIKey

    Returns:
        Dictionary with auth context
    """
    if isinstance(auth, User):
        # Get user scopes safely
        scopes = getattr(auth, "scopes", ["*"])

        return {
            "auth_type": "jwt",
            "user_id": str(auth.id),
            "email": auth.email,
            "organization_id": str(auth.organization_id),
            "scopes": scopes,
            "rate_limits": {
                "per_minute": getattr(auth, "rate_limit_per_minute", 100),
                "per_hour": getattr(auth, "rate_limit_per_hour", 5000),
                "per_day": getattr(auth, "rate_limit_per_day", 50000),
            },
        }
    elif isinstance(auth, APIKey):
        return {
            "auth_type": "api_key",
            "api_key_id": str(auth.id),
            "api_key_name": auth.name,
            "user_id": str(auth.user_id),
            "organization_id": str(auth.organization_id),
            "scopes": auth.scopes,
            "rate_limits": {
                "per_minute": auth.rate_limit_per_minute,
                "per_hour": auth.rate_limit_per_hour,
                "per_day": auth.rate_limit_per_day,
            },
        }
    else:
        return {"auth_type": "unknown", "error": "Invalid authentication object"}


def require_scopes_for_auth(
    required_scopes: List[str],
    auth: Union[User, APIKey] = Depends(get_current_user_or_api_key),
):
    """
    Check if the authenticated entity (User or APIKey) has required scopes.
    Now properly checks scopes for both users and API keys.

    Args:
        required_scopes: List of required scopes
        auth: Authenticated User or APIKey

    Returns:
        The auth object if authorized

    Raises:
        HTTPException: If insufficient permissions
    """
    if isinstance(auth, User):
        # Check user scopes (with backward compatibility)
        if "*" in auth.scopes:
            # User has wildcard scope, allow all
            return auth
        elif set(required_scopes).issubset(set(auth.scopes)):
            # User has all required scopes
            return auth
        else:
            # If user doesn't have scope method, assume they have all permissions (backward compatibility)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions",
                headers={"X-Auth-Type": "jwt"},
            )
    elif isinstance(auth, APIKey):
        # Check API key scopes
        for scope in required_scopes:
            if not auth.has_scope(scope):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required scope: {scope}",
                    headers={"X-Auth-Type": "api_key"},
                )
        return auth
    else:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication"
        )


async def require_api_key_auth(
    authorization: Optional[HTTPAuthorizationCredentials] = Depends(api_key_scheme),
    x_api_key: Optional[str] = Header(None),
    db: Session = Depends(get_db),
) -> APIKey:
    """
    Require API key authentication only (no JWT support).
    Use this for endpoints that should only be accessible to third-party applications.
    """
    auth = APIKeyAuth()
    return await auth(authorization=authorization, x_api_key=x_api_key, db=db)


def is_user_auth(auth: Union[User, APIKey]) -> bool:
    """Check if authentication is from a user (JWT)"""
    return isinstance(auth, User)


def is_api_key_auth(auth: Union[User, APIKey]) -> bool:
    """Check if authentication is from an API key"""
    return isinstance(auth, APIKey)


def get_organization_id(auth: Union[User, APIKey]) -> str:
    """Get organization ID from either User or APIKey"""
    if isinstance(auth, User):
        return auth.organization_id
    elif isinstance(auth, APIKey):
        return auth.organization_id
    else:
        raise ValueError("Invalid authentication object")


def get_user_id(auth: Union[User, APIKey]) -> str:
    """Get user ID from either User or APIKey"""
    if isinstance(auth, User):
        return auth.id
    elif isinstance(auth, APIKey):
        return auth.user_id
    else:
        raise ValueError("Invalid authentication object")


def get_rate_limit_headers_for_auth(auth: Union[User, APIKey]) -> dict:
    """
    Get rate limit headers for any authentication type.
    Fixed to handle missing rate limit fields and datetime serialization.

    Args:
        auth: Authenticated User or APIKey

    Returns:
        Dictionary of headers to add to the response
    """
    try:
        # Only attempt rate limit info if the auth object supports it
        if (
            hasattr(auth, "rate_limit_per_minute")
            and getattr(auth, "rate_limit_per_minute") is not None
        ):
            rate_info = rate_limiter.get_rate_limit_info(auth)
        else:
            # Provide default rate info for objects without rate limiting
            rate_info = {
                "minute": {"limit": 100, "used": 0, "remaining": 100, "reset_at": None},
                "hour": {"limit": 5000, "used": 0, "remaining": 5000, "reset_at": None},
                "day": {
                    "limit": 50000,
                    "used": 0,
                    "remaining": 50000,
                    "reset_at": None,
                },
            }

        if isinstance(auth, User):
            minute_limit = getattr(auth, "rate_limit_per_minute", 100)
            hour_limit = getattr(auth, "rate_limit_per_hour", 5000)
            day_limit = getattr(auth, "rate_limit_per_day", 50000)
            auth_id = f"user_{auth.id}"
            total_requests = getattr(auth, "total_requests", 0)
        else:  # APIKey
            minute_limit = auth.rate_limit_per_minute
            hour_limit = auth.rate_limit_per_hour
            day_limit = auth.rate_limit_per_day
            auth_id = auth.key_prefix
            total_requests = auth.total_requests

        headers = {
            # Minute window
            "X-RateLimit-Limit-Minute": str(minute_limit),
            "X-RateLimit-Remaining-Minute": str(
                max(0, minute_limit - rate_info["minute"]["used"])
            ),
            # Hour window
            "X-RateLimit-Limit-Hour": str(hour_limit),
            "X-RateLimit-Remaining-Hour": str(
                max(0, hour_limit - rate_info["hour"]["used"])
            ),
            # Day window
            "X-RateLimit-Limit-Day": str(day_limit),
            "X-RateLimit-Remaining-Day": str(
                max(0, day_limit - rate_info["day"]["used"])
            ),
            # Auth info
            "X-Auth-ID": auth_id,
            "X-Total-Requests": str(total_requests),
            "X-Auth-Type": "jwt" if isinstance(auth, User) else "api_key",
        }

        # Add reset timestamps if available (with safe timestamp conversion)
        if rate_info["minute"]["reset_at"]:
            headers["X-RateLimit-Reset-Minute"] = str(
                int(rate_info["minute"]["reset_at"].timestamp())
            )

        if rate_info["hour"]["reset_at"]:
            headers["X-RateLimit-Reset-Hour"] = str(
                int(rate_info["hour"]["reset_at"].timestamp())
            )

        if rate_info["day"]["reset_at"]:
            headers["X-RateLimit-Reset-Day"] = str(
                int(rate_info["day"]["reset_at"].timestamp())
            )

        return headers

    except Exception as e:
        # Log the error but don't fail the request
        print(f"Warning: Could not generate rate limit headers: {e}")
        return {
            "X-Auth-Type": "jwt" if isinstance(auth, User) else "api_key",
            "X-Auth-ID": f"user_{auth.id}"
            if isinstance(auth, User)
            else getattr(auth, "key_prefix", "unknown"),
        }


class VerifyHumanResponse(BaseModel):
    success: bool = Field(..., description="Whether the token is valid")
    challenge_ts: Optional[str] = Field(None, description="Timestamp of the challenge")
    hostname: Optional[str] = Field(None, description="Hostname of the site")
    error_codes: Optional[List[str]] = Field(None, description="Error codes")


async def verify_human(request: Request) -> VerifyHumanResponse:
    try:
        settings = get_settings()
        verify_url = "https://challenges.cloudflare.com/turnstile/v0/siteverify"

        form_data = await request.form()
        cf_turnstile_response = form_data.get("cf-turnstile-response")

        if not settings.ENVIRONMENT == "production":
            return VerifyHumanResponse(
                success=True,
                challenge_ts=None,
                hostname=None,
                error_codes=None,
            )

        # Verify token with turnstile
        async with aiohttp.ClientSession() as session:
            data = aiohttp.FormData()
            data.add_field("secret", settings.TURNSTILE_SECRET_KEY)
            data.add_field("response", cf_turnstile_response)

            ssl_context = ssl.create_default_context(cafile=certifi.where())

            async with session.post(
                verify_url, data=data, ssl_context=ssl_context
            ) as response:
                if response.status != 200:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid token {cf_turnstile_response}",
                    )

                data = VerifyHumanResponse.model_validate(await response.json())
                if not data.success:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid token {cf_turnstile_response}",
                    )
                return data

    except HTTPException as e:
        raise e
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid token"
        ) from e

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error verifying token: {str(e)}",
        ) from e
