# aiplanet_platform/core/rate_limiter.py
"""
Enhanced rate limiting for both API keys and JWT users - FIXED to prevent double counting
"""
from typing import Dict, Union
from datetime import datetime, timedelta


class RateLimiter:
    """In-memory rate limiter for API keys and JWT users"""

    def __init__(self):
        self._windows: Dict[str, Dict[str, Dict]] = {}

    def _get_auth_info(self, auth: Union[str, object]) -> tuple[str, int, int, int]:
        """
        Extract auth info from either User object, APIKey object, or key prefix string.

        Args:
            auth: Either User object, APIKey object, or key prefix string

        Returns:
            tuple: (identifier, rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day)
        """
        if isinstance(auth, str):
            # It's a key prefix string, use default limits
            return (
                auth,
                100,  # default minute limit
                1000,  # default hour limit
                10000,  # default day limit
            )
        elif hasattr(auth, "key_prefix"):
            # It's an APIKey object
            return (
                auth.key_prefix,
                auth.rate_limit_per_minute,
                auth.rate_limit_per_hour,
                auth.rate_limit_per_day,
            )
        elif hasattr(auth, "get_user_identifier"):
            # It's a User object
            return (
                auth.get_user_identifier(),
                getattr(auth, "rate_limit_per_minute", 100),
                getattr(auth, "rate_limit_per_hour", 1000),
                getattr(auth, "rate_limit_per_day", 10000),
            )
        else:
            # Fallback for unknown objects - try to extract common attributes
            identifier = getattr(auth, "id", str(auth))
            return (
                f"unknown_{identifier}",
                100,  # default limits
                1000,
                10000,
            )

    def check_rate_limit(self, auth: Union[str, object]) -> bool:
        """
        Check if an auth entity is allowed to make a request WITHOUT decrementing counters.
        Use this for checking without consuming a request.

        Args:
            auth: User object, APIKey object, or key prefix string

        Returns:
            True if allowed, False if rate limited
        """
        identifier, minute_limit, hour_limit, day_limit = self._get_auth_info(auth)

        now = datetime.utcnow()

        # Initialize windows for this identifier if not exists
        if identifier not in self._windows:
            return True  # First request is always allowed

        windows = self._windows[identifier]

        # Check and reset windows if needed
        self._reset_expired_windows(windows, now)

        # Check all rate limits WITHOUT incrementing
        if windows["minute"]["count"] >= minute_limit:
            return False
        if windows["hour"]["count"] >= hour_limit:
            return False
        if windows["day"]["count"] >= day_limit:
            return False

        return True

    def is_allowed(self, auth: Union[str, object]) -> bool:
        """
        Check if an auth entity is allowed to make a request AND decrement counters.
        This consumes a request from the rate limit.

        Args:
            auth: User object, APIKey object, or key prefix string

        Returns:
            True if allowed, False if rate limited
        """
        identifier, minute_limit, hour_limit, day_limit = self._get_auth_info(auth)

        now = datetime.utcnow()

        # Initialize windows for this identifier if not exists
        if identifier not in self._windows:
            self._windows[identifier] = {
                "minute": {"count": 0, "start": now},
                "hour": {"count": 0, "start": now},
                "day": {"count": 0, "start": now},
            }

        windows = self._windows[identifier]

        # Check and reset windows if needed
        self._reset_expired_windows(windows, now)

        # Check all rate limits BEFORE incrementing
        if windows["minute"]["count"] >= minute_limit:
            return False
        if windows["hour"]["count"] >= hour_limit:
            return False
        if windows["day"]["count"] >= day_limit:
            return False

        # Only increment counters if the request is allowed
        windows["minute"]["count"] += 1
        windows["hour"]["count"] += 1
        windows["day"]["count"] += 1

        # Update last used timestamp for the auth object if possible
        if hasattr(auth, "last_used_at"):
            auth.last_used_at = now
        if hasattr(auth, "total_requests"):
            auth.total_requests += 1

        return True

    def consume_request(self, auth: Union[str, object]) -> bool:
        """
        Consume a request from the rate limit (decrement counters).
        Only call this after checking with check_rate_limit().

        Args:
            auth: User object, APIKey object, or key prefix string

        Returns:
            True if consumption was successful, False if rate limited
        """
        identifier, minute_limit, hour_limit, day_limit = self._get_auth_info(auth)

        now = datetime.utcnow()

        # Initialize windows for this identifier if not exists
        if identifier not in self._windows:
            self._windows[identifier] = {
                "minute": {"count": 0, "start": now},
                "hour": {"count": 0, "start": now},
                "day": {"count": 0, "start": now},
            }

        windows = self._windows[identifier]

        # Check and reset windows if needed
        self._reset_expired_windows(windows, now)

        # Check all rate limits BEFORE incrementing
        if windows["minute"]["count"] >= minute_limit:
            return False
        if windows["hour"]["count"] >= hour_limit:
            return False
        if windows["day"]["count"] >= day_limit:
            return False

        # Increment counters
        windows["minute"]["count"] += 1
        windows["hour"]["count"] += 1
        windows["day"]["count"] += 1

        # Update last used timestamp for the auth object if possible
        if hasattr(auth, "last_used_at"):
            auth.last_used_at = now
        if hasattr(auth, "total_requests"):
            auth.total_requests += 1

        return True

    def get_rate_limit_info(self, auth: Union[str, object]) -> dict:
        """
        Get rate limit information for an auth entity.

        Args:
            auth: User object, APIKey object, or key prefix string

        Returns:
            Dictionary with rate limit info
        """
        identifier, minute_limit, hour_limit, day_limit = self._get_auth_info(auth)

        if identifier not in self._windows:
            now = datetime.utcnow()
            return {
                "minute": {
                    "limit": minute_limit,
                    "used": 0,
                    "remaining": minute_limit,
                    "reset_at": now + timedelta(minutes=1),
                },
                "hour": {
                    "limit": hour_limit,
                    "used": 0,
                    "remaining": hour_limit,
                    "reset_at": now + timedelta(hours=1),
                },
                "day": {
                    "limit": day_limit,
                    "used": 0,
                    "remaining": day_limit,
                    "reset_at": now + timedelta(days=1),
                },
            }

        windows = self._windows[identifier]
        now = datetime.utcnow()

        # Reset expired windows before returning info
        self._reset_expired_windows(windows, now)

        return {
            "minute": {
                "limit": minute_limit,
                "used": windows["minute"]["count"],
                "remaining": max(0, minute_limit - windows["minute"]["count"]),
                "reset_at": windows["minute"]["start"] + timedelta(minutes=1),
            },
            "hour": {
                "limit": hour_limit,
                "used": windows["hour"]["count"],
                "remaining": max(0, hour_limit - windows["hour"]["count"]),
                "reset_at": windows["hour"]["start"] + timedelta(hours=1),
            },
            "day": {
                "limit": day_limit,
                "used": windows["day"]["count"],
                "remaining": max(0, day_limit - windows["day"]["count"]),
                "reset_at": windows["day"]["start"] + timedelta(days=1),
            },
        }

    def get_remaining_requests(self, auth: Union[str, object]) -> dict:
        """
        Get remaining requests for all time windows.

        Args:
            auth: User object, APIKey object, or key prefix string

        Returns:
            Dictionary with remaining requests
        """
        info = self.get_rate_limit_info(auth)
        return {
            "minute": info["minute"]["remaining"],
            "hour": info["hour"]["remaining"],
            "day": info["day"]["remaining"],
        }

    def reset_limits(self, auth: Union[str, object]) -> None:
        """
        Reset rate limits for an auth entity.

        Args:
            auth: User object, APIKey object, or key prefix string
        """
        identifier, _, _, _ = self._get_auth_info(auth)

        if identifier in self._windows:
            del self._windows[identifier]

    def _reset_expired_windows(self, windows: dict, now: datetime) -> None:
        """
        Reset expired time windows.

        Args:
            windows: Windows dictionary for a specific identifier
            now: Current datetime
        """
        # Reset minute window
        if now - windows["minute"]["start"] >= timedelta(minutes=1):
            windows["minute"] = {"count": 0, "start": now}

        # Reset hour window
        if now - windows["hour"]["start"] >= timedelta(hours=1):
            windows["hour"] = {"count": 0, "start": now}

        # Reset day window
        if now - windows["day"]["start"] >= timedelta(days=1):
            windows["day"] = {"count": 0, "start": now}

    def get_stats(self) -> dict:
        """
        Get overall rate limiter statistics.

        Returns:
            Dictionary with statistics
        """
        return {
            "total_tracked_entities": len(self._windows),
            "active_windows": sum(
                1
                for windows in self._windows.values()
                if any(w["count"] > 0 for w in windows.values())
            ),
        }

    def cleanup_expired_windows(self):
        """
        Clean up expired rate limit windows to free memory.
        This should be called periodically.
        """
        now = datetime.utcnow()
        keys_to_remove = []

        for key_id, windows in self._windows.items():
            # If all windows are expired and have no recent activity, remove the key
            all_expired = all(
                now > window["reset_at"] and window["count"] == 0
                for window in windows.values()
            )

            if all_expired:
                keys_to_remove.append(key_id)

        for key_id in keys_to_remove:
            del self._windows[key_id]

        return len(keys_to_remove)


# Create a global instance
rate_limiter = RateLimiter()
