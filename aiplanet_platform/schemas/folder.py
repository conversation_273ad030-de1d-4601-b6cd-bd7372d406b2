"""
Pydantic schemas for folder
"""
from uuid import UUID
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class FolderBase(BaseModel):
    """Base schema for folder"""

    name: str = Field(
        ..., description="Name of the folder", min_length=1, max_length=255
    )
    description: Optional[str] = Field(None, description="Description of the folder")
    # Add your custom fields here


class FolderCreate(FolderBase):
    """Schema for creating a folder"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Folder",
                "description": "This is an example folder",
                # Add example values for other fields
            }
        }


class FolderUpdate(BaseModel):
    """Schema for updating a folder"""

    name: Optional[str] = Field(
        None, description="Name of the folder", min_length=1, max_length=255
    )
    description: Optional[str] = Field(None, description="Description of the folder")
    # Add your custom fields here

    chunking_config: Optional[dict] = Field(
        None, description="Chunking configuration for the folder"
    )
    embedding_config: Optional[dict] = Field(
        None, description="Embedding configuration for the folder"
    )
    vector_db_config: Optional[dict] = Field(
        None, description="Vector database configuration for the folder"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Updated Folder",
                "description": "This is an updated folder",
                # Add example values for other fields
            }
        }


class FolderResponse(FolderBase):
    """Schema for folder response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "name": "Example Folder",
                "description": "This is an example folder",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                # Add example values for other fields
            }
        }


class FolderList(BaseModel):
    """Schema for paginated list of folders"""

    items: List[FolderResponse] = Field(..., description="List of folders")
    total: int = Field(..., description="Total number of folders")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": 1,
                        "name": "Example Folder 1",
                        "description": "This is an example folder",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": 2,
                        "name": "Example Folder 2",
                        "description": "This is another example folder",
                        "created_at": "2023-01-03T00:00:00Z",
                        "updated_at": "2023-01-04T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
