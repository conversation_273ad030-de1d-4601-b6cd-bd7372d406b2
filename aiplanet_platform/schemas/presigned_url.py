"""
Pydantic schemas for presigned URL operations
"""
from typing import Optional
from pydantic import BaseModel, Field


class PresignedURLRequest(BaseModel):
    """Schema for requesting a presigned URL"""

    object_name: str = Field(
        ...,
        description="Name of the object/file to upload",
        min_length=1,
        max_length=255,
    )
    expiration_minutes: Optional[int] = Field(
        15, description="URL expiration time in minutes", ge=1, le=1440  # Max 24 hours
    )
    content_type: Optional[str] = Field(
        "application/pdf", description="Content type of the file", max_length=100
    )

    class Config:
        json_schema_extra = {
            "example": {
                "object_name": "document_123.pdf",
                "expiration_minutes": 15,
                "content_type": "application/pdf",
            }
        }


class PresignedURLResponse(BaseModel):
    """Schema for presigned URL response"""

    presigned_url: str = Field(
        ..., description="The generated presigned URL for file upload"
    )
    provider: str = Field(..., description="Storage provider used (aws or azure)")
    bucket_name: Optional[str] = Field(
        None, description="AWS S3 bucket name (only for AWS)"
    )
    container_name: Optional[str] = Field(
        None, description="Azure container name (only for Azure)"
    )
    object_name: str = Field(..., description="Name of the object/file")
    blob_name: Optional[str] = Field(
        None, description="Azure blob name (only for Azure)"
    )
    expires_in_minutes: int = Field(..., description="URL expiration time in minutes")

    class Config:
        json_schema_extra = {
            "example": {
                "presigned_url": "https://example-bucket.s3.amazonaws.com/document_123.pdf?...",
                "provider": "aws",
                "bucket_name": "my-bucket",
                "object_name": "document_123.pdf",
                "expires_in_minutes": 15,
            }
        }
