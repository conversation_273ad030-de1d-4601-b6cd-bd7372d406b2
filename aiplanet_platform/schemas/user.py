"""
Pydantic schemas for user
"""

from uuid import UUID
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field
from aiplanet_platform.constants.user import UserType


class UserBase(BaseModel):
    """Base schema for user"""

    name: str = Field(..., description="Name of the user", min_length=1, max_length=255)
    bio: Optional[str] = Field(None, description="Description of the user")
    email: str = Field(
        ..., description="Email of the user", min_length=1, max_length=255
    )
    password: str = Field(
        ..., description="Password of the user", min_length=1, max_length=255
    )
    role: str = Field(
        UserType.NORMAL, description="Role of the user", min_length=1, max_length=255
    )
    # Add your custom fields here


class UserCreate(UserBase):
    """Schema for creating a user"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example User",
                "bio": "This is an example user",
                "email": "<EMAIL>",
                "password": "examplepassword",
                "role": "normal",
                # Add example values for other fields
            }
        }


class UserUpdate(UserBase):
    """Schema for updating a user"""

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Updated User",
                "bio": "This is an updated user",
                "email": "<EMAIL>",
                "password": "updatedpassword",
                "role": "normal",
                "status": "active",
                "organization_id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                # Add example values for other fields
            }
        }


class UserProfileUpdate(BaseModel):
    """Schema for safe user profile updates (only name and bio)"""

    name: Optional[str] = Field(
        None, description="Name of the user", min_length=1, max_length=255
    )
    bio: Optional[str] = Field(None, description="Description of the user")

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Updated Name",
                "bio": "Updated bio description",
            }
        }


class UserResponse(UserBase):
    """Schema for user response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    password: Optional[str] = Field(None, description="Password of the user")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                "name": "Example User",
                "email": "<EMAIL>",
                "bio": "This is an example user",
                "role": "normal",
                "status": "active",
                "organization_id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                "organization": {
                    "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                    "name": "Example Organization",
                    "description": "This is an example organization",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                "description": "This is an example user",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                # Add example values for other fields
            }
        }


class UserList(BaseModel):
    """Schema for paginated list of users"""

    items: List[UserResponse] = Field(..., description="List of users")
    total: int = Field(..., description="Total number of users")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "name": "Example User",
                        "email": "<EMAIL>",
                        "bio": "This is an example user",
                        "role": "normal",
                        "status": "active",
                        "organization_id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "organization": {
                            "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                            "name": "Example Organization",
                            "description": "This is an example organization",
                            "created_at": "2023-01-01T00:00:00Z",
                            "updated_at": "2023-01-02T00:00:00Z",
                            "is_deleted": False,
                        },
                        "description": "This is an example user",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        # Add example values for other fields
                    },
                    {
                        "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "name": "Example User",
                        "email": "<EMAIL>",
                        "bio": "This is an example user",
                        "role": "normal",
                        "status": "active",
                        "organization_id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "organization": {
                            "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                            "name": "Example Organization",
                            "description": "This is an example organization",
                            "created_at": "2023-01-01T00:00:00Z",
                            "updated_at": "2023-01-02T00:00:00Z",
                            "is_deleted": False,
                        },
                        "description": "This is an example user",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        # Add example values for other fields
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


class PasswordUpdate(BaseModel):
    """Schema for password updates"""

    current_password: str = Field(..., description="Current password for verification")
    new_password: str = Field(..., description="New password", min_length=6)

    class Config:
        json_schema_extra = {
            "example": {
                "current_password": "current_password",
                "new_password": "new_password",
            }
        }
