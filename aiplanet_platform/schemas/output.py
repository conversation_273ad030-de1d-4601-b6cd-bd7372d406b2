"""
Pydantic schemas for output
"""
from datetime import datetime
from typing import List, Optional, Union
from uuid import UUID

from autogen_core import ComponentModel
from pydantic import BaseModel, Field


class OutputBase(BaseModel):
    """Base schema for output"""

    component: Union[ComponentModel, dict] = Field(
        ..., description="Component of the tool"
    )
    organization_id: Optional[UUID] = Field(None, description="Organization ID")
    # Add your custom fields here


class OutputCreate(OutputBase):
    """Schema for creating a output"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "component": {
                        "provider": "autogen_core.io.JSONOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "JSON output component for structured data output",
                        "label": "JSONOutput",
                        "config": {
                            "indent": 2,
                            "ensure_ascii": False,
                            "sort_keys": True,
                            "output_path": "output.json",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.MarkdownOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "Markdown output component for structured data output",
                        "label": "MarkdownOutput",
                        "config": {
                            "encoding": "utf-8",
                            "output_path": "output.md",
                            "include_toc": True,
                            "heading_level": 2,
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.TextOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "Text output component for structured data output",
                        "label": "TextOutput",
                        "config": {
                            "encoding": "utf-8",
                            "output_path": "output.txt",
                            "line_ending": "lf",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
            ]
        }


class OutputUpdate(OutputBase):
    """Schema for updating a output"""

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "component": {
                        "provider": "autogen_core.io.JSONOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "JSON output component for structured data output",
                        "label": "JSONOutput",
                        "config": {
                            "indent": 2,
                            "ensure_ascii": False,
                            "sort_keys": True,
                            "output_path": "output.json",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.MarkdownOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "Markdown output component for structured data output",
                        "label": "MarkdownOutput",
                        "config": {
                            "encoding": "utf-8",
                            "output_path": "output.md",
                            "include_toc": True,
                            "heading_level": 2,
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.TextOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "Text output component for structured data output",
                        "label": "TextOutput",
                        "config": {
                            "encoding": "utf-8",
                            "output_path": "output.txt",
                            "line_ending": "lf",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
            ]
        }


class OutputResponse(OutputBase):
    """Schema for output response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "examples": [
                {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "component": {
                        "provider": "autogen_core.io.TextOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "Text output component for structured data output",
                        "label": "TextOutput",
                        "config": {
                            "encoding": "utf-8",
                            "output_path": "output.txt",
                            "line_ending": "lf",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                {
                    "id": "123e4567-e89b-12d3-a456-426614174001",
                    "component": {
                        "provider": "autogen_core.io.JSONOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "JSON output component for structured data output",
                        "label": "JSONOutput",
                        "config": {
                            "indent": 2,
                            "ensure_ascii": False,
                            "sort_keys": True,
                            "output_path": "output.json",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                {
                    "id": "123e4567-e89b-12d3-a456-426614174002",
                    "component": {
                        "provider": "autogen_core.io.MarkdownOutput",
                        "component_type": "output",
                        "version": 1,
                        "component_version": 1,
                        "description": "Markdown output component for structured data output",
                        "label": "MarkdownOutput",
                        "config": {
                            "encoding": "utf-8",
                            "output_path": "output.md",
                            "include_toc": True,
                            "heading_level": 2,
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
            ]
        }


class OutputList(BaseModel):
    """Schema for paginated list of outputs"""

    items: List[OutputResponse] = Field(..., description="List of outputs")
    total: int = Field(..., description="Total number of outputs")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": 1,
                        "name": "Example Output 1",
                        "description": "This is an example output",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": 2,
                        "name": "Example Output 2",
                        "description": "This is another example output",
                        "created_at": "2023-01-03T00:00:00Z",
                        "updated_at": "2023-01-04T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
