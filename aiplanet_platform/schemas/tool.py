"""
Pydantic schemas for tool
"""

from datetime import datetime
from typing import List, Optional, Union
from uuid import UUID

from autogen_core import ComponentModel
from pydantic import BaseModel, Field


class ToolBase(BaseModel):
    """Base schema for tool"""

    component: Union[ComponentModel, dict] = Field(
        ..., description="Component of the tool"
    )
    organization_id: Optional[UUID] = Field(None, description="Organization ID")
    # Add your custom fields here


class ToolCreate(ToolBase):
    """Schema for creating a tool"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_core.tools.FunctionTool",
                    "component_type": "tool",
                    "version": 1,
                    "component_version": 1,
                    "description": "Create custom tools by wrapping standard Python functions.",
                    "label": "FunctionTool",
                    "config": {
                        "source_code": 'def tool_func(x: str):\n    return x+"_world"\n',
                        "name": "function_tool",
                        "description": "a function tool",
                        "global_imports": [],
                        "has_cancellation_support": False,
                    },
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
            }
        }


class ToolUpdate(ToolBase):
    """Schema for updating a tool"""

    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_core.tools.FunctionTool",
                    "component_type": "tool",
                    "version": 1,
                    "component_version": 1,
                    "description": "Create custom tools by wrapping standard Python functions.",
                    "label": "FunctionTool",
                    "config": {
                        "source_code": 'def tool_func(x: str):\n    return x+"_world"\n',
                        "name": "function_tool",
                        "description": "a function tool",
                        "global_imports": [],
                        "has_cancellation_support": False,
                    },
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
            }
        }


class ToolResponse(ToolBase):
    """Schema for tool response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    is_default: bool = Field(False, description="Whether this is a default system tool")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "component": {
                    "provider": "autogen_core.tools.FunctionTool",
                    "component_type": "tool",
                    "version": 1,
                    "component_version": 1,
                    "description": "Create custom tools by wrapping standard Python functions.",
                    "label": "FunctionTool",
                    "config": {
                        "source_code": 'def tool_func(x: str):\n    return x+"_world"\n',
                        "name": "function_tool",
                        "description": "a function tool",
                        "global_imports": [],
                        "has_cancellation_support": False,
                    },
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                "is_default": False,
                # Add example values for other fields
            }
        }


class ToolList(BaseModel):
    """Schema for paginated list of tools"""

    items: List[ToolResponse] = Field(..., description="List of tools")
    total: int = Field(..., description="Total number of tools")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "component": {
                            "provider": "autogen_core.tools.FunctionTool",
                            "component_type": "tool",
                            "version": 1,
                            "component_version": 1,
                            "description": "Create custom tools by wrapping standard Python functions.",
                            "label": "FunctionTool",
                            "config": {
                                "source_code": 'def tool_func(x: str):\n    return x+"_world"\n',
                                "name": "function_tool",
                                "description": "a function tool",
                                "global_imports": [],
                                "has_cancellation_support": False,
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        "is_default": False,
                    },
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "component": {
                            "provider": "autogen_core.tools.FunctionTool",
                            "component_type": "tool",
                            "version": 1,
                            "component_version": 1,
                            "description": "Create custom tools by wrapping standard Python functions.",
                            "label": "FunctionTool",
                            "config": {
                                "source_code": 'def tool_func(x: str):\n    return x+"_world"\n',
                                "name": "function_tool",
                                "description": "a function tool",
                                "global_imports": [],
                                "has_cancellation_support": False,
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        "is_default": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
