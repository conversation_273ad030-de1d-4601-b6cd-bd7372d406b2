"""
Pydantic schemas for run
"""
from datetime import datetime
from typing import List, Optional, Literal, Union, Dict

from pydantic import BaseModel, Field


class RunBase(BaseModel):
    """Base schema for run"""

    name: str = Field(..., description="Name of the run", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the run")
    # Add your custom fields here


class RunCreate(RunBase):
    """Schema for creating a run"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Run",
                "description": "This is an example run",
                # Add example values for other fields
            }
        }


class RunUpdate(BaseModel):
    """Schema for updating a run"""

    name: Optional[str] = Field(
        None, description="Name of the run", min_length=1, max_length=255
    )
    description: Optional[str] = Field(None, description="Description of the run")
    # Add your custom fields here

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Updated Run",
                "description": "This is an updated run",
                # Add example values for other fields
            }
        }


class RunResponse(RunBase):
    """Schema for run response"""

    id: int = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "name": "Example Run",
                "description": "This is an example run",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                # Add example values for other fields
            }
        }


class RunList(BaseModel):
    """Schema for paginated list of runs"""

    items: List[RunResponse] = Field(..., description="List of runs")
    total: int = Field(..., description="Total number of runs")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": 1,
                        "name": "Example Run 1",
                        "description": "This is an example run",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": 2,
                        "name": "Example Run 2",
                        "description": "This is another example run",
                        "created_at": "2023-01-03T00:00:00Z",
                        "updated_at": "2023-01-04T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }


class PromptInput(BaseModel):
    type: Literal["PromptInput"]
    content: str
    label: str = Field(default="Prompt")


class TextInput(BaseModel):
    type: Literal["TextInput"]
    content: str
    label: str = Field(default="Text")
    id: Optional[str] = Field(default=None, description="ID of the input")


class ChatInput(BaseModel):
    type: Literal["ChatInput"]
    content: str
    label: str = Field(default="Chat")
    id: Optional[str] = Field(default=None, description="ID of the input")


class ImageInput(BaseModel):
    type: Literal["ImageInput"]
    content: str
    label: str = Field(default="Image")
    id: Optional[str] = Field(default=None, description="ID of the input")


class AudioInput(BaseModel):
    type: Literal["AudioInput"]
    content: str
    label: str = Field(default="Audio")
    id: Optional[str] = Field(default=None, description="ID of the input")


class VideoInput(BaseModel):
    type: Literal["VideoInput"]
    content: str
    label: str = Field(default="Video")
    id: Optional[str] = Field(default=None, description="ID of the input")


class FileInput(BaseModel):
    type: Literal["FileInput"]
    content: Optional[str] = Field(
        default="", description="Base64 encoded file content (legacy)"
    )
    label: str = Field(default="File")
    file_type: str = Field(default="application/pdf")
    id: Optional[str] = Field(default=None, description="ID of the input")


class MultiFileInput(BaseModel):
    type: Literal["MultiFileInput"]
    content: List[FileInput]
    label: str = Field(default="Files")


class UrlType(BaseModel):
    url: str
    headers: Optional[Dict[str, str]]
    id: Optional[str] = Field(default=None, description="ID of the input")


class URLInput(BaseModel):
    type: Literal["URLInput"]
    content: UrlType
    label: str = Field(default="URL")
    id: Optional[str] = Field(default=None, description="ID of the input")


InputType = Union[
    TextInput,
    ImageInput,
    AudioInput,
    VideoInput,
    FileInput,
    MultiFileInput,
    ChatInput,
    URLInput,
]


class OutputType(BaseModel):
    type: Literal["outputType"]
    content: str
    label: str = Field(default="output")


class RunChatMessage(BaseModel):
    """Schema for chat message"""

    source: str = Field(..., description="Source of the message")
    inputs: List[InputType] = Field(..., description="Inputs to the message")

    class Config:
        json_schema_extra = {
            "example": {
                "source": "user",
                "inputs": [
                    {
                        "type": "TextInput",
                        "content": "Hello, how are you?",
                        "label": "User Input",
                    },
                    {
                        "type": "ImageInput",
                        "content": "https://example.com/image.jpg",
                        "label": "User Image",
                    },
                    {
                        "type": "AudioInput",
                        "content": "https://example.com/audio.mp3",
                    },
                    {
                        "type": "VideoInput",
                        "content": "https://example.com/video.mp4",
                    },
                    {
                        "type": "FileInput",
                        "content": "https://example.com/file.pdf",
                        "label": "Resume File",
                    },
                ],
            }
        }
