"""
Pydantic schemas for team
"""
from datetime import datetime
from typing import List, Optional, Union
from uuid import UUID

from autogen_core import ComponentModel
from pydantic import BaseModel, Field

from aiplanet_platform.schemas.agent import AgentResponse
from aiplanet_platform.schemas.model import ModelResponse
from aiplanet_platform.schemas.input import InputResponse
from aiplanet_platform.schemas.output import OutputResponse
from aiplanet_platform.schemas.termination_condition import TerminationConditionResponse


class TeamBase(BaseModel):
    """Base schema for team"""

    component: Union[ComponentModel, dict] = Field(
        ..., description="Component of the team"
    )
    organization_id: Optional[UUID] = Field(None, description="Organization ID")
    # Add your custom fields here
    team_agent_ids: Optional[List[UUID]] = Field(
        None, description="List of team agent IDs"
    )
    team_output_ids: Optional[List[UUID]] = Field(
        None, description="List of team output IDs"
    )
    team_input_ids: Optional[List[UUID]] = Field(
        None, description="List of team input IDs"
    )
    team_termination_condition_ids: Optional[List[UUID]] = Field(
        None, description="List of team termination condition IDs"
    )
    model_id: Optional[UUID] = Field(None, description="Model ID")


class TeamCreate(TeamBase):
    """Schema for creating a team"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "component": {
                        "provider": "autogen_agentchat.teams.RoundRobinGroupChat",
                        "component_type": "team",
                        "version": 1,
                        "component_version": 1,
                        "description": "A team that runs a group chat with participants taking turns in a round-robin fashion\n    to publish a message to all.",
                        "label": "RoundRobinGroupChat",
                        "config": {
                            "participants": [
                                {
                                    "provider": "autogen_agentchat.agents.AssistantAgent",
                                    "component_type": "agent",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "An agent that provides assistance with tool use.",
                                    "label": "AssistantAgent",
                                    "config": {
                                        "name": "assistant_agent",
                                        "model_client": {
                                            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                                            "component_type": "model",
                                            "version": 1,
                                            "component_version": 1,
                                            "description": "Chat completion client for OpenAI hosted models.",
                                            "label": "OpenAIChatCompletionClient",
                                            "config": {
                                                "model": "gpt-4",
                                                "api_key": "**********",
                                            },
                                        },
                                        "workbench": {
                                            "provider": "autogen_core.tools.StaticWorkbench",
                                            "component_type": "workbench",
                                            "version": 1,
                                            "component_version": 1,
                                            "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                                            "label": "StaticWorkbench",
                                            "config": {"tools": []},
                                        },
                                        "handoffs": [
                                            {
                                                "target": "user_proxy",
                                                "description": "Handoff to user_proxy.",
                                                "name": "transfer_to_user_proxy",
                                                "message": "Transferred to user_proxy, adopting the role of user_proxy immediately.",
                                            }
                                        ],
                                        "model_context": {
                                            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                            "component_type": "chat_completion_context",
                                            "version": 1,
                                            "component_version": 1,
                                            "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                                            "label": "UnboundedChatCompletionContext",
                                            "config": {},
                                        },
                                        "description": "an agent that helps the user",
                                        "system_message": "You are a helpful assistant.",
                                        "model_client_stream": False,
                                        "reflect_on_tool_use": False,
                                        "tool_call_summary_format": "{result}",
                                        "metadata": {},
                                    },
                                },
                                {
                                    "provider": "autogen_agentchat.agents.UserProxyAgent",
                                    "component_type": "agent",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "An agent that can represent a human user through an input function.",
                                    "label": "UserProxyAgent",
                                    "config": {
                                        "name": "user_proxy",
                                        "description": "a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent",
                                    },
                                },
                            ],
                            "max_turns": 10,
                            "emit_team_events": False,
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "team_agent_ids": [
                        "123e4567-e89b-12d3-a456-************",
                        "123e4567-e89b-12d3-a456-************",
                    ],
                    "team_input_ids": ["123e4567-e89b-12d3-a456-************"],
                    "team_output_ids": ["123e4567-e89b-12d3-a456-************"],
                    "team_termination_condition_ids": [
                        "123e4567-e89b-12d3-a456-************"
                    ],
                    "model_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_agentchat.teams.SelectorGroupChat",
                        "component_type": "team",
                        "version": 1,
                        "component_version": 1,
                        "description": "A group chat team that have participants takes turn to publish a message\n    to all, using a ChatCompletion model to select the next speaker after each message.",
                        "label": "SelectorGroupChat",
                        "config": {
                            "participants": [
                                {
                                    "provider": "autogen_agentchat.agents.AssistantAgent",
                                    "component_type": "agent",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "An agent that provides assistance with tool use.",
                                    "label": "AssistantAgent",
                                    "config": {
                                        "name": "assistant_agent",
                                        "model_client": {
                                            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                                            "component_type": "model",
                                            "version": 1,
                                            "component_version": 1,
                                            "description": "Chat completion client for OpenAI hosted models.",
                                            "label": "OpenAIChatCompletionClient",
                                            "config": {
                                                "model": "gpt-4",
                                                "api_key": "**********",
                                            },
                                        },
                                        "workbench": {
                                            "provider": "autogen_core.tools.StaticWorkbench",
                                            "component_type": "workbench",
                                            "version": 1,
                                            "component_version": 1,
                                            "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                                            "label": "StaticWorkbench",
                                            "config": {"tools": []},
                                        },
                                        "handoffs": [
                                            {
                                                "target": "user_proxy",
                                                "description": "Handoff to user_proxy.",
                                                "name": "transfer_to_user_proxy",
                                                "message": "Transferred to user_proxy, adopting the role of user_proxy immediately.",
                                            }
                                        ],
                                        "model_context": {
                                            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                            "component_type": "chat_completion_context",
                                            "version": 1,
                                            "component_version": 1,
                                            "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                                            "label": "UnboundedChatCompletionContext",
                                            "config": {},
                                        },
                                        "description": "an agent that helps the user",
                                        "system_message": "You are a helpful assistant.",
                                        "model_client_stream": False,
                                        "reflect_on_tool_use": False,
                                        "tool_call_summary_format": "{result}",
                                        "metadata": {},
                                    },
                                },
                                {
                                    "provider": "autogen_agentchat.agents.UserProxyAgent",
                                    "component_type": "agent",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "An agent that can represent a human user through an input function.",
                                    "label": "UserProxyAgent",
                                    "config": {
                                        "name": "user_proxy",
                                        "description": "a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent",
                                    },
                                },
                            ],
                            "model_client": {
                                "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                                "component_type": "model",
                                "version": 1,
                                "component_version": 1,
                                "description": "Chat completion client for OpenAI hosted models.",
                                "label": "OpenAIChatCompletionClient",
                                "config": {"model": "gpt-4", "api_key": "**********"},
                            },
                            "max_turns": 10,
                            "selector_prompt": "You are in a role play game. The",
                            "allow_repeated_speaker": False,
                            "max_selector_attempts": 3,
                            "emit_team_events": False,
                            "model_client_streaming": False,
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "team_agent_ids": [
                        "123e4567-e89b-12d3-a456-************",
                        "123e4567-e89b-12d3-a456-************",
                    ],
                    "team_input_ids": ["123e4567-e89b-12d3-a456-************"],
                    "team_output_ids": ["123e4567-e89b-12d3-a456-************"],
                    "team_termination_condition_ids": [
                        "123e4567-e89b-12d3-a456-************"
                    ],
                    "model_ids": "123e4567-e89b-12d3-a456-************",
                },
            ]
        }


class TeamUpdate(TeamBase):
    """Schema for updating a team"""

    # Add your custom fields here

    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_agentchat.teams.SelectorGroupChat",
                    "component_type": "team",
                    "version": 1,
                    "component_version": 1,
                    "description": "A group chat team that have participants takes turn to publish a message\n    to all, using a ChatCompletion model to select the next speaker after each message.",
                    "label": "SelectorGroupChat",
                    "config": {
                        "participants": [
                            {
                                "provider": "autogen_agentchat.agents.AssistantAgent",
                                "component_type": "agent",
                                "version": 1,
                                "component_version": 1,
                                "description": "An agent that provides assistance with tool use.",
                                "label": "AssistantAgent",
                                "config": {
                                    "name": "assistant_agent",
                                    "model_client": {
                                        "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                                        "component_type": "model",
                                        "version": 1,
                                        "component_version": 1,
                                        "description": "Chat completion client for OpenAI hosted models.",
                                        "label": "OpenAIChatCompletionClient",
                                        "config": {
                                            "model": "gpt-4",
                                            "api_key": "**********",
                                        },
                                    },
                                    "workbench": {
                                        "provider": "autogen_core.tools.StaticWorkbench",
                                        "component_type": "workbench",
                                        "version": 1,
                                        "component_version": 1,
                                        "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                                        "label": "StaticWorkbench",
                                        "config": {"tools": []},
                                    },
                                    "handoffs": [
                                        {
                                            "target": "user_proxy",
                                            "description": "Handoff to user_proxy.",
                                            "name": "transfer_to_user_proxy",
                                            "message": "Transferred to user_proxy, adopting the role of user_proxy immediately.",
                                        }
                                    ],
                                    "model_context": {
                                        "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                        "component_type": "chat_completion_context",
                                        "version": 1,
                                        "component_version": 1,
                                        "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                                        "label": "UnboundedChatCompletionContext",
                                        "config": {},
                                    },
                                    "description": "an agent that helps the user",
                                    "system_message": "You are a helpful assistant.",
                                    "model_client_stream": False,
                                    "reflect_on_tool_use": False,
                                    "tool_call_summary_format": "{result}",
                                    "metadata": {},
                                },
                            },
                            {
                                "provider": "autogen_agentchat.agents.UserProxyAgent",
                                "component_type": "agent",
                                "version": 1,
                                "component_version": 1,
                                "description": "An agent that can represent a human user through an input function.",
                                "label": "UserProxyAgent",
                                "config": {
                                    "name": "user_proxy",
                                    "description": "a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent",
                                },
                            },
                        ],
                        "model_client": {
                            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                            "component_type": "model",
                            "version": 1,
                            "component_version": 1,
                            "description": "Chat completion client for OpenAI hosted models.",
                            "label": "OpenAIChatCompletionClient",
                            "config": {"model": "gpt-4", "api_key": "**********"},
                        },
                        "max_turns": 10,
                        "selector_prompt": "You are in a role play game. The",
                        "allow_repeated_speaker": False,
                        "max_selector_attempts": 3,
                        "emit_team_events": False,
                        "model_client_streaming": False,
                    },
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
            }
        }


class TeamResponse(TeamBase):
    """Schema for team response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    is_deployed: Optional[bool] = Field(
        False, description="Whether the team is deployed"
    )
    # Include additional fields from your model here
    team_agent_ids: Optional[List[UUID]] = Field(
        [], description="List of team agent ids"
    )
    team_input_ids: Optional[List[UUID]] = Field(
        [], description="List of team input ids"
    )
    team_output_ids: Optional[List[UUID]] = Field(
        [], description="List of team output ids"
    )
    team_termination_condition_ids: Optional[List[UUID]] = Field(
        [], description="List of team termination condition ids"
    )
    agents: Optional[List[AgentResponse]] = Field([], description="List of team agents")
    team_inputs: Optional[List[InputResponse]] = Field(
        [], description="List of team inputs"
    )
    team_outputs: Optional[List[OutputResponse]] = Field(
        [], description="List of team outputs"
    )
    team_termination_conditions: Optional[List[TerminationConditionResponse]] = Field(
        [], description="List of team termination conditions"
    )
    model: Optional[ModelResponse] = Field(..., description="List of models")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "component": {
                    "provider": "autogen_agentchat.teams.SelectorGroupChat",
                    "component_type": "team",
                    "version": 1,
                    "component_version": 1,
                    "description": "A group chat team that have participants takes turn to publish a message\n    to all, using a ChatCompletion model to select the next speaker after each message.",
                    "label": "SelectorGroupChat",
                    "config": {
                        "participants": [
                            {
                                "provider": "autogen_agentchat.agents.AssistantAgent",
                                "component_type": "agent",
                                "version": 1,
                                "component_version": 1,
                                "description": "An agent that provides assistance with tool use.",
                                "label": "AssistantAgent",
                                "config": {
                                    "name": "assistant_agent",
                                    "model_client": {
                                        "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                                        "component_type": "model",
                                        "version": 1,
                                        "component_version": 1,
                                        "description": "Chat completion client for OpenAI hosted models.",
                                        "label": "OpenAIChatCompletionClient",
                                        "config": {
                                            "model": "gpt-4",
                                            "api_key": "**********",
                                        },
                                    },
                                    "workbench": {
                                        "provider": "autogen_core.tools.StaticWorkbench",
                                        "component_type": "workbench",
                                        "version": 1,
                                        "component_version": 1,
                                        "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                                        "label": "StaticWorkbench",
                                        "config": {"tools": []},
                                    },
                                    "handoffs": [
                                        {
                                            "target": "user_proxy",
                                            "description": "Handoff to user_proxy.",
                                            "name": "transfer_to_user_proxy",
                                            "message": "Transferred to user_proxy, adopting the role of user_proxy immediately.",
                                        }
                                    ],
                                    "model_context": {
                                        "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                        "component_type": "chat_completion_context",
                                        "version": 1,
                                        "component_version": 1,
                                        "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                                        "label": "UnboundedChatCompletionContext",
                                        "config": {},
                                    },
                                    "description": "an agent that helps the user",
                                    "system_message": "You are a helpful assistant.",
                                    "model_client_stream": False,
                                    "reflect_on_tool_use": False,
                                    "tool_call_summary_format": "{result}",
                                    "metadata": {},
                                },
                            },
                            {
                                "provider": "autogen_agentchat.agents.UserProxyAgent",
                                "component_type": "agent",
                                "version": 1,
                                "component_version": 1,
                                "description": "An agent that can represent a human user through an input function.",
                                "label": "UserProxyAgent",
                                "config": {
                                    "name": "user_proxy",
                                    "description": "a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent",
                                },
                            },
                        ],
                        "model_client": {
                            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                            "component_type": "model",
                            "version": 1,
                            "component_version": 1,
                            "description": "Chat completion client for OpenAI hosted models.",
                            "label": "OpenAIChatCompletionClient",
                            "config": {"model": "gpt-4", "api_key": "**********"},
                        },
                        "max_turns": 10,
                        "selector_prompt": "You are in a role play game. The",
                        "allow_repeated_speaker": False,
                        "max_selector_attempts": 3,
                        "emit_team_events": False,
                        "model_client_streaming": False,
                    },
                },
                "model_ids": "123e4567-e89b-12d3-a456-************",
                "model": {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "name": "gpt-4",
                    "description": "A large language model",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
                "organization": {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "name": "Example Organization",
                    "description": "This is an example organization",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                "team_agents": [],
                "team_inputs": [],
                "team_outputs": [],
                "team_termination_conditions": [],
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                # Add example values for other fields
            }
        }


class TeamList(BaseModel):
    """Schema for paginated list of teams"""

    items: List[TeamResponse] = Field(..., description="List of teams")
    total: int = Field(..., description="Total number of teams")
    active_workflow: int = Field(0, description="Number of active workflows/teams")
    total_workflow: int = Field(0, description="Total number of workflows for the user")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": 1,
                        "name": "Example Team 1",
                        "description": "This is an example team",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": 2,
                        "name": "Example Team 2",
                        "description": "This is another example team",
                        "created_at": "2023-01-03T00:00:00Z",
                        "updated_at": "2023-01-04T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "total": 2,
                "active_workflow": 2,
                "total_workflow": 5,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }


class TeamTest(BaseModel):
    task: str = Field(..., description="Task to be performed by the team")

    class Config:
        json_schema_extra = {
            "example": {
                "task": "What is the capital of France?",
            }
        }
