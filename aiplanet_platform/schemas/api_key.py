"""
Pydantic schemas for api key
"""
from datetime import datetime, timezone
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class APIKeyCreate(BaseModel):
    """Schema for creating a new API key"""

    name: str = Field(
        ..., min_length=1, max_length=255, description="Human-readable name"
    )
    description: Optional[str] = Field(
        None, max_length=1000, description="Description of purpose"
    )
    scopes: List[str] = Field(
        default_factory=list, description="List of allowed scopes"
    )

    # Rate limiting (optional, will use defaults if not provided)
    rate_limit_per_minute: Optional[int] = Field(60, ge=1, le=1000)
    rate_limit_per_hour: Optional[int] = Field(1000, ge=1, le=100000)
    rate_limit_per_day: Optional[int] = Field(10000, ge=1, le=1000000)

    # Optional expiration
    expires_at: Optional[datetime] = Field(None, description="Optional expiration date")

    @validator("expires_at")
    def expires_at_must_be_future(cls, v):
        if v:
            # Use timezone-aware datetime for comparison
            now = datetime.now(timezone.utc)

            # If input datetime is timezone-naive, make it timezone-aware (assume UTC)
            if v.tzinfo is None:
                v = v.replace(tzinfo=timezone.utc)

            if v <= now:
                raise ValueError("Expiration date must be in the future")
        return v


class APIKeyUpdate(BaseModel):
    """Schema for updating an API key"""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    scopes: Optional[List[str]] = None

    rate_limit_per_minute: Optional[int] = Field(None, ge=1, le=1000)
    rate_limit_per_hour: Optional[int] = Field(None, ge=1, le=100000)
    rate_limit_per_day: Optional[int] = Field(None, ge=1, le=1000000)

    is_active: Optional[bool] = None
    expires_at: Optional[datetime] = None

    @validator("expires_at")
    def expires_at_must_be_future(cls, v):
        if v:
            # Use timezone-aware datetime for comparison
            now = datetime.now(timezone.utc)

            # If input datetime is timezone-naive, make it timezone-aware (assume UTC)
            if v.tzinfo is None:
                v = v.replace(tzinfo=timezone.utc)

            if v <= now:
                raise ValueError("Expiration date must be in the future")
        return v


class APIKeyResponse(BaseModel):
    """Schema for API key responses (without sensitive data)"""

    id: UUID
    name: str
    description: Optional[str]
    key_prefix: str  # Only show prefix for identification
    scopes: List[str]

    rate_limit_per_minute: int
    rate_limit_per_hour: int
    rate_limit_per_day: int

    is_active: bool
    expires_at: Optional[datetime]

    last_used_at: Optional[datetime]
    total_requests: int

    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class APIKeyCreateResponse(BaseModel):
    """Schema for API key creation response (includes the actual key)"""

    api_key: APIKeyResponse
    key: str  # The actual API key (only shown once!)

    class Config:
        from_attributes = True


class APIKeyList(BaseModel):
    """Schema for paginated API key list"""

    items: List[APIKeyResponse]
    total: int
    page: int
    pages: int
    size: int


class APIKeyScopeInfo(BaseModel):
    """Schema for available API scopes information"""

    scope: str
    description: str
    category: str


class APIKeyScopesResponse(BaseModel):
    """Schema for listing available scopes"""

    scopes: List[APIKeyScopeInfo]
