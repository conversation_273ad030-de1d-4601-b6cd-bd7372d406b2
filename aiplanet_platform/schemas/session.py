"""
Pydantic schemas for session
"""
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field
from aiplanet_platform.schemas.team import TeamResponse
from aiplanet_platform.schemas.message import MessageResponse


class SessionBase(BaseModel):
    """Base schema for session"""

    name: Optional[str] = Field(
        None, description="Name of the session", min_length=1, max_length=255
    )
    team_id: UUID = Field(..., description="Team ID")
    # Add your custom fields here


class SessionCreate(SessionBase):
    """Schema for creating a session"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Session",
                "team_id": "123e4567-e89b-12d3-a456-************",
            }
        }


class SessionUpdate(SessionBase):
    """Schema for updating a session"""

    description: Optional[str] = Field(None, description="Description of the session")
    # Add your custom fields here

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Updated Session",
                "description": "This is an updated session",
                # Add example values for other fields
            }
        }


class SessionResponse(SessionBase):
    """Schema for session response"""

    id: UUID = Field(..., description="Unique identifier")
    description: Optional[str] = Field(None, description="Description of the session")
    messages: Optional[List[MessageResponse]] = Field(
        None, description="List of messages"
    )
    team: Optional[TeamResponse] = Field(None, description="Team")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "name": "Example Session",
                "description": "This is an example session",
                "team_id": "123e4567-e89b-12d3-a456-************",
                "team": {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "name": "Example Team",
                    "description": "This is an example team",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                "messages": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "config": {
                            "content": "Hello",
                            "source": "user",
                            "type": "text",
                        },
                        "session_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "config": {
                            "content": "Hi",
                            "source": "assistant",
                            "type": "text",
                        },
                        "session_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                # Add example values for other fields
            }
        }


class SessionList(BaseModel):
    """Schema for paginated list of sessions"""

    items: List[SessionResponse] = Field(..., description="List of sessions")
    total: int = Field(..., description="Total number of sessions")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": 1,
                        "name": "Example Session",
                        "description": "This is an example session",
                        "team_id": "123e4567-e89b-12d3-a456-************",
                        "team": {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "name": "Example Team",
                            "description": "This is an example team",
                            "created_at": "2023-01-01T00:00:00Z",
                            "updated_at": "2023-01-02T00:00:00Z",
                            "is_deleted": False,
                        },
                        "messages": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "config": {
                                    "content": "Hello",
                                    "source": "user",
                                    "type": "text",
                                },
                                "session_id": "123e4567-e89b-12d3-a456-************",
                                "created_at": "2023-01-01T00:00:00Z",
                                "updated_at": "2023-01-02T00:00:00Z",
                                "is_deleted": False,
                            },
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "config": {
                                    "content": "Hi",
                                    "source": "assistant",
                                    "type": "text",
                                },
                                "session_id": "123e4567-e89b-12d3-a456-************",
                                "created_at": "2023-01-01T00:00:00Z",
                                "updated_at": "2023-01-02T00:00:00Z",
                                "is_deleted": False,
                            },
                        ],
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        # Add example values for other fields
                    },
                    {
                        "id": 1,
                        "name": "Example Session",
                        "description": "This is an example session",
                        "team_id": "123e4567-e89b-12d3-a456-************",
                        "team": {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "name": "Example Team",
                            "description": "This is an example team",
                            "created_at": "2023-01-01T00:00:00Z",
                            "updated_at": "2023-01-02T00:00:00Z",
                            "is_deleted": False,
                        },
                        "messages": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "config": {
                                    "content": "Hello",
                                    "source": "user",
                                    "type": "text",
                                },
                                "session_id": "123e4567-e89b-12d3-a456-************",
                                "created_at": "2023-01-01T00:00:00Z",
                                "updated_at": "2023-01-02T00:00:00Z",
                                "is_deleted": False,
                            },
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "config": {
                                    "content": "Hi",
                                    "source": "assistant",
                                    "type": "text",
                                },
                                "session_id": "123e4567-e89b-12d3-a456-************",
                                "created_at": "2023-01-01T00:00:00Z",
                                "updated_at": "2023-01-02T00:00:00Z",
                                "is_deleted": False,
                            },
                        ],
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        # Add example values for other fields
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }


class RunBase(BaseModel):
    """Base schema for run"""

    user_id: Optional[UUID] = Field(None, description="User id of the run")
    session_id: UUID = Field(..., description="Session id of the run")


class RunCreate(RunBase):
    """Schema for creating a run"""

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "123e4567-e89b-12d3-a456-************",
                "session_id": "123e4567-e89b-12d3-a456-************",
            }
        }
