"""
Pydantic schemas for S3 operations
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import BaseModel, Field


class S3UploadRequest(BaseModel):
    """Schema for S3 upload request"""

    folder_id: UUID = Field(..., description="Folder ID to upload files to")
    description: Optional[str] = Field(None, description="File description")

    class Config:
        json_schema_extra = {
            "example": {
                "folder_id": "123e4567-e89b-12d3-a456-************",
                "description": "Uploaded file description",
            }
        }


class S3UploadResponse(BaseModel):
    """Schema for S3 upload response"""

    file_id: UUID = Field(..., description="File ID")
    filename: str = Field(..., description="Original filename")
    s3_path: str = Field(..., description="S3 path")
    status: str = Field(..., description="Upload status")
    size: Optional[int] = Field(None, description="File size in bytes")
    content_type: str = Field(..., description="File content type")

    class Config:
        json_schema_extra = {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-************",
                "filename": "document.pdf",
                "s3_path": "folders/123e4567-e89b-12d3-a456-************/files/abc123.pdf",
                "status": "Success",
                "size": 1024000,
                "content_type": "application/pdf",
            }
        }


class S3BulkUploadResponse(BaseModel):
    """Schema for S3 bulk upload response"""

    uploaded_files: List[S3UploadResponse] = Field(
        ..., description="List of uploaded files"
    )
    total_files: int = Field(..., description="Total number of files processed")
    successful_uploads: int = Field(..., description="Number of successful uploads")
    failed_uploads: int = Field(..., description="Number of failed uploads")

    class Config:
        json_schema_extra = {
            "example": {
                "uploaded_files": [
                    {
                        "file_id": "123e4567-e89b-12d3-a456-************",
                        "filename": "document1.pdf",
                        "s3_path": "folders/123e4567-e89b-12d3-a456-************/files/abc123.pdf",
                        "status": "Success",
                        "size": 1024000,
                        "content_type": "application/pdf",
                    }
                ],
                "total_files": 3,
                "successful_uploads": 2,
                "failed_uploads": 1,
            }
        }


class S3PresignedUrlRequest(BaseModel):
    """Schema for presigned URL request"""

    file_id: UUID = Field(..., description="File ID")
    expiration: int = Field(
        3600, description="URL expiration time in seconds", ge=1, le=604800
    )  # Max 7 days
    operation: str = Field(
        "get_object", description="S3 operation (get_object, put_object)"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-************",
                "expiration": 3600,
                "operation": "get_object",
            }
        }


class S3PresignedUrlResponse(BaseModel):
    """Schema for presigned URL response"""

    presigned_url: str = Field(..., description="Presigned URL")
    expires_in: int = Field(..., description="URL expiration time in seconds")
    file_id: UUID = Field(..., description="File ID")

    class Config:
        json_schema_extra = {
            "example": {
                "presigned_url": "https://s3.amazonaws.com/bucket/key?signature=...",
                "expires_in": 3600,
                "file_id": "123e4567-e89b-12d3-a456-************",
            }
        }


class S3PresignedUploadRequest(BaseModel):
    """Schema for presigned upload URL request"""

    folder_id: UUID = Field(..., description="Folder ID")
    filename: str = Field(
        ..., description="Original filename", min_length=1, max_length=255
    )
    content_type: str = Field(..., description="File content type")
    expiration: int = Field(
        3600, description="URL expiration time in seconds", ge=1, le=604800
    )

    class Config:
        json_schema_extra = {
            "example": {
                "folder_id": "123e4567-e89b-12d3-a456-************",
                "filename": "document.pdf",
                "content_type": "application/pdf",
                "expiration": 3600,
            }
        }


class S3PresignedUploadResponse(BaseModel):
    """Schema for presigned upload URL response"""

    upload_url: str = Field(..., description="Presigned upload URL")
    s3_key: str = Field(..., description="S3 key for the file")
    bucket: str = Field(..., description="S3 bucket name")
    expires_in: int = Field(..., description="URL expiration time in seconds")

    class Config:
        json_schema_extra = {
            "example": {
                "upload_url": "https://s3.amazonaws.com/bucket/key?signature=...",
                "s3_key": "folders/123e4567-e89b-12d3-a456-************/files/abc123.pdf",
                "bucket": "aiplanet-platform-files",
                "expires_in": 3600,
            }
        }


class S3FileMetadata(BaseModel):
    """Schema for S3 file metadata"""

    file_id: UUID = Field(..., description="File ID")
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="File content type")
    size: Optional[int] = Field(None, description="File size in bytes")
    last_modified: Optional[datetime] = Field(
        None, description="Last modified timestamp"
    )
    etag: Optional[str] = Field(None, description="S3 ETag")
    s3_path: str = Field(..., description="S3 path")
    status: str = Field(..., description="File status")

    class Config:
        json_schema_extra = {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-************",
                "filename": "document.pdf",
                "content_type": "application/pdf",
                "size": 1024000,
                "last_modified": "2023-01-01T00:00:00Z",
                "etag": '"abc123def456"',
                "s3_path": "folders/123e4567-e89b-12d3-a456-************/files/abc123.pdf",
                "status": "Success",
            }
        }


class S3FileListResponse(BaseModel):
    """Schema for S3 file list response"""

    files: List[S3FileMetadata] = Field(..., description="List of files")
    total_files: int = Field(..., description="Total number of files")
    folder_id: UUID = Field(..., description="Folder ID")

    class Config:
        json_schema_extra = {
            "example": {
                "files": [
                    {
                        "file_id": "123e4567-e89b-12d3-a456-************",
                        "filename": "document.pdf",
                        "content_type": "application/pdf",
                        "size": 1024000,
                        "last_modified": "2023-01-01T00:00:00Z",
                        "etag": '"abc123def456"',
                        "s3_path": "folders/123e4567-e89b-12d3-a456-************/files/abc123.pdf",
                        "status": "Success",
                    }
                ],
                "total_files": 1,
                "folder_id": "123e4567-e89b-12d3-a456-************",
            }
        }


class S3DeleteResponse(BaseModel):
    """Schema for S3 delete response"""

    file_id: UUID = Field(..., description="File ID")
    success: bool = Field(..., description="Whether the deletion was successful")
    message: str = Field(..., description="Status message")

    class Config:
        json_schema_extra = {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-************",
                "success": True,
                "message": "File deleted successfully",
            }
        }


class S3FolderCreateRequest(BaseModel):
    """Schema for combined folder creation and file upload request"""

    folder_name: str = Field(
        ..., description="Name of the folder to create", min_length=1, max_length=255
    )
    organization_id: UUID = Field(..., description="Organization ID")
    folder_description: Optional[str] = Field(None, description="Folder description")
    file_description: Optional[str] = Field(None, description="Files description")
    chunking_config: Optional[Dict[str, Any]] = Field(
        None, description="Chunking configuration"
    )
    embedding_config: Optional[Dict[str, Any]] = Field(
        None, description="Embedding configuration"
    )
    vector_db_config: Optional[Dict[str, Any]] = Field(
        None, description="Vector database configuration"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "folder_name": "My Documents",
                "organization_id": "123e4567-e89b-12d3-a456-************",
                "folder_description": "Important documents folder",
                "file_description": "Uploaded files",
                "chunking_config": {"chunk_size": 1000, "overlap": 200},
                "embedding_config": {"model": "text-embedding-ada-002"},
                "vector_db_config": {"collection": "documents"},
            }
        }


class S3FolderCreateResponse(BaseModel):
    """Schema for combined folder creation and file upload response"""

    folder_id: UUID = Field(..., description="Created folder ID")
    folder_name: str = Field(..., description="Folder name")
    uploaded_files: List[S3UploadResponse] = Field(
        ..., description="List of uploaded files"
    )
    total_files: int = Field(..., description="Total number of files processed")
    successful_uploads: int = Field(..., description="Number of successful uploads")
    failed_uploads: int = Field(..., description="Number of failed uploads")

    class Config:
        json_schema_extra = {
            "example": {
                "folder_id": "123e4567-e89b-12d3-a456-************",
                "folder_name": "My Documents",
                "uploaded_files": [
                    {
                        "file_id": "123e4567-e89b-12d3-a456-************",
                        "filename": "document1.pdf",
                        "s3_path": "folders/123e4567-e89b-12d3-a456-************/files/abc123.pdf",
                        "status": "Success",
                        "size": 1024000,
                        "content_type": "application/pdf",
                    }
                ],
                "total_files": 3,
                "successful_uploads": 2,
                "failed_uploads": 1,
            }
        }
