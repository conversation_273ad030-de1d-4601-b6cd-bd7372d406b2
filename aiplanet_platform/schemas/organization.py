"""
Pydantic schemas for organization
"""
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from aiplanet_platform.constants.organization import OrganizationStatus


class OrganizationBase(BaseModel):
    """Base schema for organization"""

    name: str = Field(
        ..., description="Name of the organization", min_length=1, max_length=255
    )
    description: Optional[str] = Field(
        None, description="Description of the organization"
    )
    status: Optional[OrganizationStatus] = Field(
        OrganizationStatus.ACTIVE, description="Status of the organization"
    )
    email: str = Field(None, description="Email of the organization")
    # Add your custom fields here


class OrganizationCreate(OrganizationBase):
    """Schema for creating a organization"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Organization",
                "description": "This is an example organization",
                "status": OrganizationStatus.PENDING,
                "email": "<EMAIL>",
                # Add example values for other fields
            }
        }


class OrganizationUpdate(BaseModel):
    """Schema for updating a organization"""

    name: Optional[str] = Field(
        None, description="Name of the organization", min_length=1, max_length=255
    )
    description: Optional[str] = Field(
        None, description="Description of the organization"
    )
    # Add your custom fields here

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Organization",
                "description": "This is an example organization",
                "status": OrganizationStatus.PENDING,
                "email": "<EMAIL>",
            }
        }


class OrganizationResponse(OrganizationBase):
    """Schema for organization response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                "name": "Example Organization",
                "description": "This is an example organization",
                "status": OrganizationStatus.ACTIVE,
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                # Add example values for other fields
            }
        }


class OrganizationList(BaseModel):
    """Schema for paginated list of organizations"""

    items: List[OrganizationResponse] = Field(..., description="List of organizations")
    total: int = Field(..., description="Total number of organizations")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "name": "Example Organization 1",
                        "description": "This is an example organization",
                        "status": OrganizationStatus.ACTIVE,
                        "email": "<EMAIL>",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "name": "Example Organization 2",
                        "description": "This is another example organization",
                        "status": OrganizationStatus.ACTIVE,
                        "email": "<EMAIL>",
                        "created_at": "2023-01-03T00:00:00Z",
                        "updated_at": "2023-01-04T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
