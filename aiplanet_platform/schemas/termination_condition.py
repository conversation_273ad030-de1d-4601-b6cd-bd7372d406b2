"""
Pydantic schemas for termination condition
"""

from uuid import UUID
from datetime import datetime
from typing import List, Optional, Union

from pydantic import BaseModel, Field
from autogen_core import ComponentModel


class TerminationConditionBase(BaseModel):
    """Base schema for termination_condition"""

    component: Union[ComponentModel, dict] = Field(
        ..., description="Component of the team"
    )
    organization_id: Optional[UUID] = Field(None, description="Organization ID")
    # Add your custom fields here


class TerminationConditionCreate(TerminationConditionBase):
    """Schema for creating a termination_condition"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                    "component_type": "termination",
                    "version": 1,
                    "component_version": 1,
                    "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
                    "label": "MaxMessageTermination",
                    "config": {"max_messages": 10, "include_agent_event": False},
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
                # Add example values for other fields
            }
        }


class TerminationConditionUpdate(TerminationConditionBase):
    """Schema for updating a termination_condition"""

    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                    "component_type": "termination",
                    "version": 1,
                    "component_version": 1,
                    "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
                    "label": "MaxMessageTermination",
                    "config": {"max_messages": 10, "include_agent_event": False},
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
                # Add example values for other fields
            }
        }


class TerminationConditionResponse(TerminationConditionBase):
    """Schema for termination_condition response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    is_default: bool = Field(
        False, description="Whether this is a default system termination condition"
    )
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "component": {
                    "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                    "component_type": "termination",
                    "version": 1,
                    "component_version": 1,
                    "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
                    "label": "MaxMessageTermination",
                    "config": {"max_messages": 10, "include_agent_event": False},
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                "is_default": False,
                # Add example values for other fields
            }
        }


class TerminationConditionList(BaseModel):
    """Schema for paginated list of termination_conditions"""

    items: List[TerminationConditionResponse] = Field(
        ..., description="List of termination_conditions"
    )
    total: int = Field(..., description="Total number of termination_conditions")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "component": {
                            "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                            "component_type": "termination",
                            "version": 1,
                            "component_version": 1,
                            "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
                            "label": "MaxMessageTermination",
                            "config": {
                                "max_messages": 10,
                                "include_agent_event": False,
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        "is_default": False,
                        # Add example values for other fields
                    },
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "component": {
                            "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                            "component_type": "termination",
                            "version": 1,
                            "component_version": 1,
                            "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
                            "label": "MaxMessageTermination",
                            "config": {
                                "max_messages": 10,
                                "include_agent_event": False,
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        "is_default": False,
                        # Add example values for other fields
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
