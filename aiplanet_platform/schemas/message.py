"""
Pydantic schemas for message
"""
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field
from aiplanet_platform.models.message import MessageConfig


class MessageBase(BaseModel):
    """Base schema for message"""

    run_id: UUID = Field(..., description="Name of the message")
    session_id: UUID = Field(..., description="Name of the message")
    config: Optional[MessageConfig] = Field(
        None, description="Message config of the message"
    )
    # Add your custom fields here


class MessageCreate(MessageBase):
    """Schema for creating a message"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "run_id": "123e4567-e89b-12d3-a456-************",
                "session_id": "123e4567-e89b-12d3-a456-************",
                "config": {"content": "Hello World", "source": "user", "type": "text"},
            }
        }


class MessageUpdate(BaseModel):
    """Schema for updating a message"""

    class Config:
        json_schema_extra = {
            "example": {
                "run_id": "123e4567-e89b-12d3-a456-************",
                "session_id": "123e4567-e89b-12d3-a456-************",
                "config": {"content": "Hello World", "source": "user", "type": "text"},
            }
        }


class MessageResponse(MessageBase):
    """Schema for message response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "run_id": "123e4567-e89b-12d3-a456-************",
                "session_id": "123e4567-e89b-12d3-a456-************",
                "config": {"content": "Hello World", "source": "user", "type": "text"},
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                # Add example values for other fields
            }
        }


class MessageList(BaseModel):
    """Schema for paginated list of messages"""

    items: List[MessageResponse] = Field(..., description="List of messages")
    total: int = Field(..., description="Total number of messages")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "run_id": "123e4567-e89b-12d3-a456-************",
                        "session_id": "123e4567-e89b-12d3-a456-************",
                        "config": {
                            "content": "Hello World",
                            "source": "user",
                            "type": "text",
                        },
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        # Add example values for other fields
                    },
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "run_id": "123e4567-e89b-12d3-a456-************",
                        "session_id": "123e4567-e89b-12d3-a456-************",
                        "config": {
                            "content": "Hello World",
                            "source": "user",
                            "type": "text",
                        },
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        # Add example values for other fields
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
