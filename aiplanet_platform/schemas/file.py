"""
Pydantic schemas for file
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class FileBase(BaseModel):
    """Base schema for file"""

    name: str = Field(..., description="Name of the file", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the file")
    # Add your custom fields here


class FileCreate(FileBase):
    """Schema for creating a file"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example File",
                "description": "This is an example file",
                # Add example values for other fields
            }
        }


class FileUpdate(BaseModel):
    """Schema for updating a file"""

    name: Optional[str] = Field(
        None, description="Name of the file", min_length=1, max_length=255
    )
    description: Optional[str] = Field(None, description="Description of the file")
    # Add your custom fields here

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Updated File",
                "description": "This is an updated file",
                # Add example values for other fields
            }
        }


class FileResponse(FileBase):
    """Schema for file response"""

    id: int = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "name": "Example File",
                "description": "This is an example file",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                # Add example values for other fields
            }
        }


class FileList(BaseModel):
    """Schema for paginated list of files"""

    items: List[FileResponse] = Field(..., description="List of files")
    total: int = Field(..., description="Total number of files")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": 1,
                        "name": "Example File 1",
                        "description": "This is an example file",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": 2,
                        "name": "Example File 2",
                        "description": "This is another example file",
                        "created_at": "2023-01-03T00:00:00Z",
                        "updated_at": "2023-01-04T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }


class FileStatusUpdate(BaseModel):
    """Schema for updating file status"""

    file_id: str = Field(..., description="File ID")
    status: str = Field(..., description="File status")

    class Config:
        json_schema_extra = {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "Success",
            }
        }
