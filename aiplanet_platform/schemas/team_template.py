"""
Pydantic schemas for team template
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class TeamTemplateBase(BaseModel):
    """Base schema for team template"""

    name: str = Field(
        ..., min_length=1, max_length=255, description="Name of the template"
    )
    description: Optional[str] = Field(None, description="Description of the template")
    category: Optional[str] = Field(
        None, max_length=100, description="Category of the template"
    )
    template_config: dict = Field(
        ...,
        description="Template configuration with nested structure: {'team': {...}, 'inputs': [...], 'outputs': [...]}",
    )
    use_case: Optional[str] = Field(
        None, max_length=255, description="Use case description"
    )
    tags: Optional[List[str]] = Field(default_factory=list, description="Template tags")

    @validator("tags")
    def validate_tags(cls, v):
        if v and len(v) > 10:
            raise ValueError("Maximum 10 tags allowed")
        return v


class TeamTemplateCreate(TeamTemplateBase):
    """Schema for creating a team template"""

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Customer Service Team Template",
                "description": "A template for customer service workflows",
                "category": "customer_service",
                "template_config": {
                    "team": {
                        "label": "Customer Service Team",
                        "config": {
                            "max_turns": 5,
                            "participants": [
                                {
                                    "label": "support_agent",
                                    "config": {
                                        "name": "support_agent",
                                        "tools": [
                                            {
                                                "label": "WebSearchTool",
                                                "config": {
                                                    "name": "web_search",
                                                    "description": "Search the web for current information",
                                                    "source_code": "async def search_web(query: str) -> str:\n    # Implementation here\n    return 'Search results'",
                                                    "global_imports": [],
                                                    "has_cancellation_support": False,
                                                },
                                                "version": 1,
                                                "provider": "autogen_core.tools.FunctionTool",
                                                "description": "Create custom tools by wrapping standard Python functions.",
                                                "component_type": "tool",
                                                "component_version": 1,
                                            }
                                        ],
                                        "metadata": {},
                                        "description": "A helpful customer service agent",
                                        "model_client": {
                                            "label": "gpt4_model",
                                            "config": {
                                                "model": "gpt-4",
                                                "temperature": 0.7,
                                                "max_tokens": 1000,
                                            },
                                            "version": 1,
                                            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                                            "description": "Chat completion client for OpenAI hosted models.",
                                            "component_type": "model",
                                            "component_version": 1,
                                        },
                                        "model_context": {
                                            "label": "UnboundedChatCompletionContext",
                                            "config": {},
                                            "version": 1,
                                            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                            "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                                            "component_type": "chat_completion_context",
                                            "component_version": 1,
                                        },
                                        "system_message": "You are a helpful customer service agent.",
                                        "model_client_stream": False,
                                        "reflect_on_tool_use": False,
                                        "tool_call_summary_format": "{result}",
                                    },
                                    "version": 1,
                                    "provider": "autogen_agentchat.agents.AssistantAgent",
                                    "description": "An agent that provides assistance with tool use.",
                                    "component_type": "agent",
                                    "component_version": 1,
                                }
                            ],
                            "emit_team_events": False,
                            "termination_condition": {
                                "label": "MaxMessageTermination",
                                "config": {
                                    "max_messages": 10,
                                    "include_agent_event": False,
                                },
                                "version": 1,
                                "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                                "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
                                "component_type": "termination",
                                "component_version": 1,
                            },
                        },
                        "version": 1,
                        "provider": "autogen_agentchat.teams.RoundRobinGroupChat",
                        "description": "A team that runs a group chat with participants taking turns in a round-robin fashion to publish a message to all.",
                        "component_type": "team",
                        "component_version": 1,
                    },
                    "inputs": [
                        {
                            "provider": "autogen_core.io.TextInput",
                            "component_type": "input",
                            "version": 1,
                            "component_version": 1,
                            "description": "Text input for customer queries",
                            "label": "Customer Query Input",
                            "config": {"content": "", "encoding": "utf-8"},
                        }
                    ],
                    "outputs": [
                        {
                            "provider": "autogen_core.io.TextOutput",
                            "component_type": "output",
                            "version": 1,
                            "component_version": 1,
                            "description": "Text output for customer responses",
                            "label": "Customer Response Output",
                            "config": {
                                "encoding": "utf-8",
                                "output_path": "response.txt",
                                "line_ending": "lf",
                            },
                        }
                    ],
                },
                "use_case": "Handle customer inquiries and support requests",
                "tags": ["customer_service", "support"],
            }
        }


class TeamTemplateUpdate(BaseModel):
    """Schema for updating a team template"""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    template_config: Optional[dict] = None
    use_case: Optional[str] = Field(None, max_length=255)
    tags: Optional[List[str]] = None

    @validator("tags")
    def validate_tags(cls, v):
        if v and len(v) > 10:
            raise ValueError("Maximum 10 tags allowed")
        return v


class TeamTemplateResponse(TeamTemplateBase):
    """Schema for team template response"""

    id: UUID = Field(..., description="Unique identifier")
    organization_id: Optional[UUID] = Field(None, description="Organization ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the template is deleted")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "name": "Customer Service Team Template",
                "description": "A template for customer service workflows",
                "category": "customer_service",
                "template_config": {
                    "label": "Customer Service Team",
                    "config": {
                        "max_turns": 5,
                        "participants": [],
                        "emit_team_events": False,
                        "termination_condition": {
                            "label": "MaxMessageTermination",
                            "config": {
                                "max_messages": 10,
                                "include_agent_event": False,
                            },
                            "version": 1,
                            "provider": "autogen_agentchat.conditions.MaxMessageTermination",
                            "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
                            "component_type": "termination",
                            "component_version": 1,
                        },
                    },
                    "version": 1,
                    "provider": "autogen_agentchat.teams.RoundRobinGroupChat",
                    "description": "A team that runs a group chat with participants taking turns in a round-robin fashion to publish a message to all.",
                    "component_type": "team",
                    "component_version": 1,
                },
                "use_case": "Handle customer inquiries and support requests",
                "tags": ["customer_service", "support"],
                "organization_id": "123e4567-e89b-12d3-a456-************",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
            }
        }


class TeamTemplateList(BaseModel):
    """Schema for paginated list of team templates"""

    items: List[TeamTemplateResponse] = Field(..., description="List of team templates")
    total: int = Field(..., description="Total number of templates")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "name": "Customer Service Team Template",
                        "description": "A template for customer service workflows",
                        "category": "customer_service",
                        "template_config": {
                            "label": "Customer Service Team",
                            "config": {
                                "max_turns": 5,
                                "participants": [],
                                "emit_team_events": False,
                                "termination_condition": {},
                            },
                            "version": 1,
                            "provider": "autogen_agentchat.teams.RoundRobinGroupChat",
                            "description": "A team that runs a group chat with participants taking turns in a round-robin fashion to publish a message to all.",
                            "component_type": "team",
                            "component_version": 1,
                        },
                        "use_case": "Handle customer inquiries",
                        "tags": ["customer_service"],
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    }
                ],
                "total": 1,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }


class TeamFromTemplateRequest(BaseModel):
    """Schema for creating a team from a template"""

    template_id: UUID = Field(..., description="ID of the template to use")
    name: Optional[str] = Field(None, description="Override template name")
    customizations: Optional[dict] = Field(
        None, description="Custom overrides for the template configuration"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "template_id": "123e4567-e89b-12d3-a456-************",
                "name": "My Custom Team",
                "customizations": {"config": {"max_turns": 10}},
            }
        }
