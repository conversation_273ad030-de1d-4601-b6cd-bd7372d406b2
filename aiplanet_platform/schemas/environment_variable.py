from typing import Optional
from pydantic import BaseModel
from uuid import UUID


class EnvironmentVariableCreate(BaseModel):
    name: str
    value: str
    type: str = "string"
    description: Optional[str] = None
    required: bool = False


class EnvironmentVariableUpdate(BaseModel):
    name: Optional[str] = None
    value: Optional[str] = None
    type: Optional[str] = None
    description: Optional[str] = None
    required: Optional[bool] = None


class EnvironmentVariableOut(BaseModel):
    id: UUID
    name: str
    value: Optional[str] = None
    type: str
    description: Optional[str] = None
    required: bool

    class Config:
        orm_mode = True
