"""
Pydantic schemas for input
"""
from datetime import datetime
from typing import List, Optional, Union
from uuid import UUID

from autogen_core import ComponentModel
from pydantic import BaseModel, Field


class InputBase(BaseModel):
    """Base schema for input"""

    component: Union[ComponentModel, dict] = Field(
        ..., description="Component of the tool"
    )
    organization_id: Optional[UUID] = Field(None, description="Organization ID")
    # Add your custom fields here


class InputCreate(InputBase):
    """Schema for creating a input"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "component": {
                        "provider": "autogen_core.io.FileInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "File input component for reading various file types",
                        "label": "FileInput",
                        "config": {
                            "file_path": "data/input.txt",
                            "encoding": "utf-8",
                            "file_type": "txt",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.TextInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "Text input component for direct text input",
                        "label": "TextInput",
                        "config": {"content": "Hello, world!", "encoding": "utf-8"},
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.URLInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "URL input component for fetching data from APIs",
                        "label": "URLInput",
                        "config": {
                            "url": "https://api.example.com/data",
                            "headers": {"Authorization": "Bearer token"},
                            "timeout": 30,
                            "verify_ssl": True,
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.ImageInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "Image input component for handling image data",
                        "label": "ImageInput",
                        "config": {
                            "source": "image.png",
                            "source_type": "file",
                            "format": "png",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
            ]
        }


class InputUpdate(InputBase):
    """Schema for updating a input"""

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "component": {
                        "provider": "autogen_core.io.FileInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "File input component for reading various file types",
                        "label": "FileInput",
                        "config": {
                            "file_path": "data/input.txt",
                            "encoding": "utf-8",
                            "file_type": "txt",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.TextInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "Text input component for direct text input",
                        "label": "TextInput",
                        "config": {"content": "Hello, world!", "encoding": "utf-8"},
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.URLInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "URL input component for fetching data from APIs",
                        "label": "URLInput",
                        "config": {
                            "url": "https://api.example.com/data",
                            "headers": {"Authorization": "Bearer token"},
                            "timeout": 30,
                            "verify_ssl": True,
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
                {
                    "component": {
                        "provider": "autogen_core.io.ImageInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "Image input component for handling image data",
                        "label": "ImageInput",
                        "config": {
                            "source": "image.png",
                            "source_type": "file",
                            "format": "png",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                },
            ]
        }


class InputResponse(InputBase):
    """Schema for input response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "examples": [
                {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "component": {
                        "provider": "autogen_core.io.FileInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "File input component for reading various file types",
                        "label": "FileInput",
                        "config": {
                            "file_path": "data/input.txt",
                            "encoding": "utf-8",
                            "file_type": "txt",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "component": {
                        "provider": "autogen_core.io.TextInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "Text input component for direct text input",
                        "label": "TextInput",
                        "config": {"content": "Hello, world!", "encoding": "utf-8"},
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "component": {
                        "provider": "autogen_core.io.URLInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "URL input component for fetching data from APIs",
                        "label": "URLInput",
                        "config": {
                            "url": "https://api.example.com/data",
                            "headers": {"Authorization": "Bearer token"},
                            "timeout": 30,
                            "verify_ssl": True,
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
                {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "component": {
                        "provider": "autogen_core.io.ImageInput",
                        "component_type": "input",
                        "version": 1,
                        "component_version": 1,
                        "description": "Image input component for handling image data",
                        "label": "ImageInput",
                        "config": {
                            "source": "image.png",
                            "source_type": "file",
                            "format": "png",
                        },
                    },
                    "organization_id": "123e4567-e89b-12d3-a456-************",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-02T00:00:00Z",
                    "is_deleted": False,
                },
            ]
        }


class InputList(BaseModel):
    """Schema for paginated list of inputs"""

    items: List[InputResponse] = Field(..., description="List of inputs")
    total: int = Field(..., description="Total number of inputs")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "component": {
                            "provider": "autogen_core.io.FileInput",
                            "component_type": "input",
                            "version": 1,
                            "component_version": 1,
                            "description": "File input component for reading various file types",
                            "label": "FileInput",
                            "config": {
                                "file_path": "data/input.txt",
                                "encoding": "utf-8",
                                "file_type": "txt",
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "component": {
                            "provider": "autogen_core.io.TextInput",
                            "component_type": "input",
                            "version": 1,
                            "component_version": 1,
                            "description": "Text input component for direct text input",
                            "label": "TextInput",
                            "config": {"content": "Hello, world!", "encoding": "utf-8"},
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
