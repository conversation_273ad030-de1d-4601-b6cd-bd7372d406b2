"""
Pydantic schemas for agent
"""

from datetime import datetime
from typing import List, Optional, Union
from uuid import UUID

from autogen_core import ComponentModel
from pydantic import BaseModel, Field
from aiplanet_platform.schemas.tool import ToolResponse


class AgentBase(BaseModel):
    """Base schema for agent"""

    # Add your custom fields here
    component: Union[ComponentModel, dict] = Field(
        ..., description="Component of the agent"
    )
    model_id: Optional[UUID] = Field(None, description="Associated model ID")
    tool_ids: Optional[List[UUID]] = Field([], description="List of tool IDs")
    organization_id: Optional[UUID] = Field(None, description="Organization ID")


class AgentCreate(AgentBase):
    """Schema for creating a agent"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_agentchat.agents.AssistantAgent",
                    "component_type": "agent",
                    "version": 1,
                    "component_version": 1,
                    "description": "An agent that provides assistance with tool use.",
                    "label": "AssistantAgent",
                    "config": {
                        "name": "assistant_agent",
                        "model_client": {
                            "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                            "component_type": "model",
                            "version": 1,
                            "component_version": 1,
                            "description": "Chat completion client for Azure OpenAI hosted models.",
                            "label": "AzureOpenAIChatCompletionClient",
                            "config": {
                                "model": "gpt-4o-mini",
                                "api_key": "**********",
                                "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                                "azure_deployment": "{your-azure-deployment}",
                                "api_version": "2024-06-01",
                            },
                        },
                        "workbench": {
                            "provider": "autogen_core.tools.StaticWorkbench",
                            "component_type": "workbench",
                            "version": 1,
                            "component_version": 1,
                            "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                            "label": "StaticWorkbench",
                            "config": {"tools": []},
                        },
                        "model_context": {
                            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                            "component_type": "chat_completion_context",
                            "version": 1,
                            "component_version": 1,
                            "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                            "label": "UnboundedChatCompletionContext",
                            "config": {},
                        },
                        "description": "an agent that helps the user",
                        "system_message": "You are a helpful assistant.",
                        "model_client_stream": False,
                        "reflect_on_tool_use": False,
                        "tool_call_summary_format": "{result}",
                        "metadata": {},
                    },
                },
                "model_id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                "tool_ids": ["2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841"],
            }
        }


class AgentUpdate(AgentBase):
    """Schema for updating a agent"""

    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_agentchat.agents.AssistantAgent",
                    "component_type": "agent",
                    "version": 1,
                    "component_version": 1,
                    "description": "An updated agent that provides assistance with tool use.",
                    "label": "Assistant Agent",
                    "config": {
                        "name": "assistant_agent",
                        "model_client": {
                            "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                            "component_type": "model",
                            "version": 1,
                            "component_version": 1,
                            "description": "Chat completion client for Azure OpenAI hosted models.",
                            "label": "AzureOpenAIChatCompletionClient",
                            "config": {
                                "model": "gpt-4o-mini",
                                "api_key": "**********",
                                "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                                "azure_deployment": "{your-azure-deployment}",
                                "api_version": "2024-06-01",
                            },
                        },
                        "workbench": {
                            "provider": "autogen_core.tools.StaticWorkbench",
                            "component_type": "workbench",
                            "version": 1,
                            "component_version": 1,
                            "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                            "label": "StaticWorkbench",
                            "config": {"tools": []},
                        },
                        "model_context": {
                            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                            "component_type": "chat_completion_context",
                            "version": 1,
                            "component_version": 1,
                            "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                            "label": "UnboundedChatCompletionContext",
                            "config": {},
                        },
                        "description": "an agent that helps the user",
                        "system_message": "You are a helpful assistant.",
                        "model_client_stream": False,
                        "reflect_on_tool_use": False,
                        "tool_call_summary_format": "{result}",
                        "metadata": {},
                    },
                },
                "tool_ids": ["2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841"],
                "model_id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
            }
        }


class AgentResponse(AgentBase):
    """Schema for agent response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    is_default: bool = Field(
        False, description="Whether this is a default system agent"
    )
    # Include additional fields from your model here
    component: Union[ComponentModel, dict] = Field(
        ..., description="Component of the agent"
    )
    organization_id: Optional[UUID] = Field(..., description="Organization ID")
    model_id: Optional[UUID] = Field(..., description="Associated model ID")
    tool_ids: List[UUID] = Field([], description="List of tool IDs")
    tools: List[ToolResponse] = Field([], description="List of tools")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                "component": {
                    "provider": "autogen_agentchat.agents.AssistantAgent",
                    "component_type": "agent",
                    "version": 1,
                    "component_version": 1,
                    "description": "An updated agent that provides assistance with tool use.",
                    "label": "Assistant Agent",
                    "config": {
                        "name": "assistant_agent",
                        "model_client": {
                            "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                            "component_type": "model",
                            "version": 1,
                            "component_version": 1,
                            "description": "Chat completion client for Azure OpenAI hosted models.",
                            "label": "AzureOpenAIChatCompletionClient",
                            "config": {
                                "model": "gpt-4o-mini",
                                "api_key": "**********",
                                "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                                "azure_deployment": "{your-azure-deployment}",
                                "api_version": "2024-06-01",
                            },
                        },
                        "workbench": {
                            "provider": "autogen_core.tools.StaticWorkbench",
                            "component_type": "workbench",
                            "version": 1,
                            "component_version": 1,
                            "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                            "label": "StaticWorkbench",
                            "config": {"tools": []},
                        },
                        "model_context": {
                            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                            "component_type": "chat_completion_context",
                            "version": 1,
                            "component_version": 1,
                            "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                            "label": "UnboundedChatCompletionContext",
                            "config": {},
                        },
                        "description": "an agent that helps the user",
                        "system_message": "You are a helpful assistant.",
                        "model_client_stream": False,
                        "reflect_on_tool_use": False,
                        "tool_call_summary_format": "{result}",
                        "metadata": {},
                    },
                },
                "organization_id": "123e4567-e89b-12d3-a456-426614174000",
                "model_id": "987fcdeb-51a2-4b6c-d789-426614174001",
                "tool_ids": [
                    "456789ab-cdef-1234-5678-426614174002",
                    "789abcde-f123-4567-89ab-426614174003",
                ],
                "tools": [
                    {
                        "id": "456789ab-cdef-1234-5678-426614174002",
                        "component": {
                            "provider": "autogen_core.tools.FunctionTool",
                            "component_type": "tool",
                            "version": 1,
                            "component_version": 1,
                            "description": "Calculator tool for mathematical operations",
                            "label": "CalculatorTool",
                            "config": {
                                "source_code": "def calculate(expression: str) -> float:\n    return eval(expression)",
                                "name": "calculator",
                                "description": "Perform mathematical calculations",
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-426614174000",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": "789abcde-f123-4567-89ab-426614174003",
                        "component": {
                            "provider": "autogen_core.tools.FunctionTool",
                            "component_type": "tool",
                            "version": 1,
                            "component_version": 1,
                            "description": "Web search tool for finding information",
                            "label": "WebSearchTool",
                            "config": {
                                "source_code": "def web_search(query: str) -> str:\n    # Implementation here\n    pass",
                                "name": "web_search",
                                "description": "Search the web for information",
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-426614174000",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
                "is_default": False,
            }
        }


class AgentList(BaseModel):
    """Schema for paginated list of agents"""

    items: List[AgentResponse] = Field(..., description="List of agents")
    total: int = Field(..., description="Total number of agents")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "component": {
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "component_type": "agent",
                            "version": 1,
                            "component_version": 1,
                            "description": "An updated agent that provides assistance with tool use.",
                            "label": "Assistant Agent",
                            "config": {
                                "name": "assistant_agent",
                                "model_client": {
                                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                                    "component_type": "model",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "Chat completion client for Azure OpenAI hosted models.",
                                    "label": "AzureOpenAIChatCompletionClient",
                                    "config": {
                                        "model": "gpt-4o-mini",
                                        "api_key": "**********",
                                        "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                                        "azure_deployment": "{your-azure-deployment}",
                                        "api_version": "2024-06-01",
                                    },
                                },
                                "workbench": {
                                    "provider": "autogen_core.tools.StaticWorkbench",
                                    "component_type": "workbench",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                                    "label": "StaticWorkbench",
                                    "config": {"tools": []},
                                },
                                "model_context": {
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                                    "label": "UnboundedChatCompletionContext",
                                    "config": {},
                                },
                                "description": "an agent that helps the user",
                                "system_message": "You are a helpful assistant.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": False,
                                "tool_call_summary_format": "{result}",
                                "metadata": {},
                            },
                        },
                        "organization_id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "model_id": "987fcdeb-51a2-4b6c-d789-426614174001",
                        "tool_ids": [
                            "456789ab-cdef-1234-5678-426614174002",
                            "789abcde-f123-4567-89ab-426614174003",
                        ],
                        "tools": [
                            {
                                "id": "456789ab-cdef-1234-5678-426614174002",
                                "component": {
                                    "provider": "autogen_core.tools.FunctionTool",
                                    "component_type": "tool",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "Calculator tool for mathematical operations",
                                    "label": "CalculatorTool",
                                    "config": {
                                        "source_code": "def calculate(expression: str) -> float:\n    return eval(expression)",
                                        "name": "calculator",
                                        "description": "Perform mathematical calculations",
                                    },
                                },
                                "organization_id": "123e4567-e89b-12d3-a456-426614174000",
                                "created_at": "2023-01-01T00:00:00Z",
                                "updated_at": "2023-01-02T00:00:00Z",
                                "is_deleted": False,
                            },
                            {
                                "id": "789abcde-f123-4567-89ab-426614174003",
                                "component": {
                                    "provider": "autogen_core.tools.FunctionTool",
                                    "component_type": "tool",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "Web search tool for finding information",
                                    "label": "WebSearchTool",
                                    "config": {
                                        "source_code": "def web_search(query: str) -> str:\n    # Implementation here\n    pass",
                                        "name": "web_search",
                                        "description": "Search the web for information",
                                    },
                                },
                                "organization_id": "123e4567-e89b-12d3-a456-426614174000",
                                "created_at": "2023-01-01T00:00:00Z",
                                "updated_at": "2023-01-02T00:00:00Z",
                                "is_deleted": False,
                            },
                        ],
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        "is_default": False,
                    },
                    {
                        "id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "component": {
                            "provider": "autogen_agentchat.agents.AssistantAgent",
                            "component_type": "agent",
                            "version": 1,
                            "component_version": 1,
                            "description": "An updated agent that provides assistance with tool use.",
                            "label": "Assistant Agent",
                            "config": {
                                "name": "assistant_agent",
                                "model_client": {
                                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                                    "component_type": "model",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "Chat completion client for Azure OpenAI hosted models.",
                                    "label": "AzureOpenAIChatCompletionClient",
                                    "config": {
                                        "model": "gpt-4o-mini",
                                        "api_key": "**********",
                                        "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                                        "azure_deployment": "{your-azure-deployment}",
                                        "api_version": "2024-06-01",
                                    },
                                },
                                "workbench": {
                                    "provider": "autogen_core.tools.StaticWorkbench",
                                    "component_type": "workbench",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution.",
                                    "label": "StaticWorkbench",
                                    "config": {"tools": []},
                                },
                                "model_context": {
                                    "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
                                    "component_type": "chat_completion_context",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "An unbounded chat completion context that keeps a view of the all the messages.",
                                    "label": "UnboundedChatCompletionContext",
                                    "config": {},
                                },
                                "description": "an agent that helps the user",
                                "system_message": "You are a helpful assistant.",
                                "model_client_stream": False,
                                "reflect_on_tool_use": False,
                                "tool_call_summary_format": "{result}",
                                "metadata": {},
                            },
                        },
                        "organization_id": "2a5f0a9e-3ad1-4a9d-b4e3-8a3e7724d841",
                        "model_id": "987fcdeb-51a2-4b6c-d789-426614174001",
                        "tool_ids": [
                            "456789ab-cdef-1234-5678-426614174002",
                            "789abcde-f123-4567-89ab-426614174003",
                        ],
                        "tools": [
                            {
                                "id": "456789ab-cdef-1234-5678-426614174002",
                                "component": {
                                    "provider": "autogen_core.tools.FunctionTool",
                                    "component_type": "tool",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "Calculator tool for mathematical operations",
                                    "label": "CalculatorTool",
                                    "config": {
                                        "source_code": "def calculate(expression: str) -> float:\n    return eval(expression)",
                                        "name": "calculator",
                                        "description": "Perform mathematical calculations",
                                    },
                                },
                                "organization_id": "123e4567-e89b-12d3-a456-426614174000",
                                "created_at": "2023-01-01T00:00:00Z",
                                "updated_at": "2023-01-02T00:00:00Z",
                                "is_deleted": False,
                            },
                            {
                                "id": "789abcde-f123-4567-89ab-426614174003",
                                "component": {
                                    "provider": "autogen_core.tools.FunctionTool",
                                    "component_type": "tool",
                                    "version": 1,
                                    "component_version": 1,
                                    "description": "Web search tool for finding information",
                                    "label": "WebSearchTool",
                                    "config": {
                                        "source_code": "def web_search(query: str) -> str:\n    # Implementation here\n    pass",
                                        "name": "web_search",
                                        "description": "Search the web for information",
                                    },
                                },
                                "organization_id": "123e4567-e89b-12d3-a456-426614174000",
                                "created_at": "2023-01-01T00:00:00Z",
                                "updated_at": "2023-01-02T00:00:00Z",
                                "is_deleted": False,
                            },
                        ],
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                        "is_default": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
