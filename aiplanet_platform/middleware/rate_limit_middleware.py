# aiplanet_platform/core/rate_limit_middleware.py
"""
Rate limiting middleware for API responses
"""
import logging
from typing import Callable, Optional, Union
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.orm import Session

from aiplanet_platform.core.rate_limiter import rate_limiter
from aiplanet_platform.models.api_key import APIKey
from aiplanet_platform.services.api_key_service import APIKeyService
from aiplanet_platform.services.user_service import UserService
from aiplanet_platform.core.security import decode_token
from aiplanet_platform.core.enhanced_security import get_rate_limit_headers_for_auth
from aiplanet_platform.models.user import User
from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.config import get_settings


logger = logging.getLogger(__name__)
settings = get_settings()


def serialize_rate_limit_info(rate_info: dict) -> dict:
    """
    Convert datetime objects to timestamps for JSON serialization.

    Args:
        rate_info: Rate limit info dictionary with potential datetime objects

    Returns:
        Dictionary with datetime objects converted to timestamps
    """

    def convert_value(value):
        if hasattr(value, "timestamp"):  # It's a datetime object
            return value.timestamp()
        elif isinstance(value, dict):
            return {k: convert_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [convert_value(item) for item in value]
        else:
            return value

    return convert_value(rate_info)


class EnhancedRateLimitMiddleware(BaseHTTPMiddleware):
    """
    Enhanced middleware to handle rate limiting for both JWT users and API keys.
    This middleware ONLY CHECKS rate limits without decrementing counters.
    The actual decrementing happens in the enhanced_security functions.
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and check rate limiting (without consuming requests).

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or endpoint handler

        Returns:
            Response from the next handler with rate limit headers
        """
        # Initialize auth state
        request.state.auth_entity = None
        request.state.auth_type = None

        # Skip rate limiting for certain paths
        skip_paths = [
            f"{settings.API_V1_PREFIX}/{path}"
            for path in ["/docs", "/openapi.json", "/health", "/metrics"]
        ]
        if any(request.url.path.startswith(path) for path in skip_paths):
            response = await call_next(request)
            return response

        # Try to identify the authentication method and entity
        auth_entity = await self._identify_auth_entity(request)

        if auth_entity:
            request.state.auth_entity = auth_entity
            request.state.auth_type = (
                "jwt" if isinstance(auth_entity, User) else "api_key"
            )

            # Check rate limits WITHOUT decrementing counters
            # The actual decrementing will happen in enhanced_security functions
            if not rate_limiter.check_rate_limit(auth_entity):
                # Rate limit exceeded - return 429 response
                rate_info = rate_limiter.get_rate_limit_info(auth_entity)
                headers = get_rate_limit_headers_for_auth(auth_entity)

                # Convert datetime objects to timestamps for JSON serialization
                serializable_rate_info = serialize_rate_limit_info(rate_info)

                return JSONResponse(
                    status_code=429,
                    content={
                        "detail": "Rate limit exceeded",
                        "error": "too_many_requests",
                        "rate_limit_info": serializable_rate_info,
                    },
                    headers=headers,
                )

        # Process the request (rate limiting will happen in enhanced_security)
        response = await call_next(request)

        # Add rate limit headers to the response if we have an auth entity
        if auth_entity:
            headers = get_rate_limit_headers_for_auth(auth_entity)
            for key, value in headers.items():
                response.headers[key] = value

        return response

    async def _identify_auth_entity(
        self, request: Request
    ) -> Optional[Union[User, APIKey]]:
        """
        Identify the authentication entity (User or APIKey) from the request.

        Args:
            request: The incoming HTTP request

        Returns:
            User or APIKey object if authentication is found, None otherwise
        """
        try:
            # Get database session
            db_gen = get_db()
            db: Session = next(db_gen)

            try:
                # Check for JWT token first
                auth_header = request.headers.get("authorization")
                if auth_header and auth_header.lower().startswith("bearer "):
                    token = auth_header[7:]  # Remove "Bearer " prefix

                    # If it looks like an API key, handle it as such
                    if token.startswith("ak_"):
                        return await self._get_api_key(token, db)
                    else:
                        # Try to decode as JWT
                        return await self._get_user_from_jwt(token, db)

                # Check X-API-Key header
                x_api_key = request.headers.get("x-api-key")
                if x_api_key and x_api_key.startswith("ak_"):
                    return await self._get_api_key(x_api_key, db)

                return None

            finally:
                db.close()

        except Exception as e:
            logger.warning(f"Error identifying auth entity: {e}")
            return None

    async def _get_user_from_jwt(self, token: str, db: Session) -> Optional[User]:
        """
        Get user from JWT token.

        Args:
            token: JWT token string
            db: Database session

        Returns:
            User object if valid, None otherwise
        """
        try:
            payload = decode_token(token)
            user_id = payload.get("sub")

            if user_id:
                user_service = UserService(db)
                user = user_service.validate_and_update_user_by_id(user_id)
                # Check if user is active and organization is active
                if (
                    user
                    # TODO: Add user status and organization status
                    # and user.status == "active"
                    # and user.organization.status == "active"
                ):
                    # Add rate limiting methods if they don't exist (for users without migration)
                    if not hasattr(user, "get_user_identifier"):
                        user.get_user_identifier = lambda: f"user_{user.id}"
                    if not hasattr(user, "has_scope"):
                        user.has_scope = lambda scope: True  # Default: all permissions
                    return user

        except Exception as e:
            logger.debug(f"Error getting user from JWT: {e}")

        return None

    async def _get_api_key(self, api_key_value: str, db: Session) -> Optional[APIKey]:
        """
        Get API key from key value.

        Args:
            api_key_value: API key string
            db: Database session

        Returns:
            APIKey object if valid, None otherwise
        """
        try:
            api_key_service = APIKeyService(db)
            api_key = api_key_service.validate_api_key(api_key_value)

            if api_key and api_key.is_active:
                return api_key

        except Exception as e:
            logger.debug(f"Error getting API key: {e}")

        return None


class RateLimitHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add rate limit headers to API responses"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and add rate limit headers to the response.

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or endpoint handler

        Returns:
            Response with rate limit headers added (if applicable)
        """
        # Process the request
        response = await call_next(request)

        # Check if this was an API key authenticated request
        if hasattr(request.state, "api_key") and request.state.api_key:
            api_key: APIKey = request.state.api_key

            try:
                # Get current rate limit information
                rate_info = rate_limiter.get_rate_limit_info(api_key)

                # Add rate limit headers for minute window
                response.headers["X-RateLimit-Limit-Minute"] = str(
                    api_key.rate_limit_per_minute
                )
                response.headers["X-RateLimit-Remaining-Minute"] = str(
                    max(0, api_key.rate_limit_per_minute - rate_info["minute"]["used"])
                )
                if rate_info["minute"]["reset_at"]:
                    response.headers["X-RateLimit-Reset-Minute"] = str(
                        int(rate_info["minute"]["reset_at"].timestamp())
                    )

                # Add rate limit headers for hour window
                response.headers["X-RateLimit-Limit-Hour"] = str(
                    api_key.rate_limit_per_hour
                )
                response.headers["X-RateLimit-Remaining-Hour"] = str(
                    max(0, api_key.rate_limit_per_hour - rate_info["hour"]["used"])
                )
                if rate_info["hour"]["reset_at"]:
                    response.headers["X-RateLimit-Reset-Hour"] = str(
                        int(rate_info["hour"]["reset_at"].timestamp())
                    )

                # Add rate limit headers for day window
                response.headers["X-RateLimit-Limit-Day"] = str(
                    api_key.rate_limit_per_day
                )
                response.headers["X-RateLimit-Remaining-Day"] = str(
                    max(0, api_key.rate_limit_per_day - rate_info["day"]["used"])
                )
                if rate_info["day"]["reset_at"]:
                    response.headers["X-RateLimit-Reset-Day"] = str(
                        int(rate_info["day"]["reset_at"].timestamp())
                    )

                # Add API key identification header (prefix only for security)
                response.headers["X-API-Key-ID"] = api_key.key_prefix

                # Add usage tracking header
                response.headers["X-Total-Requests"] = str(api_key.total_requests)

            except Exception as e:
                # If there's an error getting rate limit info, don't fail the request
                # Just log it and continue without headers
                logger.warn(f"Warning: Could not add rate limit headers: {e}")

        return response


class RequestTimingMiddleware(BaseHTTPMiddleware):
    """Middleware to add request timing information"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and add timing headers.

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or endpoint handler

        Returns:
            Response with timing headers added
        """
        import time

        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        # Add processing time header
        response.headers["X-Process-Time"] = f"{process_time:.4f}"

        return response


class APIKeyContextMiddleware(BaseHTTPMiddleware):
    """Middleware to set API key context in request state"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Extract API key information and store in request state.

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or endpoint handler

        Returns:
            Response from the next handler
        """
        # Initialize API key state
        request.state.api_key = None
        request.state.auth_type = None

        # Check for API key in headers
        api_key_value = None

        # Check Authorization header
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.lower().startswith("bearer "):
            token = auth_header[7:]  # Remove "Bearer " prefix
            if token.startswith("ak_"):  # This looks like an API key
                api_key_value = token
                request.state.auth_type = "api_key"

        # Check X-API-Key header
        if not api_key_value:
            x_api_key = request.headers.get("x-api-key")
            if x_api_key and x_api_key.startswith("ak_"):
                api_key_value = x_api_key
                request.state.auth_type = "api_key"

        # If no API key found, assume JWT auth
        if not api_key_value and auth_header:
            request.state.auth_type = "jwt"

        # Store the API key value for later use
        if api_key_value:
            request.state.api_key_value = api_key_value

        # Process the request
        response = await call_next(request)

        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers to responses"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Add security headers to all responses.

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or endpoint handler

        Returns:
            Response with security headers added
        """
        response = await call_next(request)

        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        # Add API version header
        response.headers["X-API-Version"] = "1.0"

        # Add powered-by header (optional)
        response.headers["X-Powered-By"] = "AI Planet Platform"

        return response


# Utility function to get rate limit info for a response
def get_rate_limit_headers(api_key: APIKey) -> dict:
    """
    Get rate limit headers for an API key.

    Args:
        api_key: The API key to get rate limit info for

    Returns:
        Dictionary of headers to add to the response
    """
    try:
        rate_info = rate_limiter.get_rate_limit_info(api_key)

        headers = {
            # Minute window
            "X-RateLimit-Limit-Minute": str(api_key.rate_limit_per_minute),
            "X-RateLimit-Remaining-Minute": str(
                max(0, api_key.rate_limit_per_minute - rate_info["minute"]["used"])
            ),
            # Hour window
            "X-RateLimit-Limit-Hour": str(api_key.rate_limit_per_hour),
            "X-RateLimit-Remaining-Hour": str(
                max(0, api_key.rate_limit_per_hour - rate_info["hour"]["used"])
            ),
            # Day window
            "X-RateLimit-Limit-Day": str(api_key.rate_limit_per_day),
            "X-RateLimit-Remaining-Day": str(
                max(0, api_key.rate_limit_per_day - rate_info["day"]["used"])
            ),
            # API key info
            "X-API-Key-ID": api_key.key_prefix,
            "X-Total-Requests": str(api_key.total_requests),
        }

        # Add reset timestamps if available
        if rate_info["minute"]["reset_at"]:
            headers["X-RateLimit-Reset-Minute"] = str(
                int(rate_info["minute"]["reset_at"].timestamp())
            )

        if rate_info["hour"]["reset_at"]:
            headers["X-RateLimit-Reset-Hour"] = str(
                int(rate_info["hour"]["reset_at"].timestamp())
            )

        if rate_info["day"]["reset_at"]:
            headers["X-RateLimit-Reset-Day"] = str(
                int(rate_info["day"]["reset_at"].timestamp())
            )

        return headers

    except Exception as e:
        logger.warn(f"Warning: Could not generate rate limit headers: {e}")
        return {}


# Function to manually add rate limit headers to a response
def add_rate_limit_headers(response: Response, api_key: APIKey) -> None:
    """
    Manually add rate limit headers to a response.

    Args:
        response: The FastAPI response object
        api_key: The API key to get rate limit info for
    """
    headers = get_rate_limit_headers(api_key)
    for key, value in headers.items():
        response.headers[key] = value


# Utility functions for manual rate limit header management
def add_rate_limit_headers_to_response(
    response: Response, auth: Union[User, APIKey]
) -> None:
    """
    Manually add rate limit headers to a response.

    Args:
        response: The FastAPI response object
        auth: The authenticated User or APIKey
    """
    headers = get_rate_limit_headers_for_auth(auth)
    for key, value in headers.items():
        response.headers[key] = value


def check_rate_limit_and_get_headers(auth: Union[User, APIKey]) -> tuple[bool, dict]:
    """
    Check rate limits and get headers for an authenticated entity.

    Args:
        auth: The authenticated User or APIKey

    Returns:
        Tuple of (is_allowed, headers_dict)
    """
    is_allowed = rate_limiter.check_rate_limit(
        auth
    )  # Use check_rate_limit instead of is_allowed
    headers = get_rate_limit_headers_for_auth(auth)
    return is_allowed, headers
