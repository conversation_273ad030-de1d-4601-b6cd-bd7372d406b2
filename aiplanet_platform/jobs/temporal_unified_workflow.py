"""
Unified Temporal workflow combining task construction and team execution
"""
import asyncio
import logging
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional

from temporalio import activity, workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError
from autogen_agentchat.messages import TextMessage

logger = logging.getLogger(__name__)


# Unified data classes
@dataclass
class UnifiedWorkflowInput:
    """Input for unified workflow (task construction + team execution)"""

    run_id: str
    chat_data: Dict[str, Any]  # Raw chat inputs that need processing
    team_config: Dict[str, Any]
    env_vars: Optional[List[Dict[str, Any]]] = None
    user_id: Optional[str] = None
    team_state: Optional[Dict[str, Any]] = None
    # Document intelligence credentials (optional)
    doc_intel_endpoint: Optional[str] = None
    doc_intel_key: Optional[str] = None


@dataclass
class UnifiedWorkflowResult:
    """Result of unified workflow"""

    run_id: str
    status: str
    final_result: Optional[Dict[str, Any]] = None
    messages: Optional[List[Dict[str, Any]]] = None
    team_state: Optional[Dict[str, Any]] = None
    constructed_task: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None
    duration: float = 0.0
    task_construction_duration: float = 0.0
    team_execution_duration: float = 0.0


# Activities for the unified workflow
@activity.defn
async def setup_environment_for_unified_workflow(
    run_id: str, env_vars: Optional[List[Dict[str, Any]]] = None
) -> bool:
    """Setup environment variables for unified workflow execution"""
    try:
        import os

        if env_vars:
            logger.info(f"Setting up environment for unified run {run_id}")
            for env_var in env_vars:
                if isinstance(env_var, dict):
                    name = env_var.get("name")
                    value = env_var.get("value")
                    if name and value:
                        os.environ[name] = value

        return True
    except Exception as e:
        logger.error(f"Failed to setup environment for unified run {run_id}: {e}")
        raise ApplicationError(f"Environment setup failed: {str(e)}")


@activity.defn
async def construct_task_from_chat_data(
    run_id: str,
    chat_data: Dict[str, Any],
    doc_intel_endpoint: Optional[str] = None,
    doc_intel_key: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """Construct task from chat data - unified version with PARALLEL processing for better performance"""
    try:
        import base64
        import io

        logger.info(f"Constructing task for unified run {run_id}")

        chat_inputs = chat_data.get("inputs", [])

        # Separate inputs by type for parallel processing
        text_inputs = []
        file_inputs = []
        url_inputs = []

        for i, input_item in enumerate(chat_inputs):
            input_type = input_item.get("type")
            input_item["_index"] = i  # Track original order

            if input_type in ["TextInput", "ChatInput"]:
                text_inputs.append(input_item)
            elif input_type == "FileInput":
                file_inputs.append(input_item)
            elif input_type == "URLInput":
                url_inputs.append(input_item)
            else:
                raise ApplicationError(f"Invalid input type: {input_type}")

        logger.info(
            f"Processing {len(text_inputs)} text inputs, {len(file_inputs)} file inputs, {len(url_inputs)} URL inputs"
        )

        # Process all input types in parallel
        async def process_text_input(input_item):
            """Process a single text input"""
            try:
                text_message = TextMessage(
                    source="user", content=input_item.get("content", "")
                )
                return (input_item["_index"], text_message.model_dump())
            except Exception as e:
                logger.error(f"Error processing text input {input_item['_index']}: {e}")
                raise

        async def process_file_input(input_item):
            """Process a single file input"""
            try:
                file_type = input_item.get("file_type")
                content = input_item.get("content")

                if not content:
                    raise ApplicationError("File content is missing")

                # Decode base64 content
                decoded_content = base64.b64decode(content)
                processed_content = None

                if file_type == "application/pdf":
                    # Process PDF with Azure Document Intelligence
                    try:
                        from azure.ai.formrecognizer import DocumentAnalysisClient
                        from azure.core.credentials import AzureKeyCredential

                        if not doc_intel_endpoint or not doc_intel_key:
                            raise ApplicationError(
                                "Azure Document Intelligence credentials not provided"
                            )

                        # Use async client for non-blocking processing
                        doc_intel_client = DocumentAnalysisClient(
                            endpoint=doc_intel_endpoint,
                            credential=AzureKeyCredential(doc_intel_key),
                        )

                        stream = io.BytesIO(decoded_content)
                        poller = doc_intel_client.begin_analyze_document(
                            "prebuilt-document", document=stream
                        )
                        result = poller.result()

                        # Extract text from all pages
                        full_text = []
                        for page in result.pages:
                            for line in page.lines:
                                full_text.append(line.content)
                        processed_content = "\\n".join(full_text)

                    except ImportError:
                        raise ApplicationError(
                            "Azure Document Intelligence SDK not available"
                        )

                elif file_type in [
                    "application/json",
                    "text/plain",
                    "text/x-python",
                    "text/csv",
                    "text/x-javascript",
                ]:
                    # For text-based files, decode as UTF-8
                    processed_content = decoded_content.decode("utf-8")
                else:
                    raise ApplicationError(f"Unsupported file type: {file_type}")

                text_message = TextMessage(
                    source="user",
                    content=processed_content,
                    metadata={"file_type": file_type},
                )
                return (input_item["_index"], text_message.model_dump())
            except Exception as e:
                logger.error(f"Error processing file input {input_item['_index']}: {e}")
                raise

        async def process_url_input(input_item):
            """Process a single URL input"""
            try:
                text_message = TextMessage(
                    source="user", content=input_item.get("content", "")
                )
                return (input_item["_index"], text_message.model_dump())
            except Exception as e:
                logger.error(f"Error processing URL input {input_item['_index']}: {e}")
                raise

        # Create tasks for parallel processing
        tasks = []

        # Add text processing tasks
        for text_input in text_inputs:
            tasks.append(process_text_input(text_input))

        # Add file processing tasks (these will run in parallel!)
        for file_input in file_inputs:
            tasks.append(process_file_input(file_input))

        # Add URL processing tasks
        for url_input in url_inputs:
            tasks.append(process_url_input(url_input))

        # Process all inputs in parallel
        if tasks:
            logger.info(f"Starting parallel processing of {len(tasks)} inputs...")
            start_time = asyncio.get_event_loop().time()

            # Use asyncio.gather to process all inputs concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            end_time = asyncio.get_event_loop().time()
            logger.info(
                f"Parallel processing completed in {end_time - start_time:.2f} seconds"
            )

            # Check for any exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Task {i} failed: {result}")
                    raise ApplicationError(f"Input processing failed: {str(result)}")

            # Sort results by original index to maintain order
            sorted_results = sorted(results, key=lambda x: x[0])
            task = [result[1] for result in sorted_results]
        else:
            task = []

        logger.info(f"Task construction completed for run {run_id}, {len(task)} items")

        # Ensure task is always a list of dictionaries for type safety
        if not isinstance(task, list):
            logger.warning(
                f"Task construction returned non-list: {type(task)}, converting to list"
            )
            task = [task] if task else []

        # Validate each item in the task is a dictionary
        validated_task = []
        for i, item in enumerate(task):
            if isinstance(item, dict):
                validated_task.append(item)
            else:
                logger.warning(f"Task item {i} is not a dict: {type(item)}, converting")
                validated_task.append(
                    {"source": "user", "content": str(item), "metadata": {}}
                )

        return validated_task

    except Exception as e:
        logger.error(f"Task construction failed for run {run_id}: {e}")
        raise ApplicationError(f"Task construction failed: {str(e)}")


@activity.defn
async def execute_team_stream_unified(
    run_id: str,
    task: List[Dict[str, Any]],
    team_config: Dict[str, Any],
    user_id: Optional[str] = None,
    team_state: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """Execute team streaming with heartbeat for non-blocking execution"""
    try:
        # Import here to avoid circular imports
        from aiplanet_platform.services.team_manager_service import TeamManagerService
        from aiplanet_platform.core.database import get_db
        from aiplanet_platform.constants.team_manager import TeamResult
        from sqlalchemy.orm import Session

        logger.info(f"Starting unified team execution for run {run_id}")

        # Create database session with connection pooling awareness
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            # Create team manager service
            team_manager = TeamManagerService(db)

            # Execute team stream and collect results
            final_result = None
            messages = []
            clean_task = (
                [
                    TextMessage(
                        source=input_data["source"],
                        content=input_data["content"],
                        metadata=input_data.get("metadata", {}),
                    )
                    for input_data in task
                ]
                if isinstance(task, list)
                else task
            )

            # Add heartbeat and async yielding for non-blocking execution
            message_count = 0
            async for message in team_manager.run_stream(
                task=clean_task,
                team_config=team_config,
                input_func=None,  # Will be handled by websocket layer
                cancellation_token=None,  # Temporal handles cancellation
                env_vars=None,  # Already set up in previous activity
                state=team_state,
            ):
                # Send heartbeat every few messages to keep activity alive
                message_count += 1
                if message_count % 5 == 0:
                    activity.heartbeat(f"Processing message {message_count}")

                # Check if this is the final result
                if isinstance(message, TeamResult):
                    final_result = message.model_dump()
                    logger.info(f"Final result: {final_result}")
                # Collect messages for final result
                elif hasattr(message, "model_dump"):
                    message_dict = message.model_dump()
                    messages.append(message_dict)

                # Yield control to allow other async operations (non-blocking)
                await asyncio.sleep(0)

            # Save final state
            if hasattr(team_manager, "_team") and team_manager._team:
                team_state = await team_manager.save_state()
            else:
                team_state = None

            return {
                "final_result": final_result,
                "team_state": team_state,
                "messages": messages,
                "status": "completed",
            }

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Unified team execution failed for run {run_id}: {e}")
        raise ApplicationError(f"Team execution failed: {str(e)}")


@activity.defn
async def update_run_status_unified(
    run_id: str,
    status: str,
    team_result: Optional[Dict[str, Any]] = None,
    constructed_task: Optional[List[Dict[str, Any]]] = None,
    error: Optional[str] = None,
) -> bool:
    """Update run status in database for unified workflow"""
    try:
        from aiplanet_platform.services.run_service import RunService
        from aiplanet_platform.core.database import get_db
        from aiplanet_platform.constants.run_context import RunStatus
        from sqlalchemy.orm import Session
        from uuid import UUID

        logger.info(f"Updating unified run status for {run_id}: {status}")

        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            run_service = RunService(db)
            run = run_service.fetch_resource_by_id(UUID(run_id))

            if run:
                # Map status string to RunStatus enum
                status_mapping = {
                    "active": RunStatus.ACTIVE,
                    "task_construction": RunStatus.ACTIVE,
                    "team_execution": RunStatus.ACTIVE,
                    "completed": RunStatus.COMPLETE,
                    "stopped": RunStatus.STOPPED,
                    "failed": RunStatus.ERROR,
                }

                run.status = status_mapping.get(status, RunStatus.ERROR)

                if team_result:
                    run.team_result = team_result
                if constructed_task:
                    # Store constructed task for debugging/reference
                    if not hasattr(run, "metadata"):
                        run.metadata = {}
                    run.metadata["constructed_task"] = constructed_task
                if error:
                    run.error = error

                run_service.update_resource_by_id(UUID(run_id), run.__dict__)
                return True
            else:
                logger.warning(f"Run {run_id} not found for status update")
                return False

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Failed to update unified run status for {run_id}: {e}")
        raise ApplicationError(f"Status update failed: {str(e)}")


# Main Unified Workflow
@workflow.defn
class UnifiedWorkflow:
    """Unified Temporal workflow for task construction + team execution"""

    @workflow.run
    async def run(self, input_data: UnifiedWorkflowInput) -> UnifiedWorkflowResult:
        """Main unified workflow execution"""

        start_time = workflow.now()
        run_id = input_data.run_id

        logger.info(f"Starting unified workflow for run {run_id}")

        try:
            # Step 1: Setup environment
            await workflow.execute_activity(
                setup_environment_for_unified_workflow,
                args=[run_id, input_data.env_vars],
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=10),
                    maximum_attempts=3,
                ),
            )

            # Step 2: Update status to active (task construction phase)
            await workflow.execute_activity(
                update_run_status_unified,
                args=[run_id, "task_construction"],
                start_to_close_timeout=timedelta(minutes=1),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=5),
                    maximum_attempts=3,
                ),
            )

            # Step 3: Construct task from chat data
            task_construction_start = workflow.now()
            constructed_task = await workflow.execute_activity(
                construct_task_from_chat_data,
                args=[
                    run_id,
                    input_data.chat_data,
                    input_data.doc_intel_endpoint,
                    input_data.doc_intel_key,
                ],
                start_to_close_timeout=timedelta(
                    minutes=10
                ),  # Allow time for file processing
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=2),
                    maximum_interval=timedelta(seconds=30),
                    maximum_attempts=2,
                ),
            )
            task_construction_duration = (
                workflow.now() - task_construction_start
            ).total_seconds()

            # Step 4: Update status to team execution phase
            await workflow.execute_activity(
                update_run_status_unified,
                args=[run_id, "team_execution", None, constructed_task],
                start_to_close_timeout=timedelta(minutes=1),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=5),
                    maximum_attempts=3,
                ),
            )

            # Step 5: Execute team streaming (main activity)
            team_execution_start = workflow.now()
            execution_result = await workflow.execute_activity(
                execute_team_stream_unified,
                args=[
                    run_id,
                    constructed_task,
                    input_data.team_config,
                    input_data.user_id,
                    input_data.team_state,
                ],
                start_to_close_timeout=timedelta(
                    hours=1
                ),  # Long timeout for team execution
                heartbeat_timeout=timedelta(minutes=5),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=5),
                    maximum_interval=timedelta(minutes=1),
                    maximum_attempts=2,  # Limited retries for long-running activities
                ),
            )
            team_execution_duration = (
                workflow.now() - team_execution_start
            ).total_seconds()

            # Step 6: Update final status
            await workflow.execute_activity(
                update_run_status_unified,
                args=[run_id, "completed", execution_result.get("final_result")],
                start_to_close_timeout=timedelta(minutes=1),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=5),
                    maximum_attempts=3,
                ),
            )

            total_duration = (workflow.now() - start_time).total_seconds()

            return UnifiedWorkflowResult(
                run_id=run_id,
                status="completed",
                final_result=execution_result.get("final_result"),
                messages=execution_result.get("messages"),
                team_state=execution_result.get("team_state"),
                constructed_task=constructed_task,
                duration=total_duration,
                task_construction_duration=task_construction_duration,
                team_execution_duration=team_execution_duration,
            )

        except Exception as e:
            logger.error(f"Unified workflow failed for run {run_id}: {e}")

            # Update status to failed
            try:
                await workflow.execute_activity(
                    update_run_status_unified,
                    args=[run_id, "failed", None, None, str(e)],
                    start_to_close_timeout=timedelta(minutes=1),
                )
            except Exception as update_error:
                logger.error(f"Failed to update error status: {update_error}")

            total_duration = (workflow.now() - start_time).total_seconds()

            return UnifiedWorkflowResult(
                run_id=run_id, status="failed", error=str(e), duration=total_duration
            )
