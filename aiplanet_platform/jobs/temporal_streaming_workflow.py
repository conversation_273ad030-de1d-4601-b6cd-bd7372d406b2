"""
Temporal workflow for team streaming functionality - CORRECTED VERSION
"""
import logging
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional

from temporalio import activity, workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError
from autogen_agentchat.messages import TextMessage

logger = logging.getLogger(__name__)


# Data classes for workflow inputs/outputs
@dataclass
class TeamStreamingWorkflowInput:
    """Input for team streaming workflow"""

    run_id: str
    task: List[Dict[str, Any]]
    team_config: Dict[str, Any]
    env_vars: Optional[List[Dict[str, Any]]] = None
    user_id: Optional[str] = None
    team_state: Optional[Dict[str, Any]] = None


@dataclass
class TeamStreamingWorkflowResult:
    """Result of team streaming workflow"""

    run_id: str
    status: str
    final_result: Optional[Dict[str, Any]] = None
    messages: Optional[List[Dict[str, Any]]] = None
    team_state: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    duration: float = 0.0


# Activities
@activity.defn
async def setup_team_environment(
    run_id: str, env_vars: Optional[List[Dict[str, Any]]] = None
) -> bool:
    """Setup environment variables for team execution"""
    try:
        import os

        if env_vars:
            logger.info(f"Setting up environment for run {run_id}")
            for env_var in env_vars:
                if isinstance(env_var, dict):
                    name = env_var.get("name")
                    value = env_var.get("value")
                    if name and value:
                        os.environ[name] = value

        return True
    except Exception as e:
        logger.error(f"Failed to setup environment for run {run_id}: {e}")
        raise ApplicationError(f"Environment setup failed: {str(e)}")


@activity.defn
async def execute_team_stream(
    run_id: str,
    task: List[Dict[str, Any]],  # no more Union[str, Dict, BaseMessage]
    team_config: Dict[str, Any],
    user_id: Optional[str] = None,
    team_state: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """Execute team streaming and return final result"""
    try:
        # Import here to avoid circular imports
        from aiplanet_platform.services.team_manager_service import TeamManagerService
        from aiplanet_platform.core.database import get_db
        from aiplanet_platform.constants.team_manager import TeamResult
        from sqlalchemy.orm import Session

        logger.info(f"Starting team execution for run {run_id}")

        # Create database session with connection pooling awareness
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            # Create team manager service
            team_manager = TeamManagerService(db)
            # Execute team stream and collect results
            final_result = None
            messages = []
            clean_task = (
                [
                    TextMessage(
                        source=input["source"],
                        content=input["content"],
                        metadata=input["metadata"],
                    )
                    for input in task
                ]
                if isinstance(task, list)
                else task
            )
            # Add heartbeat to prevent Temporal from timing out on long operations
            import asyncio
            from temporalio import activity

            message_count = 0
            async for message in team_manager.run_stream(
                task=clean_task,
                team_config=team_config,
                input_func=None,  # Will be handled by websocket layer
                cancellation_token=None,  # Temporal handles cancellation
                env_vars=None,  # Already set up in previous activity
                state=team_state,
            ):
                # Send heartbeat every few messages to keep activity alive
                message_count += 1
                if message_count % 5 == 0:
                    activity.heartbeat(f"Processing message {message_count}")

                # Check if this is the final result
                if isinstance(message, TeamResult):
                    final_result = message.model_dump()
                    logger.info(f"Final result: {final_result}")
                # Collect messages for final result
                elif hasattr(message, "model_dump"):
                    message_dict = message.model_dump()
                    messages.append(message_dict)

                # Yield control to allow other async operations
                await asyncio.sleep(0)

            # Save final state
            if hasattr(team_manager, "_team") and team_manager._team:
                team_state = await team_manager.save_state()
            else:
                team_state = None

            return {
                "final_result": final_result,
                "team_state": team_state,
                "messages": messages,
                "status": "completed",
            }

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Team execution failed for run {run_id}: {e}")
        raise ApplicationError(f"Team execution failed: {str(e)}")


@activity.defn
async def update_run_status(
    run_id: str,
    status: str,
    team_result: Optional[Dict[str, Any]] = None,
    error: Optional[str] = None,
) -> bool:
    """Update run status in database"""
    try:
        from aiplanet_platform.services.run_service import RunService
        from aiplanet_platform.core.database import get_db
        from aiplanet_platform.constants.run_context import RunStatus
        from sqlalchemy.orm import Session
        from uuid import UUID

        logger.info(f"Updating run status for {run_id}: {status}")

        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            run_service = RunService(db)
            run = run_service.fetch_resource_by_id(UUID(run_id))

            if run:
                # Map status string to RunStatus enum
                status_mapping = {
                    "active": RunStatus.ACTIVE,
                    "completed": RunStatus.COMPLETE,
                    "stopped": RunStatus.STOPPED,
                    "failed": RunStatus.ERROR,
                }

                run.status = status_mapping.get(status, RunStatus.ERROR)

                if team_result:
                    run.team_result = team_result
                if error:
                    run.error = error

                run_service.update_resource_by_id(UUID(run_id), run.__dict__)
                return True
            else:
                logger.warning(f"Run {run_id} not found for status update")
                return False

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Failed to update run status for {run_id}: {e}")
        raise ApplicationError(f"Status update failed: {str(e)}")


# Main Workflow
@workflow.defn
class TeamStreamingWorkflow:
    """Temporal workflow for team streaming execution"""

    @workflow.run
    async def run(
        self, input_data: TeamStreamingWorkflowInput
    ) -> TeamStreamingWorkflowResult:
        """Main workflow execution"""

        start_time = workflow.now()
        run_id = input_data.run_id

        logger.info(f"Starting team streaming workflow for run {run_id}")

        try:
            # Step 1: Setup environment
            await workflow.execute_activity(
                setup_team_environment,
                args=[run_id, input_data.env_vars],
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=10),
                    maximum_attempts=3,
                ),
            )

            # Step 2: Update run status to active
            await workflow.execute_activity(
                update_run_status,
                args=[run_id, "active"],
                start_to_close_timeout=timedelta(minutes=1),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=5),
                    maximum_attempts=3,
                ),
            )

            # Step 3: Execute team streaming (main activity)
            execution_result = await workflow.execute_activity(
                execute_team_stream,
                args=[
                    run_id,
                    input_data.task,
                    input_data.team_config,
                    input_data.user_id,
                ],
                start_to_close_timeout=timedelta(
                    hours=1
                ),  # Long timeout for team execution
                heartbeat_timeout=timedelta(minutes=5),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=5),
                    maximum_interval=timedelta(minutes=1),
                    maximum_attempts=2,  # Limited retries for long-running activities
                ),
            )

            # Step 4: Update final status
            await workflow.execute_activity(
                update_run_status,
                args=[run_id, "completed", execution_result.get("final_result")],
                start_to_close_timeout=timedelta(minutes=1),
                retry_policy=RetryPolicy(
                    initial_interval=timedelta(seconds=1),
                    maximum_interval=timedelta(seconds=5),
                    maximum_attempts=3,
                ),
            )

            duration = (workflow.now() - start_time).total_seconds()

            return TeamStreamingWorkflowResult(
                run_id=run_id,
                status="completed",
                final_result=execution_result.get("final_result"),
                duration=duration,
                messages=execution_result.get("messages"),
                team_state=execution_result.get("team_state"),
            )

        except Exception as e:
            logger.error(f"Workflow failed for run {run_id}: {e}")

            # Update status to failed
            try:
                await workflow.execute_activity(
                    update_run_status,
                    args=[run_id, "failed", None, str(e)],
                    start_to_close_timeout=timedelta(minutes=1),
                )
            except Exception as update_error:
                logger.error(f"Failed to update error status: {update_error}")

            duration = (workflow.now() - start_time).total_seconds()

            return TeamStreamingWorkflowResult(
                run_id=run_id, status="failed", error=str(e), duration=duration
            )
