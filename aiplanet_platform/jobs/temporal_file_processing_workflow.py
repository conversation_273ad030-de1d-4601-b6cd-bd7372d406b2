"""
Temporal workflow for task construction functionality
"""
import base64
import io
import logging
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional

from temporalio import activity, workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError
from autogen_agentchat.messages import TextMessage
from aiplanet_platform.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


# Data classes for workflow inputs/outputs
@dataclass
class TaskConstructionWorkflowInput:
    """Input for task construction workflow"""

    run_id: str
    chat_data: Dict[str, Any]  # Serialized RunChatMessage
    user_id: Optional[str] = None


@dataclass
class TaskConstructionWorkflowResult:
    """Result of task construction workflow"""

    run_id: str
    status: str
    task: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None
    duration: float = 0.0


# Activities
@activity.defn
async def process_text_input(run_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process text input and return TextMessage dict"""
    try:
        logger.info(f"Processing text input for run {run_id}")

        text_message = TextMessage(source="user", content=input_data.get("content", ""))

        return text_message.model_dump()

    except Exception as e:
        logger.error(f"Failed to process text input for run {run_id}: {e}")
        raise ApplicationError(f"Text input processing failed: {str(e)}")


@activity.defn
async def process_file_input_with_client(
    run_id: str,
    input_data: Dict[str, Any],
    doc_intel_endpoint: Optional[str] = None,
    doc_intel_key: Optional[str] = None,
) -> Dict[str, Any]:
    """Process file input with document intelligence client creation"""
    try:
        logger.info(
            f"Processing file input with client for run {run_id}, type: {input_data.get('file_type')}"
        )

        file_type = input_data.get("file_type")
        content = input_data.get("content")

        if not content:
            raise ApplicationError("File content is missing")

        # Decode base64 content
        decoded_content = base64.b64decode(content)
        processed_content = None

        if file_type == "application/pdf":
            # Import Azure Document Intelligence client
            try:
                from azure.ai.documentintelligence import DocumentIntelligenceClient
                from azure.core.credentials import AzureKeyCredential

                if not doc_intel_endpoint or not doc_intel_key:
                    raise ApplicationError(
                        "Azure Document Intelligence credentials not provided"
                    )

                # Create client
                doc_intel_client = DocumentIntelligenceClient(
                    endpoint=doc_intel_endpoint,
                    credential=AzureKeyCredential(doc_intel_key),
                )

                # Process PDF
                stream = io.BytesIO(decoded_content)
                poller = doc_intel_client.begin_analyze_document(
                    "prebuilt-document", document=stream
                )
                result = poller.result()

                # Extract text from all pages
                full_text = []
                for page in result.pages:
                    for line in page.lines:
                        full_text.append(line.content)
                processed_content = "\n".join(full_text)

            except ImportError:
                raise ApplicationError("Azure Document Intelligence SDK not available")

        elif file_type in [
            "application/json",
            "text/plain",
            "text/x-python",
            "text/csv",
            "text/x-javascript",
        ]:
            # For text-based files, decode as UTF-8
            processed_content = decoded_content.decode("utf-8")
        else:
            raise ApplicationError(f"Unsupported file type: {file_type}")

        text_message = TextMessage(
            source="user", content=processed_content, metadata={"file_type": file_type}
        )

        return text_message.model_dump()

    except Exception as e:
        logger.error(f"Failed to process file input for run {run_id}: {e}")
        raise ApplicationError(f"File input processing failed: {str(e)}")


@activity.defn
async def process_url_input(run_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process URL input and return TextMessage dict"""
    try:
        logger.info(f"Processing URL input for run {run_id}")

        text_message = TextMessage(source="user", content=input_data.get("content", ""))

        return text_message.model_dump()

    except Exception as e:
        logger.error(f"Failed to process URL input for run {run_id}: {e}")
        raise ApplicationError(f"URL input processing failed: {str(e)}")


# Main Workflow
@workflow.defn
class TaskConstructionWorkflow:
    """Temporal workflow for task construction"""

    @workflow.run
    async def run(
        self, input_data: TaskConstructionWorkflowInput
    ) -> TaskConstructionWorkflowResult:
        """Main workflow execution"""

        start_time = workflow.now()
        run_id = input_data.run_id
        doc_intel_endpoint = settings.DOCUMENT_INTEL_ENDPOINT
        doc_intel_key = settings.DOCUMENT_INTEL_KEY
        logger.info(f"Starting task construction workflow for run {run_id}")

        try:
            # Step 1: Process all inputs
            task = []
            chat_inputs = input_data.chat_data.get("inputs", [])

            for input_item in chat_inputs:
                input_type = input_item.get("type")

                if input_type == "TextInput" or input_type == "ChatInput":
                    result = await workflow.execute_activity(
                        process_text_input,
                        args=[run_id, input_item],
                        start_to_close_timeout=timedelta(minutes=5),
                        retry_policy=RetryPolicy(
                            initial_interval=timedelta(seconds=1),
                            maximum_interval=timedelta(seconds=10),
                            maximum_attempts=3,
                        ),
                    )
                    task.append(result)

                elif input_type == "FileInput":
                    result = await workflow.execute_activity(
                        process_file_input_with_client,
                        args=[run_id, input_item, doc_intel_endpoint, doc_intel_key],
                        start_to_close_timeout=timedelta(
                            minutes=10
                        ),  # Longer for file processing
                        retry_policy=RetryPolicy(
                            initial_interval=timedelta(seconds=2),
                            maximum_interval=timedelta(seconds=30),
                            maximum_attempts=2,  # Limited retries for file processing
                        ),
                    )
                    task.append(result)

                elif input_type == "URLInput":
                    result = await workflow.execute_activity(
                        process_url_input,
                        args=[run_id, input_item],
                        start_to_close_timeout=timedelta(minutes=5),
                        retry_policy=RetryPolicy(
                            initial_interval=timedelta(seconds=1),
                            maximum_interval=timedelta(seconds=10),
                            maximum_attempts=3,
                        ),
                    )
                    task.append(result)

                else:
                    # Invalid input type - this will cause the workflow to fail
                    raise ApplicationError(f"Invalid input type: {input_type}")

            duration = (workflow.now() - start_time).total_seconds()

            return TaskConstructionWorkflowResult(
                run_id=run_id,
                status="completed",
                task=task,
                duration=duration,
            )

        except Exception as e:
            logger.error(f"Task construction workflow failed for run {run_id}: {e}")
            duration = (workflow.now() - start_time).total_seconds()

            return TaskConstructionWorkflowResult(
                run_id=run_id, status="failed", error=str(e), duration=duration
            )
