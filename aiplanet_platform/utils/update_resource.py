"""
Utility functions for update resource
"""
import logging
import re
from datetime import datetime, date
from typing import Any, Dict, Optional

from fastapi import HTTPException, status


logger = logging.getLogger(__name__)


def update_component_fields(current_component, incoming_component):
    """
    Compare and update component fields recursively.

    Rules:
    - If value not present in incoming, skip it (keep current)
    - If value present in both, compare and use incoming value if different
    - If value only in incoming, add it

    Args:
        current_component: Current component dict from database
        incoming_component: Incoming component dict with updates

    Returns:
        Updated component dict
    """
    # Handle None cases
    if current_component is None:
        current_component = {}
    if incoming_component is None:
        return current_component

    # Start with a copy of current component
    updated = current_component.copy()

    # Recursively process each field in incoming component
    for key, incoming_value in incoming_component.items():
        if key in current_component:
            current_value = current_component[key]

            # If both are dicts, recurse
            if isinstance(current_value, dict) and isinstance(incoming_value, dict):
                updated[key] = update_component_fields(current_value, incoming_value)
            # If values are different, use incoming value
            elif current_value != incoming_value:
                updated[key] = incoming_value
            # If values are same, keep current (no change needed)
        else:
            # Key not in current, add it
            updated[key] = incoming_value

    return updated


def validate_update_resource_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate update_resource data.

    Args:
        data: Dictionary of update_resource data

    Returns:
        Validated data

    Raises:
        HTTPException: If validation fails
    """
    # Example validation
    required_fields = ["name"]
    for field in required_fields:
        if field not in data or not data[field]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Field '{}' is required for update_resource".format(field),
            )

    # Validate specific fields
    if "name" in data and len(data["name"]) < 3:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Field 'name' must be at least 3 characters long",
        )

    if "status" in data and data["status"] not in [
        "active",
        "inactive",
        "pending",
        "archived",
    ]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid status value: {}".format(data["status"]),
        )

    return data


def format_update_resource_response(update_resource: Any) -> Dict[str, Any]:
    """
    Format update_resource for API response.

    Args:
        update_resource: Update_Resource object

    Returns:
        Formatted update_resource data
    """
    return {
        "id": update_resource.id,
        "name": update_resource.name,
        "description": update_resource.description,
        "created_at": update_resource.created_at.isoformat()
        if update_resource.created_at
        else None,
        "updated_at": update_resource.updated_at.isoformat()
        if update_resource.updated_at
        else None,
        "is_active": not update_resource.is_deleted,
        # Add other fields as needed
    }


def parse_update_resource_filters(query_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse and validate query parameters for filtering update_resources.

    Args:
        query_params: Dictionary of query parameters

    Returns:
        Validated filters
    """
    filters = {}

    # Process each query parameter
    for key, value in query_params.items():
        # Skip pagination parameters
        if key in ["skip", "limit", "sort_by", "sort_order"]:
            continue

        # Handle special filter operators (e.g., created_at_gt, name_like)
        if "_" in key:
            field, operator = key.rsplit("_", 1)

            # Handle specific operators
            if operator == "like":
                filters["{}{}".format(field, "_like")] = value
            elif operator in ["gt", "lt", "gte", "lte", "ne", "in"]:
                filters["{}{}".format(field, "_" + operator)] = value
            else:
                # Direct equals filter for fields with underscore
                filters[key] = value
        else:
            # Direct equals filter
            filters[key] = value

    return filters


def snake_to_camel(snake_str: str) -> str:
    """
    Convert snake_case to camelCase.

    Args:
        snake_str: String in snake_case

    Returns:
        String in camelCase
    """
    components = snake_str.split("_")
    return components[0] + "".join(x.title() for x in components[1:])


def camel_to_snake(camel_str: str) -> str:
    """
    Convert camelCase to snake_case.

    Args:
        camel_str: String in camelCase

    Returns:
        String in snake_case
    """
    snake_str = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", camel_str)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", snake_str).lower()


def model_to_dict(model: Any) -> Dict[str, Any]:
    """
    Convert SQLAlchemy model to dictionary.

    Args:
        model: SQLAlchemy model instance

    Returns:
        Dictionary representation of the model
    """
    result = {}
    for key, value in model.__dict__.items():
        if not key.startswith("_"):
            if isinstance(value, (datetime, date)):
                value = value.isoformat()
            result[key] = value
    return result


def dict_to_model(model_class: Any, data: Dict[str, Any]) -> Any:
    """
    Convert dictionary to SQLAlchemy model.

    Args:
        model_class: SQLAlchemy model class
        data: Dictionary of data

    Returns:
        SQLAlchemy model instance
    """
    return model_class(**data)


def paginate_results(query: Any, page: int = 1, page_size: int = 100) -> Dict[str, Any]:
    """
    Paginate database query results.

    Args:
        query: SQLAlchemy query
        page: Page number (1-indexed)
        page_size: Number of items per page

    Returns:
        Dictionary with paginated results
    """
    total = query.count()
    pages = (total + page_size - 1) // page_size if page_size > 0 else 1

    if page < 1:
        page = 1
    elif page > pages and pages > 0:
        page = pages

    items = query.offset((page - 1) * page_size).limit(page_size).all()

    return {
        "items": items,
        "total": total,
        "page": page,
        "pages": pages,
        "size": page_size,
    }


def filter_query_by_date_range(
    query: Any,
    model: Any,
    field_name: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
) -> Any:
    """
    Filter query by date range.

    Args:
        query: SQLAlchemy query
        model: SQLAlchemy model class
        field_name: Name of the date field
        start_date: Start date (inclusive)
        end_date: End date (inclusive)

    Returns:
        Filtered SQLAlchemy query
    """
    if start_date:
        query = query.filter(getattr(model, field_name) >= start_date)
    if end_date:
        query = query.filter(getattr(model, field_name) <= end_date)
    return query


def get_update_resource_by_id_or_404(service: Any, update_resource_id: int) -> Any:
    """
    Get update_resource by ID or raise 404 error.

    Args:
        service: Update_Resource service
        update_resource_id: ID of the update_resource

    Returns:
        Update_Resource object

    Raises:
        HTTPException: If update_resource not found
    """
    update_resource = service.fetch_resource_by_id(update_resource_id)
    if not update_resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Update_Resource with ID {} not found".format(update_resource_id),
        )
    return update_resource
