from fastapi import HTTPException
from sqlalchemy.orm import Session
from aiplanet_platform.models.file import File


def sanitize_class_name(folder_id: str) -> str:
    try:
        transformed = "Id_" + folder_id.replace("-", "_")
        return transformed
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error sanitizing class name: {str(e)}"
        )


def generate_unique_filename(name: str, folder_id: str, db: Session) -> str:
    """
    Generate a unique filename within the folder.
    """
    base_name, file_extension = name.rsplit(".", 1)
    unique_name = name
    counter = 1

    # Query database for existing files in the folder
    existing_files = db.query(File.name).filter(File.folderid == folder_id).all()
    existing_filenames = {file.name for file in existing_files}

    # Ensure the filename is unique by adding a counter
    while unique_name in existing_filenames:
        unique_name = f"{base_name} ({counter}).{file_extension}"
        counter += 1

    return unique_name
