"""
Constants related to output
"""
from enum import Enum
from typing import Any, Dict, Literal, Optional

from pydantic import BaseModel


# Output Configuration Classes
class JSONOutputConfig(BaseModel):
    """Configuration for JSON output."""

    indent: Optional[int] = 2
    ensure_ascii: bool = False
    sort_keys: bool = False
    output_path: Optional[str] = None


class TextOutputConfig(BaseModel):
    """Configuration for text output."""

    encoding: str = "utf-8"
    output_path: Optional[str] = None
    line_ending: Literal["lf", "crlf", "auto"] = "auto"


class MarkdownOutputConfig(BaseModel):
    """Configuration for markdown output."""

    encoding: str = "utf-8"
    output_path: Optional[str] = None
    include_toc: bool = False
    heading_level: int = 1


class OutputStatus(str, Enum):
    """Status enum for output"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    ARCHIVED = "archived"
    DELETED = "deleted"


class OutputType(str, Enum):
    """Type enum for output"""

    TYPE_A = "type_a"
    TYPE_B = "type_b"
    TYPE_C = "type_c"
    # Add your custom types here


# Constants for output
OUTPUT_DEFAULT_LIMIT = 100
OUTPUT_MAX_LIMIT = 1000
OUTPUT_MIN_NAME_LENGTH = 3
OUTPUT_MAX_NAME_LENGTH = 255

# Error messages
OUTPUT_NOT_FOUND = "Output not found"
OUTPUT_ALREADY_EXISTS = "Output with this name already exists"
OUTPUT_INVALID_STATUS = "Invalid status for output"
OUTPUT_PERMISSION_DENIED = "Permission denied for this output"

# Validation rules
OUTPUT_VALIDATION_RULES = {
    "name": {
        "min_length": OUTPUT_MIN_NAME_LENGTH,
        "max_length": OUTPUT_MAX_NAME_LENGTH,
        "required": True,
    },
    "description": {
        "required": False,
    },
    # Add more validation rules as needed
}

# Default values
OUTPUT_DEFAULT_CONFIG: Dict[str, Any] = {
    "timeout": 30,
    "retry_attempts": 3,
    "cache_ttl": 300,
    "status": OutputStatus.PENDING.value,
}

# Cache keys
OUTPUT_CACHE_KEY_PREFIX = "output"
OUTPUT_LIST_CACHE_KEY = "{}:list".format(OUTPUT_CACHE_KEY_PREFIX)
OUTPUT_DETAIL_CACHE_KEY = "{}:detail:{}".format(
    OUTPUT_CACHE_KEY_PREFIX, "{}"
)  # Format with ID

# Rate limiting
OUTPUT_RATE_LIMIT = {
    "create": {
        "rate": 10,
        "per": 60,  # seconds
    },
    "update": {
        "rate": 20,
        "per": 60,  # seconds
    },
    "delete": {
        "rate": 5,
        "per": 60,  # seconds
    },
}
