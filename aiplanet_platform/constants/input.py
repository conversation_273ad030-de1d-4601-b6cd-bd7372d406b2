"""
Constants related to input
"""

from enum import Enum
from typing import Any, Dict, Literal, Optional

from pydantic import BaseModel


# Input Configuration Classes
class FileInputConfig(BaseModel):
    """Configuration for file-based input."""

    encoding: Optional[str] = "utf-8"
    file_type: Optional[str] = None
    required: bool = True


class URLInputConfig(BaseModel):
    """Configuration for URL-based input."""

    url: str
    headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = 30
    verify_ssl: bool = True
    required: bool = True


class TextInputConfig(BaseModel):
    """Configuration for direct text input."""

    content: str
    encoding: Optional[str] = "utf-8"
    required: bool = True


class ImageInputConfig(BaseModel):
    """Configuration for image input."""

    source: str
    source_type: Literal["file", "url", "base64"] = "file"
    format: Optional[str] = None
    required: bool = True


class ChatInputConfig(BaseModel):
    """Configuration for chat input."""

    text: str
    required: bool = True


class InputStatus(str, Enum):
    """Status enum for input"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    ARCHIVED = "archived"
    DELETED = "deleted"


class InputType(str, Enum):
    """Type enum for input"""

    TYPE_A = "type_a"
    TYPE_B = "type_b"
    TYPE_C = "type_c"
    # Add your custom types here


# Constants for input
INPUT_DEFAULT_LIMIT = 100
INPUT_MAX_LIMIT = 1000
INPUT_MIN_NAME_LENGTH = 3
INPUT_MAX_NAME_LENGTH = 255

# Error messages
INPUT_NOT_FOUND = "Input not found"
INPUT_ALREADY_EXISTS = "Input with this name already exists"
INPUT_INVALID_STATUS = "Invalid status for input"
INPUT_PERMISSION_DENIED = "Permission denied for this input"

# Validation rules
INPUT_VALIDATION_RULES = {
    "name": {
        "min_length": INPUT_MIN_NAME_LENGTH,
        "max_length": INPUT_MAX_NAME_LENGTH,
        "required": True,
    },
    "description": {
        "required": False,
    },
    # Add more validation rules as needed
}

# Default values
INPUT_DEFAULT_CONFIG: Dict[str, Any] = {
    "timeout": 30,
    "retry_attempts": 3,
    "cache_ttl": 300,
    "status": InputStatus.PENDING.value,
}

# Cache keys
INPUT_CACHE_KEY_PREFIX = "input"
INPUT_LIST_CACHE_KEY = "{}:list".format(INPUT_CACHE_KEY_PREFIX)
INPUT_DETAIL_CACHE_KEY = "{}:detail:{}".format(
    INPUT_CACHE_KEY_PREFIX, "{}"
)  # Format with ID

# Rate limiting
INPUT_RATE_LIMIT = {
    "create": {
        "rate": 10,
        "per": 60,  # seconds
    },
    "update": {
        "rate": 20,
        "per": 60,  # seconds
    },
    "delete": {
        "rate": 5,
        "per": 60,  # seconds
    },
}
