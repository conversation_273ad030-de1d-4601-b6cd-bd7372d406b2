"""
Constants related to user
"""
from enum import Enum
from typing import Any, Dict


class UserStatus(str, Enum):
    """Status enum for user"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    ARCHIVED = "archived"
    DELETED = "deleted"


class UserType(str, Enum):
    """Type enum for user"""

    ADMIN = "admin"
    NORMAL = "normal"
    # Add your custom types here


# Constants for user
USER_DEFAULT_LIMIT = 100
USER_MAX_LIMIT = 1000
USER_MIN_NAME_LENGTH = 3
USER_MAX_NAME_LENGTH = 255

# Error messages
USER_NOT_FOUND = "User not found"
USER_ALREADY_EXISTS = "User with this name already exists"
USER_INVALID_STATUS = "Invalid status for user"
USER_PERMISSION_DENIED = "Permission denied for this user"

# Validation rules
USER_VALIDATION_RULES = {
    "name": {
        "min_length": USER_MIN_NAME_LENGTH,
        "max_length": USER_MAX_NAME_LENGTH,
        "required": True,
    },
    "description": {
        "required": False,
    },
    # Add more validation rules as needed
}

# Default values
USER_DEFAULT_CONFIG: Dict[str, Any] = {
    "timeout": 30,
    "retry_attempts": 3,
    "cache_ttl": 300,
    "status": UserStatus.PENDING.value,
}

# Cache keys
USER_CACHE_KEY_PREFIX = "user"
USER_LIST_CACHE_KEY = "{}:list".format(USER_CACHE_KEY_PREFIX)
USER_DETAIL_CACHE_KEY = "{}:detail:{}".format(
    USER_CACHE_KEY_PREFIX, "{}"
)  # Format with ID

# Rate limiting
USER_RATE_LIMIT = {
    "create": {
        "rate": 10,
        "per": 60,  # seconds
    },
    "update": {
        "rate": 20,
        "per": 60,  # seconds
    },
    "delete": {
        "rate": 5,
        "per": 60,  # seconds
    },
}
