"""
Constants related to api key
"""
from datetime import datetime, timedelta


API_PERMISSIONS = [
    {
        "scope": "*",
        "description": "All Permissions",
        "category": "Admin",
    },
    {
        "scope": "read:agents",
        "description": "Read AI agents and configurations",
        "category": "AI Agents",
    },
    {
        "scope": "write:agents",
        "description": "Create and update AI agents",
        "category": "AI Agents",
    },
    {
        "scope": "read:sessions",
        "description": "Read session information",
        "category": "Sessions",
    },
    {
        "scope": "write:sessions",
        "description": "Create and manage sessions",
        "category": "Sessions",
    },
    {
        "scope": "read:models",
        "description": "Read model configurations",
        "category": "Models",
    },
    {
        "scope": "write:models",
        "description": "Create and update model configurations",
        "category": "Models",
    },
    {
        "scope": "read:tools",
        "description": "Read tool configurations",
        "category": "Tools",
    },
    {
        "scope": "write:tools",
        "description": "Create and update tool configurations",
        "category": "Tools",
    },
    {
        "scope": "read:termination_conditions",
        "description": "Read termination condition configurations",
        "category": "Termination Conditions",
    },
    {
        "scope": "write:termination_conditions",
        "description": "Create and update termination condition configurations",
        "category": "Termination Conditions",
    },
    {
        "scope": "read:workflows",
        "description": "Read team configurations",
        "category": "Workflows",
    },
    {
        "scope": "write:workflows",
        "description": "Create and update team configurations",
        "category": "Workflows",
    },
    {
        "scope": "read:inputs",
        "description": "Read input configurations",
        "category": "Inputs",
    },
    {
        "scope": "write:inputs",
        "description": "Create and manage input configurations",
        "category": "Inputs",
    },
    {
        "scope": "read:outputs",
        "description": "Read output configurations",
        "category": "Outputs",
    },
    {
        "scope": "write:outputs",
        "description": "Create and manage outputs",
        "category": "Outputs",
    },
    {
        "scope": "read:folders",
        "description": "Read folder configurations",
        "category": "Folders",
    },
    {
        "scope": "write:folders",
        "description": "Create and manage folders",
        "category": "Folders",
    },
    {
        "scope": "read:files",
        "description": "Read file configurations",
        "category": "Files",
    },
    {
        "scope": "write:files",
        "description": "Create and manage files",
        "category": "Files",
    },
]

API_KEY_RATE_LIMITS = {
    "rate_limit_per_minute": 100,
    "rate_limit_per_hour": 1000,
    "rate_limit_per_day": 10000,
    "expires_at": datetime.now() + timedelta(days=365),
}
