"""
Constants related to team manager
"""
from datetime import datetime
from typing import Any, Dict, List, Literal, Optional, Sequence, Union

from autogen_agentchat.messages import (
    TextMessage,
    BaseChatMessage,
    BaseAgentEvent,
)
from autogen_core import ComponentModel
from autogen_core.models import UserMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient
from pydantic import BaseModel, ConfigDict, field_serializer


class MessageConfig(BaseModel):
    source: str
    content: Union[str, Dict[str, Any], List[Dict[str, Any]], None] = None
    message_type: Optional[str] = "text"


class TaskResult(BaseModel):
    """Result of running a task."""

    messages: Sequence[BaseAgentEvent | BaseChatMessage]
    """Messages produced by the task."""

    stop_reason: str | None = None
    """The reason the task stopped."""

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={
            BaseAgentEvent: lambda e: e.model_dump(),
            BaseChatMessage: lambda m: m.model_dump(),
        },
    )

    @field_serializer("messages")
    def serialize_messages(self, msgs, _info):
        # full model_dump on each message object
        return [m.model_dump() for m in msgs]


class TeamResult(BaseModel):
    task_result: TaskResult
    usage: str
    duration: float

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )


class LLMCallEventMessage(TextMessage):
    source: str = "llm_call_event"

    def to_text(self) -> str:
        return self.content

    def to_model_text(self) -> str:
        return self.content

    def to_model_message(self) -> UserMessage:
        raise NotImplementedError("This message type is not supported.")


class MessageMeta(BaseModel):
    task: Optional[str] = None
    task_result: Optional[TaskResult] = None
    summary_method: Optional[str] = "last"
    files: Optional[List[dict]] = None
    time: Optional[datetime] = None
    log: Optional[List[dict]] = None
    usage: Optional[List[dict]] = None


class EnvironmentVariable(BaseModel):
    name: str
    value: str
    type: Literal["string", "number", "boolean", "secret"] = "string"
    description: Optional[str] = None
    required: bool = False


class UISettings(BaseModel):
    show_llm_call_events: bool = False
    expanded_messages_by_default: bool = True
    show_agent_flow_by_default: bool = True


class SettingsConfig(BaseModel):
    environment: List[EnvironmentVariable] = []
    default_model_client: Optional[ComponentModel] = OpenAIChatCompletionClient(
        model="gpt-4o-mini", api_key="your-api-key"
    ).dump_component()
    ui: UISettings = UISettings()


class Response(BaseModel):
    message: str
    status: bool
    data: Optional[Any] = None


class SocketMessage(BaseModel):
    connection_id: str
    data: Dict[str, Any]
    type: str
