"""
Constants related to tool
"""
from enum import Enum
from typing import Dict, Any


class ToolStatus(str, Enum):
    """Status enum for tool"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    ARCHIVED = "archived"
    DELETED = "deleted"


class ToolType(str, Enum):
    """Type enum for tool"""

    TYPE_A = "type_a"
    TYPE_B = "type_b"
    TYPE_C = "type_c"
    # Add your custom types here


# Constants for tool
TOOL_DEFAULT_LIMIT = 100
TOOL_MAX_LIMIT = 1000
TOOL_MIN_NAME_LENGTH = 3
TOOL_MAX_NAME_LENGTH = 255

# Error messages
TOOL_NOT_FOUND = "Tool not found"
TOOL_ALREADY_EXISTS = "Tool with this name already exists"
TOOL_INVALID_STATUS = "Invalid status for tool"
TOOL_PERMISSION_DENIED = "Permission denied for this tool"

# Validation rules
TOOL_VALIDATION_RULES = {
    "name": {
        "min_length": TOOL_MIN_NAME_LENGTH,
        "max_length": TOOL_MAX_NAME_LENGTH,
        "required": True,
    },
    "description": {
        "required": False,
    },
    # Add more validation rules as needed
}

# Default values
TOOL_DEFAULT_CONFIG: Dict[str, Any] = {
    "timeout": 30,
    "retry_attempts": 3,
    "cache_ttl": 300,
    "status": ToolStatus.PENDING.value,
}

# Cache keys
TOOL_CACHE_KEY_PREFIX = "tool"
TOOL_LIST_CACHE_KEY = "{}:list".format(TOOL_CACHE_KEY_PREFIX)
TOOL_DETAIL_CACHE_KEY = "{}:detail:{}".format(
    TOOL_CACHE_KEY_PREFIX, "{}"
)  # Format with ID

# Rate limiting
TOOL_RATE_LIMIT = {
    "create": {
        "rate": 10,
        "per": 60,  # seconds
    },
    "update": {
        "rate": 20,
        "per": 60,  # seconds
    },
    "delete": {
        "rate": 5,
        "per": 60,  # seconds
    },
}
