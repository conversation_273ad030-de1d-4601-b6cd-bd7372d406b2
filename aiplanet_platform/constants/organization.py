"""
Constants related to organization
"""
from enum import Enum
from typing import Any, Dict


class OrganizationStatus(str, Enum):
    """Status enum for organization"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    ARCHIVED = "archived"
    DELETED = "deleted"


class OrganizationType(str, Enum):
    """Type enum for organization"""

    TYPE_A = "type_a"
    TYPE_B = "type_b"
    TYPE_C = "type_c"
    # Add your custom types here


# Constants for organization
ORGANIZATION_DEFAULT_LIMIT = 100
ORGANIZATION_MAX_LIMIT = 1000
ORGANIZATION_MIN_NAME_LENGTH = 3
ORGANIZATION_MAX_NAME_LENGTH = 255

# Error messages
ORGANIZATION_NOT_FOUND = "Organization not found"
ORGANIZATION_ALREADY_EXISTS = "Organization with this name already exists"
ORGANIZATION_INVALID_STATUS = "Invalid status for organization"
ORGANIZATION_PERMISSION_DENIED = "Permission denied for this organization"

# Validation rules
ORGANIZATION_VALIDATION_RULES = {
    "name": {
        "min_length": ORGANIZATION_MIN_NAME_LENGTH,
        "max_length": ORGANIZATION_MAX_NAME_LENGTH,
        "required": True,
    },
    "description": {
        "required": False,
    },
    # Add more validation rules as needed
}

# Default values
ORGANIZATION_DEFAULT_CONFIG: Dict[str, Any] = {
    "timeout": 30,
    "retry_attempts": 3,
    "cache_ttl": 300,
    "status": OrganizationStatus.PENDING.value,
}

# Cache keys
ORGANIZATION_CACHE_KEY_PREFIX = "organization"
ORGANIZATION_LIST_CACHE_KEY = "{}:list".format(ORGANIZATION_CACHE_KEY_PREFIX)
ORGANIZATION_DETAIL_CACHE_KEY = "{}:detail:{}".format(
    ORGANIZATION_CACHE_KEY_PREFIX, "{}"
)  # Format with ID

# Rate limiting
ORGANIZATION_RATE_LIMIT = {
    "create": {
        "rate": 10,
        "per": 60,  # seconds
    },
    "update": {
        "rate": 20,
        "per": 60,  # seconds
    },
    "delete": {
        "rate": 5,
        "per": 60,  # seconds
    },
}
