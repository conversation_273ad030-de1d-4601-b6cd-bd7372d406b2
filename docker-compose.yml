version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: aiplanet_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aiplanet_platform}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - aiplanet_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-aiplanet_platform}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis (Optional - for caching)
  redis:
    image: redis:7-alpine
    container_name: aiplanet_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - aiplanet_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aiplanet_app
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-aiplanet_platform}
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      
      # API Settings
      PROJECT_NAME: ${PROJECT_NAME:-aiplanet_platform}
      DEBUG: ${DEBUG:-false}
      API_V1_PREFIX: ${API_V1_PREFIX:-/api/v1}
      
      # Security
      SECRET_KEY: ${SECRET_KEY:-fd44c0aa45613a3095fa7f0a975071feae62485dae1c1e935ff3173a3bf35407}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      
      # CORS
      BACKEND_CORS_ORIGINS: '["http://localhost:3000","http://localhost:8000","http://localhost"]'
      
      # Database Pool Settings
      SQL_ECHO: ${SQL_ECHO:-false}
      DB_POOL_SIZE: ${DB_POOL_SIZE:-5}
      DB_MAX_OVERFLOW: ${DB_MAX_OVERFLOW:-10}
      DB_POOL_TIMEOUT: ${DB_POOL_TIMEOUT:-30}
      DB_POOL_RECYCLE: ${DB_POOL_RECYCLE:-1800}
      
      # Cache
      CACHE_TTL: ${CACHE_TTL:-300}
      
      # Logging
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      
      # Rate Limiting
      RATE_LIMIT_PER_MINUTE: ${RATE_LIMIT_PER_MINUTE:-100}
    
    ports:
      - "${APP_PORT:-8000}:8000"
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - aiplanet_network
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Database Migration Service (runs once)
  migration:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aiplanet_migration
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-aiplanet_platform}
    networks:
      - aiplanet_network
    depends_on:
      db:
        condition: service_healthy
    command: ["alembic", "upgrade", "head"]
    profiles:
      - migration

  # PgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aiplanet_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - aiplanet_network
    depends_on:
      - db
    profiles:
      - tools

# Networks
networks:
  aiplanet_network:
    driver: bridge

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local